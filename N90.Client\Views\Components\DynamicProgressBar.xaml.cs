using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using N90.Client.Models;
using N90.Shared.Services;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// DynamicProgressBar.xaml 的交互逻辑
    /// 动态进度条控件，支持不同数据类型的自动配置和温度单位转换
    /// </summary>
    public partial class DynamicProgressBar : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 数据类型
        /// </summary>
        public static readonly DependencyProperty DataTypeProperty =
            DependencyProperty.Register(
                nameof(DataType),
                typeof(ProgressBarDataType),
                typeof(DynamicProgressBar),
                new PropertyMetadata(ProgressBarDataType.CPUTemperature, OnDataTypeChanged));

        /// <summary>
        /// 当前值
        /// </summary>
        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(
                nameof(Value),
                typeof(double),
                typeof(DynamicProgressBar),
                new PropertyMetadata(0.0, OnValueChanged));

        /// <summary>
        /// 显示标签
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(
                nameof(Label),
                typeof(string),
                typeof(DynamicProgressBar),
                new PropertyMetadata("LABEL", OnLabelChanged));

        /// <summary>
        /// 是否使用华氏度（仅对温度类型有效）
        /// </summary>
        public static readonly DependencyProperty UseFahrenheitProperty =
            DependencyProperty.Register(
                nameof(UseFahrenheit),
                typeof(bool),
                typeof(DynamicProgressBar),
                new PropertyMetadata(false, OnUseFahrenheitChanged));

        #endregion

        #region 属性

        /// <summary>
        /// 数据类型
        /// </summary>
        public ProgressBarDataType DataType
        {
            get => (ProgressBarDataType)GetValue(DataTypeProperty);
            set => SetValue(DataTypeProperty, value);
        }

        /// <summary>
        /// 当前值
        /// </summary>
        public double Value
        {
            get => (double)GetValue(ValueProperty);
            set => SetValue(ValueProperty, value);
        }

        /// <summary>
        /// 显示标签
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 是否使用华氏度
        /// </summary>
        public bool UseFahrenheit
        {
            get => (bool)GetValue(UseFahrenheitProperty);
            set => SetValue(UseFahrenheitProperty, value);
        }

        #endregion

        #region 私有字段

        private ProgressBarConfig _config;
        private TemperatureUnitService _temperatureUnitService;

        #endregion

        #region 构造函数

        public DynamicProgressBar()
        {
            InitializeComponent();
            _temperatureUnitService = new TemperatureUnitService();
            _config = ProgressBarConfigManager.GetConfig(DataType);

            // 添加事件处理
            this.SizeChanged += UserControl_SizeChanged;
            this.Loaded += UserControl_Loaded;

            UpdateDisplay();
        }

        #endregion

        #region 依赖属性变化处理

        private static void OnDataTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DynamicProgressBar control)
            {
                control.OnDataTypeChanged();
            }
        }

        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DynamicProgressBar control)
            {
                control.OnValueChanged();
            }
        }

        private static void OnLabelChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DynamicProgressBar control)
            {
                control.OnLabelChanged();
            }
        }

        private static void OnUseFahrenheitChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DynamicProgressBar control)
            {
                control.OnUseFahrenheitChanged();
            }
        }

        #endregion

        #region 私有方法

        private void OnDataTypeChanged()
        {
            _config = ProgressBarConfigManager.GetConfig(DataType);
            UpdateDisplay();
        }

        private void OnValueChanged()
        {
            UpdateProgress();
            UpdateValueText();
        }

        private void OnLabelChanged()
        {
            UpdateLabelText();
        }

        private void OnUseFahrenheitChanged()
        {
            UpdateValueText();
        }

        /// <summary>
        /// 更新整个显示
        /// </summary>
        private void UpdateDisplay()
        {
            UpdateProgress();
            UpdateLabelText();
            UpdateValueText();
        }



        /// <summary>
        /// 更新进度条宽度
        /// </summary>
        private void UpdateProgress()
        {
            try
            {
                if (ProgressBar != null && BackgroundBar != null && _config != null)
                {
                    // 计算进度百分比
                    var percentage = ProgressBarConfigManager.CalculateProgressPercentage(DataType, Value);
                    
                    // 获取背景条的实际宽度
                    var backgroundWidth = BackgroundBar.ActualWidth;
                    if (backgroundWidth <= 0)
                    {
                        // 如果还没有渲染，使用默认宽度或延迟更新
                        backgroundWidth = 200; // 默认宽度
                    }

                    // 计算进度条宽度
                    var progressWidth = backgroundWidth * (percentage / 100.0);
                    ProgressBar.Width = Math.Max(0, progressWidth);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新进度条宽度时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新标签文本
        /// </summary>
        private void UpdateLabelText()
        {
            try
            {
                if (LabelText != null)
                {
                    LabelText.Text = Label;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新标签文本时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新值文本
        /// </summary>
        private void UpdateValueText()
        {
            try
            {
                if (ValueText != null && _config != null)
                {
                    // 检查是否应该使用华氏度
                    bool shouldUseFahrenheit = UseFahrenheit;
                    if (_config.IsTemperature && _temperatureUnitService != null)
                    {
                        shouldUseFahrenheit = _temperatureUnitService.UseFahrenheit;
                    }

                    var displayValue = ProgressBarConfigManager.FormatDisplayValue(DataType, Value, shouldUseFahrenheit);
                    ValueText.Text = displayValue;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新值文本时出错: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置值并更新显示
        /// </summary>
        /// <param name="value">新值</param>
        public void SetValue(double value)
        {
            Value = value;
        }

        /// <summary>
        /// 刷新温度单位设置
        /// </summary>
        public void RefreshTemperatureUnit()
        {
            if (_temperatureUnitService != null)
            {
                _temperatureUnitService.ApplyTemperatureUnitSetting();
                if (_config?.IsTemperature == true)
                {
                    UpdateValueText();
                }
            }
        }

        /// <summary>
        /// 手动触发进度条宽度更新（在容器大小改变时调用）
        /// </summary>
        public void RefreshProgress()
        {
            UpdateProgress();
        }

        #endregion

        #region 事件处理

        private void UserControl_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 当控件大小改变时，重新计算进度条宽度
            UpdateProgress();
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            // 控件加载完成后，确保显示正确
            UpdateDisplay();
        }

        #endregion
    }
}
