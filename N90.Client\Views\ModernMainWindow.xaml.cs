using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;
using N90.Client.Views.Components;
using N90.Client.Views.Pages;
using N90.Client.Services;
using N90.Shared;

namespace N90.Client.Views
{
    public partial class ModernMainWindow : Window
    {
        private NavigationButton? _activeNavButton;
        private SystemInformationPage? _systemInfoPage;
        private N90Page? _n90Page;
        private SettingsPage? _settingsPage;
        private AboutPage? _aboutPage;
        private NotifyIcon? notifyIcon;

        public ModernMainWindow()
        {
            InitializeComponent();

            // Enable window dragging
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();

            // Set initial active button and navigate to default page
            _activeNavButton = SystemInfoButton;
            NavigateToSystemInfo();

            // 订阅硬件数据更新事件
            HardwareDataManager.Instance.HardwareDataUpdated += OnHardwareDataUpdated;

            // 如果已有数据，立即更新
            var lastData = HardwareDataManager.Instance.GetLastKnownData();
            if (lastData != null)
            {
                OnHardwareDataUpdated(lastData);
            }

            // 初始化系统托盘图标
            InitializeNotifyIcon();
        }

        private void NavigateToSystemInfo_Click(object sender, RoutedEventArgs e)
        {
            SetActiveNavButton(SystemInfoButton);
            NavigateToSystemInfo();
        }

        private void NavigateToN90_Click(object sender, RoutedEventArgs e)
        {
            SetActiveNavButton(N90Button);
            NavigateToN90();
        }

        private void NavigateToSettings_Click(object sender, RoutedEventArgs e)
        {
            SetActiveNavButton(SettingsButton);
            NavigateToSettings();
        }

        private void NavigateToAbout_Click(object sender, RoutedEventArgs e)
        {
            SetActiveNavButton(AboutButton);
            NavigateToAbout();
        }

        private void NavigateToSystemInfo()
        {
            if (_systemInfoPage == null)
                _systemInfoPage = new SystemInformationPage();
            ContentFrame.Navigate(_systemInfoPage);
        }

        private void NavigateToN90()
        {
            if (_n90Page == null)
                _n90Page = new N90Page();
            ContentFrame.Navigate(_n90Page);
        }

        private void NavigateToSettings()
        {
            if (_settingsPage == null)
                _settingsPage = new SettingsPage();
            ContentFrame.Navigate(_settingsPage);
        }

        private void NavigateToAbout()
        {
            if (_aboutPage == null)
                _aboutPage = new AboutPage();
            ContentFrame.Navigate(_aboutPage);
        }

        private void SetActiveNavButton(NavigationButton button)
        {
            // Reset previous active button
            if (_activeNavButton != null)
            {
                _activeNavButton.IsSelected = false;
            }

            // Set new active button
            button.IsSelected = true;
            _activeNavButton = button;
        }

        /// <summary>
        /// 更新N90页面的硬件数据
        /// </summary>
        /// <param name="data">硬件数据</param>
        public void UpdateN90PageData(HardwareData data)
        {
            if (_n90Page != null)
            {
                _n90Page.UpdateHardwareData(data);
            }
        }

        /// <summary>
        /// 硬件数据更新事件处理
        /// </summary>
        /// <param name="data">更新的硬件数据</param>
        private void OnHardwareDataUpdated(HardwareData data)
        {
            // 确保在UI线程上执行
            Dispatcher.Invoke(() =>
            {
                // 更新N90页面数据
                UpdateN90PageData(data);
            });
        }

        protected override void OnClosed(EventArgs e)
        {
            // 取消订阅事件
            HardwareDataManager.Instance.HardwareDataUpdated -= OnHardwareDataUpdated;
            
            // 清理系统托盘图标
            notifyIcon?.Dispose();
            
            base.OnClosed(e);
        }

        protected override void OnStateChanged(EventArgs e)
        {
            try
            {
                if (WindowState == WindowState.Minimized && notifyIcon != null)
                {
                    this.Hide();
                    this.ShowInTaskbar = false;
                }
                base.OnStateChanged(e);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during state change: {ex.Message}");
                base.OnStateChanged(e);
            }
        }

        //region System Tray (NotifyIcon) - 参考旧UI实现

        private void InitializeNotifyIcon()
        {
            try
            {
                notifyIcon = new NotifyIcon();

                // Try to load custom icon, fallback to system icon
                try
                {
                    string iconPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "favicon.ico");
                    if (System.IO.File.Exists(iconPath))
                    {
                        notifyIcon.Icon = new System.Drawing.Icon(iconPath);
                    }
                    else
                    {
                        notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                    }
                }
                catch
                {
                    notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                }

                notifyIcon.Text = "N90 Hardware Monitor";
                notifyIcon.Visible = true;

                // Create context menu
                var contextMenu = new ContextMenuStrip();

                // Show/Hide menu
                var showMenu = new ToolStripMenuItem("显示界面", null, (s, e) => ShowWindow());

                // Exit menu
                var exitMenu = new ToolStripMenuItem("退出", null, (s, e) => ExitApplication());

                // Add items to context menu
                contextMenu.Items.Add(showMenu);
                contextMenu.Items.Add(new ToolStripSeparator());
                contextMenu.Items.Add(exitMenu);

                notifyIcon.ContextMenuStrip = contextMenu;

                // Double click to show window
                notifyIcon.DoubleClick += (s, e) => ShowWindow();

                this.Closing += ModernMainWindow_Closing;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"NotifyIcon initialization failed: {ex.Message}");
                // Continue without NotifyIcon if it fails
            }
        }

        private void ModernMainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // Hide to system tray instead of closing
                if (notifyIcon != null)
                {
                    e.Cancel = true;
                    this.Hide();
                    this.ShowInTaskbar = false;
                }
                else
                {
                    // If no NotifyIcon, allow normal close
                    ExitApplication();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during window closing: {ex.Message}");
            }
        }

        private void ShowWindow()
        {
            this.Show();
            this.WindowState = WindowState.Normal;
            this.ShowInTaskbar = true;
            this.Activate();
        }

        private void ExitApplication()
        {
            notifyIcon?.Dispose();

            // Always stop service when client exits
            StopServiceOnExit();

            System.Windows.Application.Current.Shutdown();
        }

        #region Service Management - 从旧UI复制

        private void StopServiceOnExit()
        {
            try
            {
                Console.WriteLine("Stopping all N90 services...");

                // 1. Stop Windows Service if it's running
                if (IsWindowsServiceRunning())
                {
                    Console.WriteLine("Stopping Windows Service...");
                    StopWindowsService();
                }

                // 2. Stop any console service processes
                var serviceProcesses = System.Diagnostics.Process.GetProcessesByName("N90.Service");

                foreach (var process in serviceProcesses)
                {
                    try
                    {
                        Console.WriteLine($"Stopping service process: {process.Id}");
                        process.CloseMainWindow();

                        // Wait a bit for graceful shutdown
                        if (!process.WaitForExit(3000))
                        {
                            Console.WriteLine($"Force killing service process: {process.Id}");
                            process.Kill();
                        }
                        else
                        {
                            Console.WriteLine($"Service process {process.Id} stopped gracefully");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to stop service process {process.Id}: {ex.Message}");
                    }
                }

                Console.WriteLine("All N90 services stopped.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop services: {ex.Message}");
            }
        }

        private bool IsWindowsServiceRunning()
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "query \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null) return false;

                process.WaitForExit();

                if (process.ExitCode == 0)
                {
                    string output = process.StandardOutput.ReadToEnd();
                    return output.Contains("RUNNING");
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check Windows service status: {ex.Message}");
                return false;
            }
        }

        private void StopWindowsService()
        {
            try
            {
                // Try to stop Windows Service with current permissions
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "stop \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        Console.WriteLine("Windows Service stopped successfully");
                    }
                    else
                    {
                        Console.WriteLine($"Failed to stop Windows Service (exit code: {process.ExitCode})");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop Windows Service: {ex.Message}");
            }
        }

        #endregion

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // 触发关闭事件，如果系统托盘图标存在，会隐藏到托盘而不是真正关闭
            this.Close();
        }

        private void TextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {

        }
    }
}