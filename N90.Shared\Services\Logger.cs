using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace N90.Shared.Services
{
    /// <summary>
    /// 优化的日志工具类，支持文件大小限制、滚动日志和异步写入
    /// </summary>
    public class Logger : IDisposable
    {
        private static readonly Lazy<Logger> _instance = new Lazy<Logger>(() => new Logger());
        public static Logger Instance => _instance.Value;

        private readonly ConcurrentQueue<LogEntry> _logQueue;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _logWriterTask;
        private readonly object _fileLock = new object();
        
        // 配置参数
        private const long MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
        private const int MAX_LOG_FILES = 5; // 保留最多5个日志文件
        private const int LOG_QUEUE_CAPACITY = 1000; // 日志队列容量
        private const int WRITE_DELAY_MS = 100; // 批量写入延迟
        
        private readonly string _logDirectory;
        private readonly string _logFilePrefix;
        private string _currentLogFile;
        private bool _disposed = false;

        private Logger()
        {
            _logQueue = new ConcurrentQueue<LogEntry>();
            _cancellationTokenSource = new CancellationTokenSource();

            // 使用统一的路径策略设置日志目录
            _logDirectory = GetLogDirectory();
            _logFilePrefix = "N90";

            Console.WriteLine($"[Logger] Initializing with directory: {_logDirectory}");

            // 确保日志目录存在
            EnsureLogDirectoryExists();

            // 初始化当前日志文件
            _currentLogFile = GetCurrentLogFileName();
            //Console.WriteLine($"[Logger] Current log file: {_currentLogFile}");

            // 启动异步日志写入任务
            _logWriterTask = Task.Run(LogWriterLoop, _cancellationTokenSource.Token);
            //Console.WriteLine($"[Logger] Logger initialized successfully");
        }

        /// <summary>
        /// 根据运行环境确定日志目录路径
        /// Debug模式：项目根目录下的logs文件夹
        /// Release模式：可执行文件目录下的logs文件夹
        /// </summary>
        /// <returns>日志目录的完整路径</returns>
        private static string GetLogDirectory()
        {
            const string logFolderName = "logs";

#if DEBUG
            // Debug模式：保存到项目根目录下的logs文件夹
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, logFolderName);
#else
            // Release模式：保存到可执行文件目录下的logs文件夹
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, logFolderName);
#endif
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInfo(string message)
        {
            Instance.Log(LogLevel.Info, message);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public static void LogDebug(string message)
        {
#if DEBUG
            Instance.Log(LogLevel.Debug, message);
#endif
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void LogWarning(string message)
        {
            Instance.Log(LogLevel.Warning, message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message)
        {
            Instance.Log(LogLevel.Error, message);
        }

        /// <summary>
        /// 记录异常日志
        /// </summary>
        public static void LogError(Exception exception, string message = null)
        {
            var logMessage = string.IsNullOrEmpty(message) 
                ? exception.ToString() 
                : $"{message}\n{exception}";
            Instance.Log(LogLevel.Error, logMessage);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        private void Log(LogLevel level, string message)
        {
            if (_disposed)
            {
                Console.WriteLine($"[Logger] Attempted to log after disposal: {message}");
                return;
            }

            var logEntry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            // 如果队列满了，丢弃最旧的日志
            if (_logQueue.Count >= LOG_QUEUE_CAPACITY)
            {
                _logQueue.TryDequeue(out _);
                Console.WriteLine($"[Logger] Queue full, dropping oldest entry");
            }

            _logQueue.Enqueue(logEntry);

            // 诊断输出
            Console.WriteLine($"[Logger] Enqueued: [{level}] {message} (Queue size: {_logQueue.Count})");
        }

        /// <summary>
        /// 异步日志写入循环
        /// </summary>
        private async Task LogWriterLoop()
        {
            var logEntries = new List<LogEntry>();
            
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // 批量收集日志条目
                    logEntries.Clear();
                    while (_logQueue.TryDequeue(out var entry) && logEntries.Count < 50)
                    {
                        logEntries.Add(entry);
                    }

                    if (logEntries.Count > 0)
                    {
                        await WriteLogEntries(logEntries);
                    }

                    // 等待一段时间再处理下一批
                    await Task.Delay(WRITE_DELAY_MS, _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    // 日志系统内部错误，输出到控制台
                    Console.WriteLine($"Logger internal error: {ex.Message}");
                }
            }

            // 处理剩余的日志条目
            logEntries.Clear();
            while (_logQueue.TryDequeue(out var entry))
            {
                logEntries.Add(entry);
            }
            
            if (logEntries.Count > 0)
            {
                await WriteLogEntries(logEntries);
            }
        }

        /// <summary>
        /// 写入日志条目到文件
        /// </summary>
        private async Task WriteLogEntries(List<LogEntry> entries)
        {
            //Console.WriteLine($"[Logger] Attempting to write {entries.Count} entries to: {_currentLogFile}");

            lock (_fileLock)
            {
                try
                {
                    // 检查当前日志文件大小
                    if (File.Exists(_currentLogFile))
                    {
                        var fileInfo = new FileInfo(_currentLogFile);
                        Console.WriteLine($"[Logger] Current log file size: {fileInfo.Length} bytes");
                        if (fileInfo.Length >= MAX_FILE_SIZE)
                        {
                            Console.WriteLine($"[Logger] File size exceeded, rotating log");
                            RotateLogFile();
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[Logger] Log file does not exist, will create: {_currentLogFile}");
                    }

                    // 写入日志条目
                    using (var writer = new StreamWriter(_currentLogFile, true))
                    {
                        foreach (var entry in entries)
                        {
                            var logLine = FormatLogEntry(entry);
                            writer.WriteLine(logLine);
                            Console.WriteLine(logLine);
                        }
                        writer.Flush(); // 确保立即写入
                    }

                    Console.WriteLine($"[Logger] Successfully wrote {entries.Count} entries");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[Logger] Failed to write log entries: {ex.Message}");
                    Console.WriteLine($"[Logger] Stack trace: {ex.StackTrace}");
                }
            }
        }

        /// <summary>
        /// 格式化日志条目
        /// </summary>
        private string FormatLogEntry(LogEntry entry)
        {
            return $"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{entry.Level}] [T{entry.ThreadId:D2}] {entry.Message}";
        }

        /// <summary>
        /// 轮转日志文件
        /// </summary>
        private void RotateLogFile()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var rotatedFileName = Path.Combine(_logDirectory, $"{_logFilePrefix}_{timestamp}.log");
                
                if (File.Exists(_currentLogFile))
                {
                    File.Move(_currentLogFile, rotatedFileName);
                }

                _currentLogFile = GetCurrentLogFileName();
                
                // 清理旧日志文件
                CleanupOldLogFiles();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to rotate log file: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理旧日志文件
        /// </summary>
        private void CleanupOldLogFiles()
        {
            try
            {
                var logFiles = Directory.GetFiles(_logDirectory, $"{_logFilePrefix}_*.log");
                Array.Sort(logFiles);

                // 保留最新的文件，删除多余的
                if (logFiles.Length > MAX_LOG_FILES)
                {
                    for (int i = 0; i < logFiles.Length - MAX_LOG_FILES; i++)
                    {
                        File.Delete(logFiles[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to cleanup old log files: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前日志文件名
        /// </summary>
        private string GetCurrentLogFileName()
        {
            return Path.Combine(_logDirectory, $"{_logFilePrefix}_current.log");
        }

        /// <summary>
        /// 确保日志目录存在
        /// </summary>
        private void EnsureLogDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                {
                    Console.WriteLine($"[Logger] Creating log directory: {_logDirectory}");
                    Directory.CreateDirectory(_logDirectory);
                    Console.WriteLine($"[Logger] Log directory created successfully");
                }
                else
                {
                    Console.WriteLine($"[Logger] Log directory already exists: {_logDirectory}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Logger] Failed to create log directory: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _cancellationTokenSource.Cancel();
            
            try
            {
                _logWriterTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error waiting for log writer task: {ex.Message}");
            }

            _cancellationTokenSource?.Dispose();
        }
    }

    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }

    /// <summary>
    /// 日志条目
    /// </summary>
    internal class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public int ThreadId { get; set; }
    }
}
