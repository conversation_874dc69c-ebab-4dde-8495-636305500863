using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using N90.Shared.Services;

namespace N90.Service.Models
{
    /// <summary>
    /// Service端的应用程序设置配置类
    /// </summary>
    public class ServiceAppSettings
    {
        // 应用程序设置
        public bool AutoLaunch { get; set; } = true;
        public bool DisableUpdating { get; set; } = false;
        public bool ShowMessageBox { get; set; } = false;
        public bool Fahrenheit { get; set; } = false;
        public bool CompatibilityMode { get; set; } = false;
        public int DelayedStart { get; set; } = 12;

        // 硬件检测设置
        public bool AutoDetectCpu { get; set; } = true;
        public bool AutoDetectGpu { get; set; } = true;
        public bool AutoDetectMemory { get; set; } = true;

        // 传感器选择设置
        [JsonPropertyName("selectedNetworkInterface")]
        public string SelectedNetworkInterface { get; set; } = string.Empty;

        [JsonPropertyName("selectedGpuSensor")]
        public string SelectedGpuSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuTempSensor")]
        public string SelectedCpuTempSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuVoltageSensor")]
        public string SelectedCpuVoltageSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuUsageSensor")]
        public string SelectedCpuUsageSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuFanSensor")]
        public string SelectedCpuFanSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedPumpFanSensor")]
        public string SelectedPumpFanSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCaseFan1Sensor")]
        public string SelectedCaseFan1Sensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCaseFan2Sensor")]
        public string SelectedCaseFan2Sensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedHddSensor")]
        public string SelectedHddSensor { get; set; } = string.Empty;

        // 时间戳
        public DateTime LastSaved { get; set; } = DateTime.Now;

        private static readonly string SettingsPath = GetSettingsFilePath();

        /// <summary>
        /// 根据运行环境确定设置文件路径
        /// Debug模式：使用绝对路径到项目根目录
        /// Release模式：使用相对路径（与可执行文件同目录）
        /// </summary>
        /// <returns>设置文件的完整路径</returns>
        private static string GetSettingsFilePath()
        {
            const string fileName = "setting.json";

#if DEBUG
            // Debug模式：保存到项目根目录
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：保存到可执行文件同目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        /// <summary>
        /// 获取设置文件路径（用于调试）
        /// </summary>
        /// <returns>设置文件路径</returns>
        public static string GetSettingsPath()
        {
            return SettingsPath;
        }

        /// <summary>
        /// 从JSON文件加载设置
        /// </summary>
        /// <returns>应用设置对象</returns>
        public static ServiceAppSettings Load()
        {
            try
            {
                Logger.LogInfo($"Loading ServiceAppSettings from: {SettingsPath}");
                Logger.LogInfo($"File exists: {File.Exists(SettingsPath)}");

                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    Logger.LogInfo($"JSON content length: {json.Length}");

                    // 使用与Client相同的JsonSerializerOptions
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var settings = JsonSerializer.Deserialize<ServiceAppSettings>(json, options);
                    if (settings != null)
                    {
                        Logger.LogInfo($"Successfully loaded settings - GPU: '{settings.SelectedGpuSensor}', CPU Temp: '{settings.SelectedCpuTempSensor}'");
                        return settings;
                    }
                    else
                    {
                        Logger.LogInfo("Deserialized settings is null");
                    }
                }
                else
                {
                    Logger.LogInfo("Settings file does not exist, returning default settings");
                }
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error loading app settings: {ex.Message}");
            }

            Logger.LogInfo("Returning new default ServiceAppSettings");
            return new ServiceAppSettings();
        }

        /// <summary>
        /// 获取所有传感器配置的字典
        /// </summary>
        /// <returns>传感器配置字典</returns>
        public Dictionary<string, string> GetSensorSettings()
        {
            var sensorSettings = new Dictionary<string, string>();

            if (!string.IsNullOrEmpty(SelectedNetworkInterface))
                sensorSettings["selectedNetworkInterface"] = SelectedNetworkInterface;
            if (!string.IsNullOrEmpty(SelectedGpuSensor))
                sensorSettings["selectedGpuSensor"] = SelectedGpuSensor;
            if (!string.IsNullOrEmpty(SelectedCpuTempSensor))
                sensorSettings["selectedCpuTempSensor"] = SelectedCpuTempSensor;
            if (!string.IsNullOrEmpty(SelectedCpuVoltageSensor))
                sensorSettings["selectedCpuVoltageSensor"] = SelectedCpuVoltageSensor;
            if (!string.IsNullOrEmpty(SelectedCpuUsageSensor))
                sensorSettings["selectedCpuUsageSensor"] = SelectedCpuUsageSensor;
            if (!string.IsNullOrEmpty(SelectedCpuFanSensor))
                sensorSettings["selectedCpuFanSensor"] = SelectedCpuFanSensor;
            if (!string.IsNullOrEmpty(SelectedPumpFanSensor))
                sensorSettings["selectedPumpFanSensor"] = SelectedPumpFanSensor;
            if (!string.IsNullOrEmpty(SelectedCaseFan1Sensor))
                sensorSettings["selectedCaseFan1Sensor"] = SelectedCaseFan1Sensor;
            if (!string.IsNullOrEmpty(SelectedCaseFan2Sensor))
                sensorSettings["selectedCaseFan2Sensor"] = SelectedCaseFan2Sensor;

            return sensorSettings;
        }
    }
}
