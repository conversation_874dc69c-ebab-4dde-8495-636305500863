using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using N90.Shared;
using N90.Shared.Services;
using N90.Client.Views.Components;
using N90.Client.Services;
using N90.Client.Models;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace N90.Client.Views.Pages
{
    public partial class SettingsPage : Page
    {
        // 头像更新事件
        public static event EventHandler? AvatarUpdated;
        private readonly TemperatureUnitService _temperatureUnitService;
        private readonly StartupService _startupService;
        private readonly HardwareMonitorService _monitorService;
        private AppSettings _appSettings; // 使用AppSettings替代MonitorSettings
        private bool _sensorsUILoaded = false;
        private bool _isLoadingSettings = false; // 防止在加载设置时触发保存
        private readonly string _avatarFolder;
        private readonly string _avatarFilePath;

        public SettingsPage()
        {
            InitializeComponent();

            _temperatureUnitService = new TemperatureUnitService();
            _startupService = new StartupService("N90HardwareMonitor");
            _monitorService = HardwareMonitorService.Instance; // 使用单例
            _appSettings = AppSettings.Load(); // 在构造函数中加载设置

            // 设置头像文件夹路径
            _avatarFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "N90HardwareMonitor", "Avatars");
            _avatarFilePath = Path.Combine(_avatarFolder, "user_avatar.png");

            InitializeCustomComboBoxes();
            // 不在构造函数中加载设置，等待Loaded事件

            // 每次都重新加载设置，确保显示最新的保存数据
            Loaded += (sender, e) => LoadPageSettings();
            Loaded += (sender, e) => LoadAvatar();
        }

        /// <summary>
        /// 加载用户头像 - 使用文件流避免文件锁定
        /// </summary>
        private void LoadAvatar()
        {
            try
            {
                if (File.Exists(_avatarFilePath))
                {
                    using (var stream = new FileStream(_avatarFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    {
                        var bitmap = new System.Windows.Media.Imaging.BitmapImage();
                        bitmap.BeginInit();
                        bitmap.StreamSource = stream;
                        bitmap.CacheOption = System.Windows.Media.Imaging.BitmapCacheOption.OnLoad;
                        bitmap.EndInit();
                        bitmap.Freeze(); // 使图片跨线程安全
                        AvatarImage.Source = bitmap;
                    }
                }
                else
                {
                    // 如果没有头像文件，使用默认头像
                    var bitmap = new System.Windows.Media.Imaging.BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri("pack://application:,,,/Resources/Images/img12.png", UriKind.Absolute);
                    bitmap.CacheOption = System.Windows.Media.Imaging.BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze();
                    AvatarImage.Source = bitmap;
                }
            }
            catch (Exception ex)
            {
                // 如果头像加载失败，使用默认头像
                var bitmap = new System.Windows.Media.Imaging.BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri("pack://application:,,,/Resources/Images/img12.png", UriKind.Absolute);
                bitmap.CacheOption = System.Windows.Media.Imaging.BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                bitmap.Freeze();
                AvatarImage.Source = bitmap;
            }
        }

        /// <summary>
        /// 上传头像按钮点击事件
        /// </summary>
        private void UploadAvatar_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择头像图片",
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp|所有文件|*.*",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyPictures)
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    UploadAvatar(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择头像文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 上传头像到本地
        /// </summary>
        private void UploadAvatar(string sourceFilePath)
        {
            try
            {
                // 确保头像文件夹存在
                if (!Directory.Exists(_avatarFolder))
                {
                    Directory.CreateDirectory(_avatarFolder);
                }

                // 检查文件大小（限制为5MB）
                var fileInfo = new FileInfo(sourceFilePath);
                if (fileInfo.Length > 5 * 1024 * 1024) // 5MB
                {
                    MessageBox.Show("头像文件大小不能超过5MB", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 复制文件到头像文件夹
                File.Copy(sourceFilePath, _avatarFilePath, true);

                // 重新加载头像 - 强制刷新避免缓存
                LoadAvatar();

                // 触发头像更新事件
                AvatarUpdated?.Invoke(this, EventArgs.Empty);

                MessageBox.Show("头像上传成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"上传头像失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除头像按钮点击事件
        /// </summary>
        private void DeleteAvatar_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要删除当前头像吗？", "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    DeleteAvatar();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除头像时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除头像并恢复默认
        /// </summary>
        private void DeleteAvatar()
        {
            try
            {
                if (File.Exists(_avatarFilePath))
                {
                    File.Delete(_avatarFilePath);
                }

                // 恢复默认头像
                AvatarImage.Source = new System.Windows.Media.Imaging.BitmapImage(new Uri("/Resources/Images/img12.png", UriKind.Relative));

                // 触发头像更新事件
                AvatarUpdated?.Invoke(this, EventArgs.Empty);

                MessageBox.Show("头像已成功删除！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除头像失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPageSettings()
        {
            try
            {
                // 重新加载设置文件，确保获取最新的保存数据
                _appSettings = AppSettings.Load();

                // 重新加载当前设置，包括温度单位等
                LoadCurrentSettings();

                // 如果硬件服务已经完全准备就绪，直接加载UI
                if (_monitorService.IsReady)
                {
                    LoadSensorUIFromCache();
                }
                else
                {
                    // 异步加载传感器设置，避免阻塞UI
                    _ = LoadMonitorSettingsAsync();
                }
            }
            catch (Exception ex)
            {
                // Error loading page settings
            }
        }

        private void InitializeCustomComboBoxes()
        {
            // 事件绑定已在XAML中完成，通过CustomComboBox_SelectionChanged统一处理
            // 移除重复的代码绑定，避免双重事件触发
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // Load startup setting (保持系统级设置)
                AutoLaunchIndicator.IsChecked = StartupService.IsCompleteAutoStartSet();

                // Load other settings from JSON file (包括温度单位)
                LoadApplicationSettings();
            }
            catch (Exception ex)
            {
                // Error loading settings
            }
        }

        private void LoadApplicationSettings()
        {
            try
            {
                // 从JSON文件加载所有应用设置
                //DisableUpdatingIndicator.IsChecked = _appSettings.DisableUpdating;
                //ShowMessageBoxIndicator.IsChecked = _appSettings.ShowMessageBox;
                //CompatibilityModeIndicator.IsChecked = _appSettings.CompatibilityMode;
                DelayedStartTextBox.Text = _appSettings.DelayedStart.ToString();

                // 硬件检测设置
                //AutoDetectCpuIndicator.IsChecked = _appSettings.AutoDetectCpu;
                //AutoDetectGpuIndicator.IsChecked = _appSettings.AutoDetectGpu;
                //AutoDetectMemoryIndicator.IsChecked = _appSettings.AutoDetectMemory;

                // 温度单位设置（从JSON读取）
                FahrenheitIndicator.IsChecked = _appSettings.Fahrenheit;

                // 同步更新TemperatureUnitService
                _temperatureUnitService.UseFahrenheit = _appSettings.Fahrenheit;
            }
            catch (Exception ex)
            {
                // Error loading application settings
            }
        }

        private void StatusIndicator_StatusChanged(object sender, StatusChangedEventArgs e)
        {
            try
            {
                if (sender is StatusIndicator indicator)
                {
                    var dataType = e.DataType;
                    var isChecked = e.IsChecked;

                    // 根据不同的DataType处理不同的逻辑
                    switch (dataType)
                    {
                        case "AutoLaunch":
                            HandleAutoLaunchChanged(isChecked);
                            break;
                        case "DisableUpdating":
                            HandleDisableUpdatingChanged(isChecked);
                            break;
                        case "ShowMessageBox":
                            HandleShowMessageBoxChanged(isChecked);
                            break;
                        case "Fahrenheit":
                            HandleFahrenheitChanged(isChecked);
                            break;
                        case "CompatibilityMode":
                            HandleCompatibilityModeChanged(isChecked);
                            break;
                        case "AutoDetectCpu":
                            HandleAutoDetectCpuChanged(isChecked);
                            break;
                        case "AutoDetectGpu":
                            HandleAutoDetectGpuChanged(isChecked);
                            break;
                        case "AutoDetectMemory":
                            HandleAutoDetectMemoryChanged(isChecked);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                // Error handling StatusIndicator change
            }
        }

        private void HandleAutoLaunchChanged(bool isChecked)
        {
            // 处理自动启动设置变化
        }

        private void HandleDisableUpdatingChanged(bool isChecked)
        {
            // 处理禁用更新设置变化
        }

        private void HandleShowMessageBoxChanged(bool isChecked)
        {
            // 处理启动消息框设置变化
        }

        private void HandleFahrenheitChanged(bool isChecked)
        {
            // 处理温度单位设置变化
            _temperatureUnitService.UseFahrenheit = isChecked;
            _temperatureUnitService.SaveTemperatureUnitSetting();
        }

        private void HandleCompatibilityModeChanged(bool isChecked)
        {
            // 处理兼容模式设置变化
        }

        private void HandleAutoDetectCpuChanged(bool isChecked)
        {
            // 处理CPU自动检测设置变化
        }

        private void HandleAutoDetectGpuChanged(bool isChecked)
        {
            // 处理GPU自动检测设置变化
        }

        private void HandleAutoDetectMemoryChanged(bool isChecked)
        {
            // 处理内存自动检测设置变化
        }

        private void CustomComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is CustomComboBox customComboBox)
                {
                    var selectedItem = customComboBox.SelectedItem;
                    var comboBoxName = customComboBox.Name;

                    // 根据不同的ComboBox处理不同的逻辑
                    switch (comboBoxName)
                    {
                        case "NetworkInterfaceComboBox":
                            HandleNetworkInterfaceChanged(selectedItem);
                            break;
                        case "CpuTempSensorComboBox":
                            HandleCpuTempSensorChanged(selectedItem);
                            break;
                        case "CpuVoltageComboBox":
                            HandleCpuVoltageChanged(selectedItem);
                            break;
                        case "CpuUsageComboBox":
                            HandleCpuUsageChanged(selectedItem);
                            break;
                        case "GpuComboBox":
                            HandleGpuChanged(selectedItem);
                            break;
                        case "CpuFanComboBox":
                            HandleCpuFanChanged(selectedItem);
                            break;
                        case "PumpFanComboBox":
                            HandlePumpFanChanged(selectedItem);
                            break;
                        case "CaseFan1ComboBox":
                            HandleCaseFan1Changed(selectedItem);
                            break;
                        case "CaseFan2ComboBox":
                            HandleCaseFan2Changed(selectedItem);
                            break;
                        case "HddComboBox":
                            HandleHddChanged(selectedItem);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                // Error handling CustomComboBox selection change
            }
        }

        private async void HandleNetworkInterfaceChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedNetworkInterface = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCpuTempSensorChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCpuTempSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCpuVoltageChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCpuVoltageSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCpuUsageChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCpuUsageSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleGpuChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedGpuSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCpuFanChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCpuFanSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandlePumpFanChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedPumpFanSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCaseFan1Changed(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCaseFan1Sensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleCaseFan2Changed(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedCaseFan2Sensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }

        private async void HandleHddChanged(object selectedItem)
        {
            // 如果正在加载设置，不要触发任何操作
            if (_isLoadingSettings) return;

            // 只更新内存中的设置，不保存到文件
            if (selectedItem is ComboBoxItem item && item.Tag != null)
            {
                _appSettings.SelectedHddSensor = item.Tag.ToString();

                // 实时发送传感器配置到Service
                await SendSelectedSensorsToService();
            }
        }



        /// <summary>
        /// 填充所有风扇选项框，使用相同的主板风扇列表
        /// </summary>
        private void PopulateFanComboBoxes(List<string> motherboardFans)
        {
            if (motherboardFans.Count == 0)
            {
                // 如果没有找到主板风扇，添加空选项
                CpuFanComboBox.AddItem("无可用风扇", "", true);
                PumpFanComboBox.AddItem("无可用风扇", "", true);
                CaseFan1ComboBox.AddItem("无可用风扇", "", true);
                CaseFan2ComboBox.AddItem("无可用风扇", "", true);
                return;
            }

            // 为所有风扇选项框添加相同的选项列表，不自动选择任何项
            foreach (var fanInfo in motherboardFans)
            {
                var fanData = ExtractFanInfo(fanInfo);

                CpuFanComboBox.AddItem(fanData.DisplayName, fanData.Key, false);
                PumpFanComboBox.AddItem(fanData.DisplayName, fanData.Key, false);
                CaseFan1ComboBox.AddItem(fanData.DisplayName, fanData.Key, false);
                CaseFan2ComboBox.AddItem(fanData.DisplayName, fanData.Key, false);
            }

            // 不再自动选择第一个选项，让LoadSavedSensorSettingsWithFallback处理选择逻辑
        }

        /// <summary>
        /// 从风扇信息字符串中提取显示名称和键值
        /// </summary>
        private (string DisplayName, string Key) ExtractFanInfo(string fanInfo)
        {
            if (string.IsNullOrEmpty(fanInfo))
                return ("", "");

            var parts = fanInfo.Split('|');
            if (parts.Length >= 2)
            {
                return (parts[0], parts[1]);
            }
            return (fanInfo, fanInfo);
        }

        /// <summary>
        /// 从传感器显示名称中提取传感器键值
        /// 显示名称格式: "硬件名称 - 传感器名称"
        /// 传感器键值格式: "硬件名称_传感器类型_传感器名称"
        /// </summary>
        private string ExtractSensorKey(string displayName, string sensorType)
        {
            if (string.IsNullOrEmpty(displayName))
                return "";

            // 显示名称格式: "Intel Core i9-10900K - CPU Package"
            // 需要转换为键值格式: "Intel Core i9-10900K_Temperature_CPU Package"
            var parts = displayName.Split(" - ");
            if (parts.Length >= 2)
            {
                string hardwareName = parts[0];
                string sensorName = parts[1];
                return $"{hardwareName}_{sensorType}_{sensorName}";
            }
            return displayName;
        }

        /// <summary>
        /// 加载已保存的传感器设置
        /// </summary>
        private void LoadSavedSensorSettings()
        {
            try
            {
                // 设置标志，防止在程序化设置选择时触发保存
                _isLoadingSettings = true;

                try
                {
                    // 设置已保存的传感器选择
                    SetComboBoxSelection(NetworkInterfaceComboBox, _appSettings.SelectedNetworkInterface);
                    SetComboBoxSelection(GpuComboBox, _appSettings.SelectedGpuSensor);
                    SetComboBoxSelection(CpuTempSensorComboBox, _appSettings.SelectedCpuTempSensor);
                    SetComboBoxSelection(CpuVoltageComboBox, _appSettings.SelectedCpuVoltageSensor);
                    SetComboBoxSelection(CpuUsageComboBox, _appSettings.SelectedCpuUsageSensor);

                    // 设置已保存的风扇选择
                    SetComboBoxSelection(CpuFanComboBox, _appSettings.SelectedCpuFanSensor);
                    SetComboBoxSelection(PumpFanComboBox, _appSettings.SelectedPumpFanSensor);
                    SetComboBoxSelection(CaseFan1ComboBox, _appSettings.SelectedCaseFan1Sensor);
                    SetComboBoxSelection(CaseFan2ComboBox, _appSettings.SelectedCaseFan2Sensor);
                    SetComboBoxSelection(HddComboBox, _appSettings.SelectedHddSensor);
                }
                finally
                {
                    // 重置标志，允许用户操作触发保存
                    _isLoadingSettings = false;
                }
            }
            catch (Exception ex)
            {
                _isLoadingSettings = false; // 确保在异常情况下也重置标志
            }
        }

        /// <summary>
        /// 根据传感器键值设置选项框的选择，如果找不到则返回false
        /// </summary>
        private bool SetComboBoxSelection(CustomComboBox comboBox, string sensorKey)
        {
            if (string.IsNullOrEmpty(sensorKey) || comboBox.InternalItems.Count == 0)
            {
                return false;
            }

            bool found = false;
            foreach (ComboBoxItem item in comboBox.InternalItems)
            {
                if (item.Tag?.ToString() == sensorKey)
                {
                    comboBox.SelectedItem = item;
                    found = true;
                    break;
                }
            }

            return found;
        }

        /// <summary>
        /// 加载已保存的传感器设置，如果配置中的传感器不存在则选择第一项作为默认值
        /// </summary>
        private void LoadSavedSensorSettingsWithFallback()
        {
            try
            {
                // 设置标志，防止在程序化设置选择时触发保存
                _isLoadingSettings = true;

                try
                {
                    // 应用智能选择逻辑：优先使用配置中的传感器，如果不存在则选择第一项
                    ApplySmartSensorSelection(NetworkInterfaceComboBox, _appSettings.SelectedNetworkInterface, "网络接口");
                    ApplySmartSensorSelection(GpuComboBox, _appSettings.SelectedGpuSensor, "GPU");
                    ApplySmartSensorSelection(CpuTempSensorComboBox, _appSettings.SelectedCpuTempSensor, "CPU温度传感器");
                    ApplySmartSensorSelection(CpuVoltageComboBox, _appSettings.SelectedCpuVoltageSensor, "CPU电压传感器");
                    ApplySmartSensorSelection(CpuUsageComboBox, _appSettings.SelectedCpuUsageSensor, "CPU使用率传感器");

                    // 应用风扇传感器的智能选择逻辑
                    ApplySmartSensorSelection(CpuFanComboBox, _appSettings.SelectedCpuFanSensor, "CPU风扇");
                    ApplySmartSensorSelection(PumpFanComboBox, _appSettings.SelectedPumpFanSensor, "水泵风扇");
                    ApplySmartSensorSelection(CaseFan1ComboBox, _appSettings.SelectedCaseFan1Sensor, "机箱风扇1");
                    ApplySmartSensorSelection(CaseFan2ComboBox, _appSettings.SelectedCaseFan2Sensor, "机箱风扇2");
                    ApplySmartSensorSelection(HddComboBox, _appSettings.SelectedHddSensor, "HDD");
                }
                finally
                {
                    // 重置标志，允许用户操作触发保存
                    _isLoadingSettings = false;
                }
            }
            catch (Exception ex)
            {
                _isLoadingSettings = false; // 确保在异常情况下也重置标志
            }
        }

        /// <summary>
        /// 应用智能传感器选择逻辑：
        /// 1. 检查配置中的传感器是否在当前可用传感器中存在
        /// 2. 如果存在，选中配置中的传感器
        /// 3. 如果不存在，选择第一项作为默认值
        /// </summary>
        private void ApplySmartSensorSelection(CustomComboBox comboBox, string configuredSensor, string sensorType)
        {
            if (comboBox.InternalItems.Count == 0)
            {
                return;
            }

            // 尝试设置配置中的传感器
            bool foundConfigured = SetComboBoxSelection(comboBox, configuredSensor);

            if (!foundConfigured)
            {
                if (comboBox.InternalItems.Count > 0)
                {
                    var firstItem = comboBox.InternalItems[0] as ComboBoxItem;
                    switch (sensorType)
                    {
                        case "CPU风扇":
                            firstItem = comboBox.InternalItems[1] as ComboBoxItem;
                            break;
                        case "机箱风扇1":
                            firstItem = comboBox.InternalItems[2] as ComboBoxItem;
                            break;
                        case "机箱风扇2":
                            firstItem = comboBox.InternalItems[3] as ComboBoxItem;
                            break;
                        default:
                            firstItem = comboBox.InternalItems[0] as ComboBoxItem;
                            break;
                    }
                    if (firstItem != null)
                    {
                        comboBox.SelectedItem = firstItem;

                        // 更新内存中的设置为新选择的传感器
                        UpdateAppSettingsForSensor(comboBox.Name, firstItem.Tag?.ToString() ?? "");
                    }
                }
            }
        }

        /// <summary>
        /// 根据ComboBox名称更新AppSettings中对应的传感器设置
        /// </summary>
        private void UpdateAppSettingsForSensor(string comboBoxName, string sensorKey)
        {
            switch (comboBoxName)
            {
                case "NetworkInterfaceComboBox":
                    _appSettings.SelectedNetworkInterface = sensorKey;
                    break;
                case "GpuComboBox":
                    _appSettings.SelectedGpuSensor = sensorKey;
                    break;
                case "CpuTempSensorComboBox":
                    _appSettings.SelectedCpuTempSensor = sensorKey;
                    break;
                case "CpuVoltageComboBox":
                    _appSettings.SelectedCpuVoltageSensor = sensorKey;
                    break;
                case "CpuUsageComboBox":
                    _appSettings.SelectedCpuUsageSensor = sensorKey;
                    break;
                case "CpuFanComboBox":
                    _appSettings.SelectedCpuFanSensor = sensorKey;
                    break;
                case "PumpFanComboBox":
                    _appSettings.SelectedPumpFanSensor = sensorKey;
                    break;
                case "CaseFan1ComboBox":
                    _appSettings.SelectedCaseFan1Sensor = sensorKey;
                    break;
                case "CaseFan2ComboBox":
                    _appSettings.SelectedCaseFan2Sensor = sensorKey;
                    break;
                case "HddComboBox":
                    _appSettings.SelectedHddSensor = sensorKey;
                    break;
            }

            // Updated setting
        }

        private async void SaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 收集所有UI设置到_appSettings对象
                CollectAllSettingsFromUI();

                // 保存所有设置到JSON文件（包括温度单位）
                _appSettings.Save();

                // 同步更新TemperatureUnitService（保持兼容性）
                _temperatureUnitService.UseFahrenheit = _appSettings.Fahrenheit;

                // 保存启动设置（只在状态发生变化时执行操作）
                var currentAutoStartState = StartupService.IsCompleteAutoStartSet();
                var desiredAutoStartState = AutoLaunchIndicator.IsChecked;

                if (desiredAutoStartState != currentAutoStartState)
                {
                    if (desiredAutoStartState)
                    {
                        // 需要启用自启，但当前未启用
                        var result = StartupService.SetupCompleteAutoStart();
                        if (!result)
                        {
                            MessageBox.Show(
                                "设置开机自启失败，可能需要管理员权限。\n\n" +
                                "请尝试以管理员身份运行程序，或手动运行 install-service.bat 脚本。",
                                "开机自启设置失败",
                                MessageBoxButton.OK,
                                MessageBoxImage.Warning);
                        }
                    }
                    else
                    {
                        // 需要禁用自启，但当前已启用
                        var result = StartupService.RemoveCompleteAutoStart();
                        if (!result)
                        {
                            MessageBox.Show(
                                "移除开机自启失败，可能需要管理员权限。",
                                "开机自启移除失败",
                                MessageBoxButton.OK,
                                MessageBoxImage.Warning);
                        }
                    }
                }
                // 如果状态没有变化，则不执行任何操作

                // 通知Service配置已更新
                await NotifyServiceConfigurationChanged();

                MessageBox.Show("Settings saved successfully!", "Settings",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从UI收集所有设置到_appSettings对象
        /// </summary>
        private void CollectAllSettingsFromUI()
        {
            try
            {
                // 应用程序设置
                _appSettings.AutoLaunch = AutoLaunchIndicator.IsChecked;
                //_appSettings.DisableUpdating = DisableUpdatingIndicator.IsChecked;
                //_appSettings.ShowMessageBox = ShowMessageBoxIndicator.IsChecked;
                _appSettings.Fahrenheit = FahrenheitIndicator.IsChecked;
                //_appSettings.CompatibilityMode = CompatibilityModeIndicator.IsChecked;

                // 延迟启动设置
                if (int.TryParse(DelayedStartTextBox.Text, out int delayedStart))
                {
                    _appSettings.DelayedStart = delayedStart;
                }

                // 硬件检测设置
                //_appSettings.AutoDetectCpu = AutoDetectCpuIndicator.IsChecked;
                //_appSettings.AutoDetectGpu = AutoDetectGpuIndicator.IsChecked;
                //_appSettings.AutoDetectMemory = AutoDetectMemoryIndicator.IsChecked;

                // 传感器选择设置已经在各个Handle方法中实时更新了
            }
            catch (Exception ex)
            {
                // Error collecting settings from UI
            }
        }



        private void LoadSensorUIFromCache()
        {
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                if (_sensorsUILoaded)
                {
                    // 即使UI已加载，也要重新加载保存的设置，确保显示最新数据
                    LoadSavedSensorSettingsWithFallback();
                    return;
                }

                DiscoverSensors(); // 这会调用LoadSavedSensorSettingsWithFallback()
                _sensorsUILoaded = true;

                stopwatch.Stop();
            }
            catch (Exception ex)
            {
                // Error loading sensor UI from cache
            }
        }

        private async Task LoadMonitorSettingsAsync()
        {
            try
            {
                // 如果UI已经加载过，直接返回
                if (_sensorsUILoaded)
                {
                    return;
                }

                // 检查传感器是否已经被发现（在Loading页面预加载）
                if (!_monitorService.SensorsDiscovered)
                {
                    // 确保硬件监控服务已初始化
                    if (!_monitorService.IsInitialized)
                    {
                        await _monitorService.InitializeAsync();
                    }

                    // 发现传感器
                    await _monitorService.DiscoverSensorsAsync();
                }

                // 在UI线程上执行传感器UI更新（只执行一次）
                DiscoverSensors();
                _sensorsUILoaded = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sensor settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void LoadMonitorSettings()
        {
            try
            {
                // 加载完成后自动发现传感器
                DiscoverSensors();
            }
            catch (Exception ex)
            {
                // Error loading monitor settings
            }
        }

        private async void DiscoverSensors_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 重置传感器发现状态，强制重新扫描
                _monitorService.ResetSensorDiscovery();

                // 重新发现传感器
                await _monitorService.DiscoverSensorsAsync();

                // 重新加载UI
                DiscoverSensors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error discovering sensors: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DiscoverSensors()
        {
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                var sensors = _monitorService.GetAllAvailableSensors();

                // 打印所有传感器树状结构
                PrintAllSensorsTree();

                // 清空现有下拉框
                CpuTempSensorComboBox.ClearItems();
                CpuVoltageComboBox.ClearItems();
                CpuUsageComboBox.ClearItems();
                GpuComboBox.ClearItems();
                CpuFanComboBox.ClearItems();
                PumpFanComboBox.ClearItems();
                CaseFan1ComboBox.ClearItems();
                CaseFan2ComboBox.ClearItems();
                HddComboBox.ClearItems();

                // 收集主板风扇列表，供所有风扇选项框使用
                var motherboardFans = new List<string>();

                // 收集网络接口列表，避免重复添加
                var networkInterfaces = new HashSet<string>();

                // 根据传感器类型填充对应下拉框 - 不自动选择任何项
                foreach (var hardwareType in sensors)
                {
                    foreach (var hardware in hardwareType.Value)
                    {
                        foreach (var sensor in hardware.Value)
                        {
                            var key = sensor.Key;
                            var displayName = $"{hardware.Key} - {sensor.Name}";
                            string hardwareTypeKey = hardwareType.Key;

                            // Network Interface - 收集网络适配器名称，而不是具体传感器
                            if (hardwareTypeKey == "Network")
                            {
                                // hardware.Key就是网络适配器名称
                                if (!networkInterfaces.Contains(hardware.Key))
                                {
                                    networkInterfaces.Add(hardware.Key);
                                    // 不自动选择，让LoadSavedSensorSettings处理选择逻辑
                                    NetworkInterfaceComboBox.AddItem(hardware.Key, hardware.Key, false);
                                }
                            }
                            // GPU硬件 - 支持GpuNvidia、GpuIntel等所有GPU类型
                            else if (hardwareTypeKey.StartsWith("Gpu"))
                            {
                                // hardware.Key就是GPU硬件型号名称
                                string gpuName = hardware.Key;
                                bool alreadyExists = false;

                                // 检查是否已存在该GPU名称
                                foreach (var item in GpuComboBox.InternalItems)
                                {
                                    if (item is ComboBoxItem comboItem && comboItem.Tag?.ToString() == gpuName)
                                    {
                                        alreadyExists = true;
                                        break;
                                    }
                                }

                                if (!alreadyExists)
                                {
                                    // 不自动选择，让LoadSavedSensorSettings处理选择逻辑
                                    GpuComboBox.AddItem(gpuName, gpuName, false);
                                }
                            }
                            // Storage硬件 - 支持各种存储设备
                            else if (hardwareTypeKey == "Storage")
                            {
                                // hardware.Key就是存储设备名称（如 ST14000NM001G-2KJ103）
                                string storageName = hardware.Key;
                                bool alreadyExists = false;

                                // 检查是否已存在该存储设备名称
                                foreach (var item in HddComboBox.InternalItems)
                                {
                                    if (item is ComboBoxItem comboItem && comboItem.Tag?.ToString() == storageName)
                                    {
                                        alreadyExists = true;
                                        break;
                                    }
                                }

                                if (!alreadyExists)
                                {
                                    // 不自动选择，让LoadSavedSensorSettings处理选择逻辑
                                    HddComboBox.AddItem(storageName, storageName, false);
                                }
                            }
                            // CPU传感器 - 从Cpu硬件类型中获取
                            else if (hardwareTypeKey == "Cpu")
                            {
                                if (sensor.Type == "Temperature")
                                {
                                    // 添加所有温度传感器，不自动选择
                                    CpuTempSensorComboBox.AddItem(displayName, key, false);
                                }
                                else if (sensor.Type == "Voltage" && sensor.Name.Contains("CPU Core"))
                                {
                                    // 不自动选择，让LoadSavedSensorSettings处理选择逻辑
                                    CpuVoltageComboBox.AddItem(displayName, key, false);
                                }
                                else if (sensor.Type == "Load")
                                {
                                    // 添加所有负载传感器，不自动选择
                                    CpuUsageComboBox.AddItem(displayName, key, false);
                                }
                            }
                            // 收集主板风扇传感器
                            else if (hardwareTypeKey == "Motherboard" && sensor.Type == "Fan")
                            {
                                string fanName = $"{hardware.Key} - {sensor.Name}";
                                string fanKey = sensor.Key;
                                motherboardFans.Add($"{fanName}|{fanKey}"); // 使用分隔符存储显示名称和键值
                            }
                            // HDD传感器处理在外层循环中进行
                        }
                    }
                }

                // 填充风扇选项框 - 所有风扇选项框使用相同的主板风扇列表，不自动选择
                PopulateFanComboBoxes(motherboardFans);

                // 如果没有发现传感器，添加空选项
                if (NetworkInterfaceComboBox.InternalItems.Count == 0)
                    NetworkInterfaceComboBox.AddItem("无可用网络接口", "", true);
                if (CpuTempSensorComboBox.InternalItems.Count == 0)
                    CpuTempSensorComboBox.AddItem("无可用CPU温度传感器", "", true);
                if (CpuVoltageComboBox.InternalItems.Count == 0)
                    CpuVoltageComboBox.AddItem("无可用CPU电压传感器", "", true);
                if (CpuUsageComboBox.InternalItems.Count == 0)
                    CpuUsageComboBox.AddItem("无可用CPU使用率传感器", "", true);
                if (GpuComboBox.InternalItems.Count == 0)
                    GpuComboBox.AddItem("无可用GPU", "", true);
                if (HddComboBox.InternalItems.Count == 0)
                    HddComboBox.AddItem("无可用HDD", "", true);

                // 加载已保存的传感器设置，并应用智能选择逻辑
                LoadSavedSensorSettingsWithFallback();

                stopwatch.Stop();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error discovering sensors: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintAllSensorsTree()
        {
            try
            {
                var sensors = _monitorService.GetAllAvailableSensors();

                foreach (var hardwareType in sensors)
                {
                    foreach (var hardware in hardwareType.Value)
                    {
                        foreach (var sensor in hardware.Value)
                        {
                            string valueStr = "无数据";
                            if (sensor.Sensor.Value.HasValue)
                            {
                                valueStr = sensor.Sensor.Value.Value.ToString("F2");
                                if (sensor.Type == "Temperature")
                                    valueStr += "°C";
                                else if (sensor.Type == "Power")
                                    valueStr += "W";
                                else if (sensor.Type == "Fan")
                                    valueStr += " RPM";
                                else if (sensor.Type == "Load" || sensor.Type == "Control")
                                    valueStr += "%";
                                else if (sensor.Type == "Voltage")
                                    valueStr += "V";
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Error printing sensor tree
            }
        }


        private void SaveHardwareSettings()
        {
            try
            {
                // 所有传感器现在都在选择时立即保存，这里只需要保存一些通用设置
                // 如果需要保存其他设置，可以在这里添加
            }
            catch (Exception ex)
            {
                // Error saving hardware settings
            }
        }

        /// <summary>
        /// 获取当前选中的风扇传感器键值
        /// </summary>
        public Dictionary<string, string> GetSelectedFanSensors()
        {
            return new Dictionary<string, string>
            {
                ["CpuFan"] = _appSettings.SelectedCpuFanSensor,
                ["PumpFan"] = _appSettings.SelectedPumpFanSensor,
                ["CaseFan1"] = _appSettings.SelectedCaseFan1Sensor,
                ["CaseFan2"] = _appSettings.SelectedCaseFan2Sensor
            };
        }

        /// <summary>
        /// 获取风扇传感器的实际值
        /// </summary>
        public Dictionary<string, float> GetFanSensorValues()
        {
            var result = new Dictionary<string, float>();
            var fanSensors = GetSelectedFanSensors();
            var sensorKeys = fanSensors.Values.Where(v => !string.IsNullOrEmpty(v)).ToList();

            if (sensorKeys.Count > 0)
            {
                var sensorValues = _monitorService.GetSelectedValues(sensorKeys);

                foreach (var fanType in fanSensors)
                {
                    if (!string.IsNullOrEmpty(fanType.Value) && sensorValues.ContainsKey(fanType.Value))
                    {
                        result[fanType.Key] = sensorValues[fanType.Value];
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取所有选中的传感器键值
        /// </summary>
        public Dictionary<string, string> GetAllSelectedSensors()
        {
            var result = new Dictionary<string, string>();

            // 添加网络接口
            if (!string.IsNullOrEmpty(_appSettings.SelectedNetworkInterface))
                result["NetworkInterface"] = _appSettings.SelectedNetworkInterface;

            // 添加GPU
            if (!string.IsNullOrEmpty(_appSettings.SelectedGpuSensor))
                result["Gpu"] = _appSettings.SelectedGpuSensor;

            // 添加CPU传感器
            if (!string.IsNullOrEmpty(_appSettings.SelectedCpuTempSensor))
                result["CpuTemp"] = _appSettings.SelectedCpuTempSensor;
            if (!string.IsNullOrEmpty(_appSettings.SelectedCpuVoltageSensor))
                result["CpuVoltage"] = _appSettings.SelectedCpuVoltageSensor;
            if (!string.IsNullOrEmpty(_appSettings.SelectedCpuUsageSensor))
                result["CpuUsage"] = _appSettings.SelectedCpuUsageSensor;

            // 添加风扇传感器
            if (!string.IsNullOrEmpty(_appSettings.SelectedCpuFanSensor))
                result["CpuFan"] = _appSettings.SelectedCpuFanSensor;
            if (!string.IsNullOrEmpty(_appSettings.SelectedPumpFanSensor))
                result["PumpFan"] = _appSettings.SelectedPumpFanSensor;
            if (!string.IsNullOrEmpty(_appSettings.SelectedCaseFan1Sensor))
                result["CaseFan1"] = _appSettings.SelectedCaseFan1Sensor;
            if (!string.IsNullOrEmpty(_appSettings.SelectedCaseFan2Sensor))
                result["CaseFan2"] = _appSettings.SelectedCaseFan2Sensor;

            // 添加HDD传感器
            if (!string.IsNullOrEmpty(_appSettings.SelectedHddSensor))
                result["Hdd"] = _appSettings.SelectedHddSensor;

            return result;
        }

        /// <summary>
        /// 获取所有选中传感器的实际值
        /// </summary>
        public Dictionary<string, float> GetAllSensorValues()
        {
            var result = new Dictionary<string, float>();
            var allSensors = GetAllSelectedSensors();
            var sensorKeys = allSensors.Values.Where(v => !string.IsNullOrEmpty(v)).ToList();

            if (sensorKeys.Count > 0)
            {
                var sensorValues = _monitorService.GetSelectedValues(sensorKeys);

                foreach (var sensor in allSensors)
                {
                    if (!string.IsNullOrEmpty(sensor.Value) && sensorValues.ContainsKey(sensor.Value))
                    {
                        result[sensor.Key] = sensorValues[sensor.Value];
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 实时发送选中的传感器配置到Service
        /// </summary>
        private async Task SendSelectedSensorsToService()
        {
            try
            {
                var selectedSensors = GetAllSelectedSensors();
                var dataManager = HardwareDataManager.Instance;

                // 转换为object字典以匹配API
                var sensorData = selectedSensors.ToDictionary(k => k.Key, v => (object)v.Value);

                await dataManager.SendSelectedSensorData(sensorData);
            }
            catch (Exception ex)
            {
                // Error sending sensor configuration
            }
        }

        /// <summary>
        /// 收集当前页面的所有设置
        /// </summary>
        /// <returns>包含所有设置的AppSettings对象</returns>
        private AppSettings CollectCurrentSettings()
        {
            var settings = new AppSettings();

            // 收集应用程序设置
            settings.AutoLaunch = AutoLaunchIndicator.IsChecked;
            //settings.DisableUpdating = DisableUpdatingIndicator.IsChecked;
            //settings.ShowMessageBox = ShowMessageBoxIndicator.IsChecked;
            settings.Fahrenheit = FahrenheitIndicator.IsChecked;
            //settings.CompatibilityMode = CompatibilityModeIndicator.IsChecked;

            // 收集延迟启动设置
            if (int.TryParse(DelayedStartTextBox.Text, out int delayedStart))
            {
                settings.DelayedStart = delayedStart;
            }

            // 收集硬件检测设置
            //settings.AutoDetectCpu = AutoDetectCpuIndicator.IsChecked;
            //settings.AutoDetectGpu = AutoDetectGpuIndicator.IsChecked;
            //settings.AutoDetectMemory = AutoDetectMemoryIndicator.IsChecked;

            // 收集传感器选择设置
            var allSelectedSensors = GetAllSelectedSensors();
            settings.SelectedNetworkInterface = allSelectedSensors.GetValueOrDefault("NetworkInterface", "");
            settings.SelectedGpuSensor = allSelectedSensors.GetValueOrDefault("Gpu", "");
            settings.SelectedCpuTempSensor = allSelectedSensors.GetValueOrDefault("CpuTemp", "");
            settings.SelectedCpuVoltageSensor = allSelectedSensors.GetValueOrDefault("CpuVoltage", "");
            settings.SelectedCpuUsageSensor = allSelectedSensors.GetValueOrDefault("CpuUsage", "");
            settings.SelectedCpuFanSensor = allSelectedSensors.GetValueOrDefault("CpuFan", "");
            settings.SelectedPumpFanSensor = allSelectedSensors.GetValueOrDefault("PumpFan", "");
            settings.SelectedCaseFan1Sensor = allSelectedSensors.GetValueOrDefault("CaseFan1", "");
            settings.SelectedCaseFan2Sensor = allSelectedSensors.GetValueOrDefault("CaseFan2", "");

            return settings;
        }

        /// <summary>
        /// 通知Service配置已更改，Service应该重新读取配置文件
        /// </summary>
        private async Task NotifyServiceConfigurationChanged()
        {
            try
            {
                var dataManager = HardwareDataManager.Instance;

                // 发送配置文件路径给Service
                var configPath = AppSettings.GetSettingsPath();
                await dataManager.SendConfigurationChanged(configPath);

                // 单独发送温度单位变更通知
                await dataManager.SendTemperatureUnitChanged();
            }
            catch (Exception ex)
            {
                // Error notifying service about configuration change
            }
        }
    }

}
