using System;
using System.IO;
using System.Text.Json;

namespace N90.Shared.Services
{
    public class TemperatureUnitService
    {
        private TemperatureUnit temperatureUnit;
        private readonly string _settingsPath;

        public TemperatureUnitService()
        {
            // 使用与AppSettings相同的路径逻辑
            _settingsPath = GetSettingsFilePath();
            temperatureUnit = LoadTemperatureUnitSetting();
        }

        /// <summary>
        /// 获取设置文件路径（与AppSettings保持一致）
        /// </summary>
        private string GetSettingsFilePath()
        {
            const string fileName = "setting.json";

#if DEBUG
            // Debug模式：项目根目录
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：可执行文件同目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        public TemperatureUnit TemperatureUnit => temperatureUnit;

        public byte TemperatureCelsiusCode => (byte)temperatureUnit;

        public void SwitchTemperatureUnit(TemperatureUnit unit)
        {
            this.temperatureUnit = unit;
            SaveTemperatureUnitSetting();
        }

        /// <summary>
        /// 从JSON文件加载温度单位设置
        /// </summary>
        public TemperatureUnit LoadTemperatureUnitSetting()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    using var document = JsonDocument.Parse(json);
                    if (document.RootElement.TryGetProperty("fahrenheit", out var fahrenheitElement))
                    {
                        bool useFahrenheit = fahrenheitElement.GetBoolean();
                        return useFahrenheit ? TemperatureUnit.Fahrenheit : TemperatureUnit.Celsius;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading temperature unit from JSON: {ex.Message}");
            }

            return TemperatureUnit.Celsius; // 默认摄氏度
        }

        public void ApplyTemperatureUnitSetting()
        {
            temperatureUnit = LoadTemperatureUnitSetting();
        }

        /// <summary>
        /// 保存温度单位设置到JSON文件
        /// </summary>
        public void SaveTemperatureUnitSetting()
        {
            try
            {
                // 读取现有的JSON文件
                var settings = new Dictionary<string, object>();

                if (File.Exists(_settingsPath))
                {
                    var existingJson = File.ReadAllText(_settingsPath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var existingSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(existingJson, options);
                    if (existingSettings != null)
                    {
                        settings = existingSettings;
                    }
                }

                // 更新温度单位设置
                settings["fahrenheit"] = temperatureUnit == TemperatureUnit.Fahrenheit;

                // 保存回JSON文件
                var directory = Path.GetDirectoryName(_settingsPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var saveOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var updatedJson = JsonSerializer.Serialize(settings, saveOptions);
                File.WriteAllText(_settingsPath, updatedJson);

                Console.WriteLine($"Temperature unit saved to JSON: {(temperatureUnit == TemperatureUnit.Fahrenheit ? "Fahrenheit" : "Celsius")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving temperature unit to JSON: {ex.Message}");
            }
        }

        public bool UseFahrenheit
        {
            get => temperatureUnit == TemperatureUnit.Fahrenheit;
            set
            {
                temperatureUnit = value ? TemperatureUnit.Fahrenheit : TemperatureUnit.Celsius;
                SaveTemperatureUnitSetting(); // 立即保存到JSON
            }
        }
    }
}
