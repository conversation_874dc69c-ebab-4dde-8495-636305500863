<UserControl x:Class="N90.Client.Views.Components.InputDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="#80000000">

    <UserControl.Resources>
        <!-- Confirm 按钮样式 (基于NavigationButton) -->
        <Style x:Key="ConfirmButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="200"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Roboto, Segoe UI, Tahoma, Arial"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- 使用Path绘制特殊切角形状 -->
                            <Path x:Name="ButtonPath"
                                  StrokeThickness="0">
                                <Path.Fill>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#8096D702" Offset="0"/>
                                        <GradientStop Color="#4D01E9FE" Offset="1"/>
                                    </LinearGradientBrush>
                                </Path.Fill>
                                <Path.Data>
                                    <!-- 切角形状路径 -->
                                    <PathGeometry>
                                        <PathFigure StartPoint="12,0" IsClosed="True">
                                            <LineSegment Point="200,0"/>
                                            <LineSegment Point="200,18"/>
                                            <LineSegment Point="200,18"/>
                                            <LineSegment Point="188,30"/>
                                            <LineSegment Point="0,30"/>
                                            <LineSegment Point="0,12"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <!-- 文字内容 -->
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="10,5"/>

                            <!-- 阴影效果 -->
                            <Grid.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Grid.Effect>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Cancel 按钮样式 (基于NavigationButton) -->
        <Style x:Key="CancelButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="200"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Roboto, Segoe UI, Tahoma, Arial"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- 使用Path绘制特殊切角形状 -->
                            <Path x:Name="ButtonPath"
                                  StrokeThickness="0">
                                <Path.Fill>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#8096D702" Offset="0"/>
                                        <GradientStop Color="#4D01E9FE" Offset="1"/>
                                    </LinearGradientBrush>
                                </Path.Fill>
                                <Path.Data>
                                    <!-- 切角形状路径 -->
                                    <PathGeometry>
                                        <PathFigure StartPoint="12,0" IsClosed="True">
                                            <LineSegment Point="200,0"/>
                                            <LineSegment Point="200,18"/>
                                            <LineSegment Point="200,18"/>
                                            <LineSegment Point="188,30"/>
                                            <LineSegment Point="0,30"/>
                                            <LineSegment Point="0,12"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <!-- 文字内容 -->
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="10,5"/>

                            <!-- 阴影效果 -->
                            <Grid.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Grid.Effect>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- 主对话框容器 -->
        <Border Background="Black"
                BorderThickness="1"
                Width="500"
                Height="200"
                HorizontalAlignment="Center"
                VerticalAlignment="Center">
            <Border.BorderBrush>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#FF97D700" Offset="0"/>
                    <GradientStop Color="#FF01E9FE" Offset="1"/>
                </LinearGradientBrush>
            </Border.BorderBrush>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 顶部图标 -->
                <Border Grid.Row="0"
                        Width="80"
                        Height="80"
                        Background="Black"
                        BorderThickness="0"
                        CornerRadius="40"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        Margin="0,-40,0,-18"
                        Panel.ZIndex="1">
                    <Border.BorderBrush>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                            <GradientStop Color="#FF97D700" Offset="0"/>
                            <GradientStop Color="#FF01E9FE" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.BorderBrush>

                    <!-- 使用img14.png图片 -->
                    <Image Source="pack://application:,,,/Resources/Images/img14.png"
                           Width="80"
                           Height="80"
                           Stretch="Uniform"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
                </Border>

                <!-- 中间内容区域 -->
                <Grid Grid.Row="1" Margin="20,10,20,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 输入框容器 -->
                    <Border Grid.Row="1"
                            Background="Black"
                            BorderBrush="White"
                            BorderThickness="1"
                            CornerRadius="0"
                            Padding="10">
                        <Grid>
                            <!-- 占位符文本 -->
                            <TextBlock x:Name="PlaceholderTextBlock"
                                       Text="{Binding Placeholder, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                       Foreground="#FF666666"
                                       FontSize="14"
                                       IsHitTestVisible="False"
                                       VerticalAlignment="Top"
                                       Visibility="Visible"/>

                            <!-- 输入框 -->
                            <TextBox x:Name="InputTextBox"
                                     Text="{Binding InputText, RelativeSource={RelativeSource AncestorType=UserControl}, Mode=TwoWay}"
                                     Background="Transparent"
                                     Foreground="White"
                                     BorderThickness="0"
                                     FontSize="14"
                                     FontFamily="Roboto, Segoe UI, Tahoma, Arial"
                                     MinHeight="100"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     CaretBrush="#FF97D700"
                                     SelectionBrush="#4497D700"
                                     TextChanged="InputTextBox_TextChanged"/>
                        </Grid>
                    </Border>
                </Grid>

                <!-- 底部按钮区域 -->
                <Canvas Grid.Row="2"
                        Height="35"
                        Width="380"
                        Margin="0,10,0,13"
                        HorizontalAlignment="Center">
                    <!-- 确认按钮 -->
                    <Button x:Name="ConfirmButton"
                            Content="Confirm"
                            Style="{StaticResource ConfirmButtonStyle}"
                            Click="ConfirmButton_Click"
                            Canvas.Left="0"
                            Canvas.Top="0"/>

                    <!-- 取消按钮 (重叠20px) -->
                    <Button x:Name="CancelButton"
                            Content="Cancel"
                            Style="{StaticResource CancelButtonStyle}"
                            Click="CancelButton_Click"
                            Canvas.Left="180"
                            Canvas.Top="0"/>
                </Canvas>
            </Grid>
        </Border>
    </Grid>
</UserControl>
