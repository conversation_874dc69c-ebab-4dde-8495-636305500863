// 迁移自原项目 SysMonitorUSB.Data.HardwareData 及其相关类型
namespace N90.Shared
{
    public class HardwareData
    {
        public Date Date { get; set; }

        public Time Time { get; set; }

        public Weekday Weekday { get; set; }


        // CPU Information
        public CPUTemperature CPUTemperature { get; set; }
        public CPUUsage CPUUsage { get; set; }
        public CPUPower CPUPower { get; set; }
        public CPUFanSpeed CPUFanSpeed { get; set; }
        public CPUModel CPUModel { get; set; }

        // GPU Information
        public GPUTemperature GPUTemperature { get; set; }
        public GPUMemoryUsage GPUMemoryUsage { get; set; }
        public GPUPower GPUPower { get; set; }
        public GPUModel GPUModel { get; set; }

        // RAM Information
        public RAMUsage RAMUsage { get; set; }
        public AvailableRAM AvailableRAM { get; set; }

        // Case Fans
        public CaseFan1Speed CaseFan1Speed { get; set; }
        public CaseFan2Speed CaseFan2Speed { get; set; }

        // HDD Information
        public HDDTemperature HDDTemperature { get; set; }
        public HDDUsage HDDUsage { get; set; }

        public UploadSpeed UploadSpeed { get; set; }
        public DownloadSpeed DownloadSpeed { get; set; }

        //自定义字符串
        public CustomString CustomString { get; set; }
    }
    /**
    * type赋值说明
    * 时间相关：0x01 ~ 0x0F
    * CPU相关：0x10 ~ 0x1F
    * GPU相关：0x20 ~ 0x2F
    * RAM相关：0x30 ~ 0x3F
    * 风扇相关：0x40 ~ 0x4F
    * 硬盘相关：0x50 ~ 0x5F
    * 网络相关：0x60 ~ 0x6F
    */

    public class CPUTemperature
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x10;
    }
    public class CPUUsage
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x11;
    }
    public class CPUPower
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x12;
    }
    public class CPUFanSpeed
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x13;
    }
    public class CPUModel
    {
        public bool show { get; set; }
        public string value { get; set; } = "null";
        public byte type { get; set; } = 0x14;
    }

    public class GPUTemperature
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x20;
    }

    public class GPUMemoryUsage
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x21;
    }
    public class GPUPower
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x22;
    }
    public class GPUModel
    {
        public bool show { get; set; }
        public string value { get; set; } = "null";
        public byte type { get; set; } = 0x23;
    }
    public class RAMUsage
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x30;
    }
    public class AvailableRAM
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x31;
    }
    public class CaseFan1Speed
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x40;
    }
    public class CaseFan2Speed
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x41;
    }
    public class HDDTemperature
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x50;
    }
    public class HDDUsage
    {
        public bool show { get; set; }
        public float? value { get; set; } = 0.00f;
        public byte type { get; set; } = 0x51;
    }
    public class Date
    {
        public bool show { get; set; }
        public string value { get; set; } = "";
        public byte type { get; set; } = 0x00;
    }
    public class Time
    {
        public bool show { get; set; }
        public string value { get; set; } = "";
        public byte type { get; set; } = 0x01;
    }
    public class Weekday
    {
        public bool show { get; set; }
        public string value { get; set; } = "";
        public byte type { get; set; } = 0x02;
    }
    public class UploadSpeed
    {
        public bool show { get; set; }
        public double? value { get; set; } = 0.00;
        public byte type { get; set; } = 0x60;
    }
    public class DownloadSpeed
    {
        public bool show { get; set; }
        public double? value { get; set; } = 0.00;
        public byte type { get; set; } = 0x61;
    }

    public class CustomString
    {
        public bool show { get; set; }
        public string value { get; set; } = "";
        public byte type { get; set; } = 0x70;
    }
}
