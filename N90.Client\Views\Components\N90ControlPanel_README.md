# N90ControlPanel 组件

## 概述

`N90ControlPanel` 是一个可复用的WPF用户控件，从N90Page的Basic Parameters标签页左侧部分抽取而来。该组件包含了标题显示、设备图片展示和亮度控制功能。

## 功能特性

- **标题显示**：可自定义的主标题文本
- **设备图片**：可配置的设备图片显示
- **灯光开关**：ON/OFF切换按钮，支持状态切换
- **亮度控制**：可拖拽的亮度滑块，支持点击跳转
- **事件通知**：提供灯光开关和亮度改变事件
- **外部控制**：支持通过代码设置灯光状态和亮度值

## 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `Title` | string | "N90" | 主标题文本 |
| `ImageSource` | string | "/Resources/Images/img4.png" | 设备图片路径 |
| `IconSource` | string | "/Resources/Images/img1.png" | 控制区域图标路径 |
| `ControlTitle` | string | "Running Light Switch Brightness" | 控制区域标题 |
| `BrightnessValue` | double | 50.0 | 亮度值 (0-100) |
| `IsLightOn` | bool | true | 当前灯光状态（只读） |

## 事件

### LightSwitchChanged
灯光开关状态改变时触发。

**事件参数：** `LightSwitchEventArgs`
- `IsOn` (bool): 灯光是否开启

### BrightnessChanged
亮度值改变时触发。

**事件参数：** `BrightnessChangedEventArgs`
- `Brightness` (int): 亮度值 (0-100)

## 公共方法

### SetLightSwitch(bool isOn)
设置灯光开关状态。

**参数：**
- `isOn`: 是否开启灯光

### SetBrightness(double brightness)
设置亮度值。

**参数：**
- `brightness`: 亮度值 (0-100)

## 使用示例

### XAML中使用

```xml
<Window xmlns:components="clr-namespace:N90.Client.Views.Components">
    <Grid>
        <components:N90ControlPanel x:Name="ControlPanel"
                                    Title="N90"
                                    ImageSource="/Resources/Images/img4.png"
                                    IconSource="/Resources/Images/img1.png"
                                    ControlTitle="Running Light Switch Brightness"
                                    BrightnessValue="50"
                                    LightSwitchChanged="ControlPanel_LightSwitchChanged"
                                    BrightnessChanged="ControlPanel_BrightnessChanged"/>
    </Grid>
</Window>
```

### 代码中事件处理

```csharp
private void ControlPanel_LightSwitchChanged(object sender, Components.LightSwitchEventArgs e)
{
    Console.WriteLine($"灯光状态: {(e.IsOn ? "开启" : "关闭")}");
    // 添加自定义业务逻辑
}

private void ControlPanel_BrightnessChanged(object sender, Components.BrightnessChangedEventArgs e)
{
    Console.WriteLine($"亮度: {e.Brightness}%");
    // 添加自定义业务逻辑
}
```

### 代码中创建和控制

```csharp
// 创建组件
var controlPanel = new N90ControlPanel
{
    Title = "自定义设备",
    BrightnessValue = 75
};

// 订阅事件
controlPanel.LightSwitchChanged += OnLightSwitchChanged;
controlPanel.BrightnessChanged += OnBrightnessChanged;

// 控制组件状态
controlPanel.SetLightSwitch(true);   // 开启灯光
controlPanel.SetBrightness(80);      // 设置亮度为80%
```

## 样式自定义

组件内部定义了以下样式，可以通过修改组件源码来自定义外观：

- `MainTitleStyle`: 主标题样式
- `MainImgStyle`: 主图片样式
- `GroupTitleWithIconStyle`: 带图标的分组标题样式
- `TitleIconStyle`: 标题图标样式
- `TitleTextStyle`: 标题文本样式

## 注意事项

1. 确保图片资源路径正确，否则图片可能无法显示
2. 亮度值范围为0-100，超出范围的值会被自动限制
3. 组件内部处理了UI线程安全，可以在任何线程中调用公共方法
4. 事件处理中的异步操作已经在后台线程中执行，不会阻塞UI

## 依赖项

- .NET Framework 4.7.2 或更高版本
- WPF框架
- N90.Client.Services.HardwareDataManager（用于亮度更新）

## 版本历史

- v1.0: 初始版本，从N90Page抽取基础功能
- 支持标题、图片、灯光开关和亮度控制
- 提供完整的事件通知和外部控制接口
