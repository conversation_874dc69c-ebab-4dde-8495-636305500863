using System;
using System.Collections.Generic;
using System.Globalization;
using System.Resources;
using System.Reflection;
using System.Security.Principal;
using System.Diagnostics;
using Microsoft.Win32;

namespace N90.Shared.Services
{
    public class LanguageService
    {
        private readonly RegistryKey registryKey;

        public LanguageService()
        {
            registryKey = GetUserRegistryKey();
        }

        private RegistryKey GetUserRegistryKey()
        {
            try
            {
                // Use Local Machine registry for shared settings that all users can access
                // This way both SYSTEM service and user applications can read/write the same settings
                var sharedKey = Registry.LocalMachine.CreateSubKey("SOFTWARE\\N90", true);
                if (sharedKey != null)
                {
                    Console.WriteLine("[DEBUG] Using shared registry key (HKEY_LOCAL_MACHINE)");
                    return sharedKey;
                }

                // Fallback to current user if shared key fails
                Console.WriteLine("[DEBUG] Fallback to current user registry");
                return Registry.CurrentUser.CreateSubKey("Software\\N90");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Failed to get shared registry key: {ex.Message}");
                // Final fallback to current user
                try
                {
                    return Registry.CurrentUser.CreateSubKey("Software\\N90");
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"[DEBUG] Failed to get user registry key: {ex2.Message}");
                    throw;
                }
            }
        }



        public void SwitchLanguage(string languageCode)
        {
            registryKey.SetValue("Language", languageCode);
            System.Threading.Thread.CurrentThread.CurrentUICulture = new CultureInfo(languageCode);
        }

        public string LoadLanguageSetting()
        {
            string languageCode = (string)registryKey.GetValue("Language");
            return languageCode ?? "en-US"; // 默认返回英文
        }

        public void ApplyLanguageSetting()
        {
            string savedLanguageCode = LoadLanguageSetting();
            SwitchLanguage(savedLanguageCode);
        }

        public Language GetCurrentLanguage()
        {
            string languageCode = LoadLanguageSetting();
            return languageCode switch
            {
                "en-US" => Language.EN_US,
                "zh-CN" => Language.ZH_CN,
                _ => Language.EN_US
            };
        }

        public void SetLanguage(Language language)
        {
            string languageCode = language switch
            {
                Language.EN_US => "en-US",
                Language.ZH_CN => "zh-CN",
                _ => "en-US"
            };
            SwitchLanguage(languageCode);
        }

        public static string GetString(string id)
        {
            try
            {
                // Simple dictionary-based localization for now
                var currentCulture = System.Threading.Thread.CurrentThread.CurrentUICulture.Name;

                var strings = currentCulture switch
                {
                    "en-US" => GetEnglishStrings(),
                    "zh-CN" => GetChineseStrings(),
                    _ => GetEnglishStrings() // Default to English
                };

                return strings.TryGetValue(id, out string? value) ? value : $"Missing: {id}";
            }
            catch
            {
                return $"Error loading: {id}";
            }
        }

        private static Dictionary<string, string> GetChineseStrings()
        {
            return new Dictionary<string, string>
            {
                // System tray menu
                { "Show", "显示" },
                { "Language", "语言" },
                { "Chinese", "中文" },
                { "English", "English" },
                { "TemperatureUnit", "温度单位" },
                { "Celsius", "摄氏度 (°C)" },
                { "Fahrenheit", "华氏度 (°F)" },
                { "StartWithWindows", "开机启动" },
                { "Exit", "退出" },
                { "WindowTitle", "N90 硬件监控" },

                // Hardware items
                { "Date", "日期" },
                { "Time", "时间" },
                { "Weekday", "星期" },
                { "CPUTemperature", "CPU温度" },
                { "CPUUsage", "CPU使用率" },
                { "CPUPower", "CPU功耗" },
                { "CPUFanSpeed", "CPU风扇转速" },
                { "CPUModel", "CPU型号" },
                { "GPUTemperature", "GPU温度" },
                { "GPUMemoryUsage", "GPU显存使用率" },
                { "GPUPower", "GPU功耗" },
                { "GPUModel", "GPU型号" },
                { "RAMUsage", "内存使用率" },
                { "AvailableRAM", "可用内存" },
                { "CaseFan1Speed", "机箱风扇1转速" },
                { "CaseFan2Speed", "机箱风扇2转速" },
                { "HDDTemperature", "硬盘温度" },
                { "HDDUsage", "硬盘使用率" },
                { "UploadSpeed", "上传速度" },
                { "DownloadSpeed", "下载速度" },
                { "CustomString", "自定义字符串" }
            };
        }

        private static Dictionary<string, string> GetEnglishStrings()
        {
            return new Dictionary<string, string>
            {
                // System tray menu
                { "Show", "Show" },
                { "Language", "Language" },
                { "Chinese", "中文" },
                { "English", "English" },
                { "TemperatureUnit", "Temperature Unit" },
                { "Celsius", "Celsius (°C)" },
                { "Fahrenheit", "Fahrenheit (°F)" },
                { "StartWithWindows", "Start with Windows" },
                { "Exit", "Exit" },
                { "WindowTitle", "N90 Hardware Monitor" },

                // Hardware items
                { "Date", "Date" },
                { "Time", "Time" },
                { "Weekday", "Weekday" },
                { "CPUTemperature", "CPU Temperature" },
                { "CPUUsage", "CPU Usage" },
                { "CPUPower", "CPU Power" },
                { "CPUFanSpeed", "CPU Fan Speed" },
                { "CPUModel", "CPU Model" },
                { "GPUTemperature", "GPU Temperature" },
                { "GPUMemoryUsage", "GPU Memory Usage" },
                { "GPUPower", "GPU Power" },
                { "GPUModel", "GPU Model" },
                { "RAMUsage", "RAM Usage" },
                { "AvailableRAM", "Available RAM" },
                { "CaseFan1Speed", "Case Fan 1 Speed" },
                { "CaseFan2Speed", "Case Fan 2 Speed" },
                { "HDDTemperature", "HDD Temperature" },
                { "HDDUsage", "HDD Usage" },
                { "UploadSpeed", "Upload Speed" },
                { "DownloadSpeed", "Download Speed" },
                { "CustomString", "Custom String" }
            };
        }
    }
}
