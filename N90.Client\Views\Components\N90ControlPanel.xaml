<UserControl x:Class="N90.Client.Views.Components.N90ControlPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="270">
    
    <UserControl.Resources>
        <!-- 主标题样式 -->
        <Style x:Key="MainTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <!-- 主图片样式 -->
        <Style x:Key="MainImgStyle" TargetType="Image">
            <Setter Property="Width" Value="225"/>
            <Setter Property="Height" Value="280"/>
            <Setter Property="Margin" Value="0,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 带图标的分组标题样式 -->
        <Style x:Key="GroupTitleWithIconStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 标题图标样式 -->
        <Style x:Key="TitleIconStyle" TargetType="Image">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 标题文本样式 -->
        <Style x:Key="TitleTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <StackPanel>
        <!-- 主标题 -->
        <TextBlock Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                   Style="{StaticResource MainTitleStyle}"/>

        <!-- 电脑模型图片区域 -->
        <Border Background="Transparent"
                CornerRadius="8"
                Width="225"
                Height="280"
                Margin="0,30,0,40">
            <Grid>
                <!-- 电脑模型的简化表示 -->
                <Image Source="{Binding ImageSource, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                       Style="{StaticResource MainImgStyle}"/>
            </Grid>
        </Border>

        <!-- Running Light Switch Brightness -->
        <StackPanel Margin="0,0,0,20">
            <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                <Image Source="{Binding IconSource, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                       Style="{StaticResource TitleIconStyle}"/>
                <TextBlock Text="{Binding ControlTitle, RelativeSource={RelativeSource AncestorType=UserControl}}" 
                           Style="{StaticResource TitleTextStyle}" FontSize="10.5"/>
            </StackPanel>

            <!-- ON/OFF 切换按钮 -->
            <Border Margin="25,10,25,10"
                    Background="Transparent"
                    BorderBrush="#FF666666"
                    BorderThickness="1"
                    Height="20"
                    Padding="2">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- ON 按钮 -->
                    <Button Grid.Column="0"
                            x:Name="LightOnButton"
                            Content="ON"
                            Height="16"
                            Background="#FF666666"
                            Foreground="#FF97D700"
                            BorderThickness="0"
                            Padding="0"
                            FontSize="11"
                            FontFamily="Roboto"
                            FontWeight="Bold"
                            Cursor="Hand"
                            Click="LightOnButton_Click">    
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border x:Name="ButtonBorder"
                                        Background="{TemplateBinding Background}"
                                        Height="16">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Margin="0"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- OFF 按钮 -->
                    <Button Grid.Column="1"
                            x:Name="LightOffButton"
                            Content="OFF"
                            Height="16"
                            Background="Transparent"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="0"
                            FontSize="11"
                            FontFamily="Roboto"
                            FontWeight="Bold"
                            Cursor="Hand"
                            Click="LightOffButton_Click">               
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border x:Name="ButtonBorder"
                                        Background="{TemplateBinding Background}"
                                        Height="16">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Margin="0"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Border>

            <!-- 亮度滑块 -->
            <Border Margin="25,0,25,10"
                    Background="Transparent"
                    BorderBrush="#FF666666"
                    BorderThickness="1"
                    Height="20"
                    Padding="2">
                <!-- 内层透明边框 -->
                <Border Background="Transparent"
                        BorderBrush="Transparent"
                        BorderThickness="0"
                        Padding="0">
                    <Grid x:Name="SliderContainer"
                          Background="Transparent">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="ProgressColumn" Width="50*"/>
                            <ColumnDefinition x:Name="RemainingColumn" Width="50*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 滑块进度条 - 灰色条 -->
                        <Border Grid.Column="0"
                                Background="#FF666666"
                                Height="16"/>

                        <!-- 滑块剩余部分 - 透明背景 -->
                        <Border Grid.Column="1"
                                Background="Transparent"
                                Height="16"/>

                        <!-- 亮度图标 -->
                        <Path Grid.ColumnSpan="2"
                              Fill="#FF97D700"
                              HorizontalAlignment="Left"
                              VerticalAlignment="Center"
                              Stretch="Uniform"
                              Width="10"
                              Height="10"
                              Margin="6,0,0,0"
                              Panel.ZIndex="10"
                              Data="M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z"/>

                        <!-- 简化的滑块控件 - 覆盖整个区域用于交互 -->
                        <Slider Grid.ColumnSpan="2"
                                x:Name="BrightnessSlider"
                                Minimum="0"
                                Maximum="100"
                                Value="{Binding BrightnessValue, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                BorderThickness="0"
                                Height="16"
                                Margin="0"
                                ValueChanged="BrightnessSlider_ValueChanged"
                                PreviewMouseLeftButtonDown="BrightnessSlider_PreviewMouseLeftButtonDown"
                                Cursor="Hand"
                                IsMoveToPointEnabled="True"
                                Focusable="False">
                            <Slider.Template>
                                <ControlTemplate TargetType="Slider">
                                    <Grid Background="Transparent">
                                        <Track x:Name="PART_Track"
                                               VerticalAlignment="Center">
                                            <Track.DecreaseRepeatButton>
                                                <RepeatButton Background="Transparent"
                                                              BorderThickness="0"
                                                              Height="16"
                                                              Focusable="False">
                                                    <RepeatButton.Template>
                                                        <ControlTemplate TargetType="RepeatButton">
                                                            <Border Background="Transparent" Height="16"/>
                                                            <ControlTemplate.Triggers>
                                                                <!-- 移除所有悬停效果 -->
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </RepeatButton.Template>
                                                </RepeatButton>
                                            </Track.DecreaseRepeatButton>
                                            <Track.Thumb>
                                                <Thumb Width="16"
                                                       Height="16"
                                                       Background="Transparent"
                                                       BorderBrush="Transparent"
                                                       BorderThickness="0"
                                                       Opacity="0"
                                                       Cursor="Hand"
                                                       Focusable="False">
                                                    <Thumb.Template>
                                                        <ControlTemplate TargetType="Thumb">
                                                            <Border Background="Transparent"
                                                                   Width="16"
                                                                   Height="16"/>
                                                            <ControlTemplate.Triggers>
                                                                <!-- 移除所有悬停效果 -->
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Thumb.Template>
                                                </Thumb>
                                            </Track.Thumb>
                                            <Track.IncreaseRepeatButton>
                                                <RepeatButton Background="Transparent"
                                                              BorderThickness="0"
                                                              Height="16"
                                                              Focusable="False">
                                                    <RepeatButton.Template>
                                                        <ControlTemplate TargetType="RepeatButton">
                                                            <Border Background="Transparent" Height="16"/>
                                                            <ControlTemplate.Triggers>
                                                                <!-- 移除所有悬停效果 -->
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </RepeatButton.Template>
                                                </RepeatButton>
                                            </Track.IncreaseRepeatButton>
                                        </Track>
                                    </Grid>
                                    <ControlTemplate.Triggers>
                                        <!-- 移除所有悬停效果 -->
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Slider.Template>
                        </Slider>
                    </Grid>
                </Border>
            </Border>
        </StackPanel>
    </StackPanel>
</UserControl>
