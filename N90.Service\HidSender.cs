// HID 发送实现，依赖 HidLibrary
using System;
using HidLibrary;
using System.Linq;
using N90.Shared;
using N90.Shared.Services;
using System.Text;
using System.Threading;

namespace N90.Service
{
    public class HidSender
    {
        private readonly int vid;
        private readonly int pid;
        private HidDevice device;

        public bool IsDeviceConnected => device != null && device.IsConnected;

        public HidSender(int vid, int pid)
        {
            this.vid = vid;
            this.pid = pid;
            OpenDevice();
        }

        public bool OpenDevice()
        {
            try
            {
                // LoggerExtensions.LogDebugWithCaller($"Attempting to open HID device VID:{vid:X4} PID:{pid:X4}");

                var deviceList = HidDevices.Enumerate(vid, pid);
                device = deviceList.FirstOrDefault();

                if (device != null && device.IsConnected)
                {
                    device.OpenDevice();
                    LoggerExtensions.LogHidCommunication("OpenDevice", $"VID:{vid:X4} PID:{pid:X4}", true);
                    return true;
                }

                // LoggerExtensions.LogHidCommunication("OpenDevice", $"Device not found VID:{vid:X4} PID:{pid:X4}", false);
                return false;
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Failed to open HID device");
                return false;
            }
        }

        public void CloseDevice()
        {
            try
            {
                if (device != null && device.IsOpen)
                {
                    device.CloseDevice();
                    device = null;
                    LoggerExtensions.LogHidCommunication("CloseDevice", $"VID:{vid:X4} PID:{pid:X4}", true);
                }
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Failed to close HID device");
            }
        }

        public async void SendData(HardwareData data, byte temperatureCelsiusCode)
        {
            byte[] dataPacket;

            //时间
            if (data.Date.show)
            {
                // Parse date string (format: YYYY-MM-DD)
                string[] dateParts = data.Date.value.Split('-');
                int year = int.Parse(dateParts[0]);
                int month = int.Parse(dateParts[1]);
                int day = int.Parse(dateParts[2]);

                dataPacket = new byte[8];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00; // 包序号（仅一个包）
                dataPacket[2] = data.Date.type;
                dataPacket[3] = 4; // 固定3字节长度
                //dataPacket[4] = (byte)(year - 1990); // 年份偏移量
                BitConverter.GetBytes((short)(year)).CopyTo(dataPacket, 4);
                dataPacket[6] = (byte)month; // 月份
                dataPacket[7] = (byte)day; // 日期
                Logger.LogInfo($"Date: {data.Date.value}");
                SendByteData(dataPacket);
            }
            if (data.Time.show)
            {
                // Parse time string (format: HH:mm:ss)
                string[] timeParts = data.Time.value.Split(':');
                int hours = int.Parse(timeParts[0]);
                int minutes = int.Parse(timeParts[1]);
                int seconds = int.Parse(timeParts[2]);

                dataPacket = new byte[7];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00; // 包序号（仅一个包）
                dataPacket[2] = data.Time.type;
                dataPacket[3] = 3; // 固定3字节长度
                dataPacket[4] = (byte)hours; // 小时
                dataPacket[5] = (byte)minutes; // 分钟
                dataPacket[6] = (byte)seconds; // 秒
                Logger.LogInfo($"Time: {data.Time.value}");
                SendByteData(dataPacket);
            }
            if (data.Weekday.show)
            {
                // Convert weekday string to number (1-7)
                int weekday = 0;
                switch (data.Weekday.value)
                {
                    case "星期一":
                    case "Monday": weekday = 1; break;
                    case "星期二":
                    case "Tuesday": weekday = 2; break;
                    case "星期三":
                    case "Wednesday": weekday = 3; break;
                    case "星期四":
                    case "Thursday": weekday = 4; break;
                    case "星期五":
                    case "Friday": weekday = 5; break;
                    case "星期六":
                    case "Saturday": weekday = 6; break;
                    case "星期日":
                    case "Sunday": weekday = 7; break;
                }

                dataPacket = new byte[5];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00; // 包序号（仅一个包）
                dataPacket[2] = data.Weekday.type;
                dataPacket[3] = 1; // 固定1字节长度
                dataPacket[4] = (byte)weekday;
                Logger.LogInfo($"Weekday: {data.Weekday.value}");
                SendByteData(dataPacket);
            }
            //cpu
            if (data.CPUTemperature.show)
            {
                dataPacket = new byte[7];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CPUTemperature.type;
                dataPacket[3] = 3;
                dataPacket[4] = temperatureCelsiusCode;
                BitConverter.GetBytes((short)(data.CPUTemperature.value.Value * 100)).CopyTo(dataPacket, 5);
                Logger.LogInfo($"CPU温度: {data.CPUTemperature.value}");
                SendByteData(dataPacket);
            }
            if (data.CPUUsage.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CPUUsage.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.CPUUsage.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"CPU使用率: {data.CPUUsage.value}");
                SendByteData(dataPacket);
            }
            if (data.CPUPower.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CPUPower.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.CPUPower.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"CPU功率: {data.CPUPower.value}");
                SendByteData(dataPacket);
            }
            if (data.CPUFanSpeed.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CPUFanSpeed.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.CPUFanSpeed.value.Value)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"CPU风扇转速: {data.CPUFanSpeed.value}");
                SendByteData(dataPacket);
            }
            if (data.CPUModel.show)
            {
                byte[] bytes = Encoding.UTF8.GetBytes(data.CPUModel.value);
                int maxPayloadSize = 64 - 4; // 每个包的有效载荷最大长度（64 - 4字节包头）

                if (bytes.Length > maxPayloadSize)
                {
                    // 计算需要拆分成多少个包
                    int totalPackets = (int)Math.Ceiling((double)bytes.Length / maxPayloadSize);

                    for (int packetIndex = 0; packetIndex < totalPackets; packetIndex++)
                    {
                        // 计算当前包的载荷起始位置和长度
                        int payloadStart = packetIndex * maxPayloadSize;
                        int payloadLength = Math.Min(maxPayloadSize, bytes.Length - payloadStart);

                        // 构造数据包（包头 + 载荷）
                        dataPacket = new byte[payloadLength + 4];
                        dataPacket[0] = 0x00;
                        dataPacket[1] = (byte)packetIndex; // 包序号（0x00, 0x01, ...）
                        dataPacket[2] = data.CPUModel.type;
                        dataPacket[3] = (byte)payloadLength;

                        // 复制当前包的载荷数据
                        Buffer.BlockCopy(bytes, payloadStart, dataPacket, 4, payloadLength);

                        // 发送当前包
                        Logger.LogInfo($"CPU型号: {data.CPUModel.value}");
                        SendByteData(dataPacket);
                    }
                }
                else
                {
                    // 数据长度 <= 61 字节，直接发送
                    dataPacket = new byte[bytes.Length + 4];
                    dataPacket[0] = 0x00;
                    dataPacket[1] = 0x00; // 包序号（仅一个包）
                    dataPacket[2] = data.CPUModel.type;
                    dataPacket[3] = (byte)bytes.Length;
                    Buffer.BlockCopy(bytes, 0, dataPacket, 4, bytes.Length);
                    Logger.LogInfo($"CPU型号: {data.CPUModel.value}");
                    SendByteData(dataPacket);
                }
            }

            if (data.GPUTemperature.show)
            {
                dataPacket = new byte[7];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.GPUTemperature.type;
                dataPacket[3] = 3;
                dataPacket[4] = temperatureCelsiusCode;
                BitConverter.GetBytes((short)(data.GPUTemperature.value.Value * 100)).CopyTo(dataPacket, 5);
                Logger.LogInfo($"GPU温度: {data.GPUTemperature.value}");
                SendByteData(dataPacket);
            }
            if (data.GPUMemoryUsage.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.GPUMemoryUsage.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.GPUMemoryUsage.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"GPU内存使用率: {data.GPUMemoryUsage.value}");
                SendByteData(dataPacket);
            }
            if (data.GPUPower.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.GPUPower.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.GPUPower.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"GPU功率: {data.GPUPower.value}");
                SendByteData(dataPacket);
            }
            if (data.GPUModel.show)
            {
                byte[] bytes = Encoding.UTF8.GetBytes(data.GPUModel.value);
                int maxPayloadSize = 64 - 4; // 每个包的有效载荷最大长度（64 - 4字节包头）

                if (bytes.Length > maxPayloadSize)
                {
                    // 计算需要拆分成多少个包
                    int totalPackets = (int)Math.Ceiling((double)bytes.Length / maxPayloadSize);

                    for (int packetIndex = 0; packetIndex < totalPackets; packetIndex++)
                    {
                        // 计算当前包的载荷起始位置和长度
                        int payloadStart = packetIndex * maxPayloadSize;
                        int payloadLength = Math.Min(maxPayloadSize, bytes.Length - payloadStart);

                        // 构造数据包（包头 + 载荷）
                        dataPacket = new byte[payloadLength + 4];
                        dataPacket[0] = 0x00;
                        dataPacket[1] = (byte)packetIndex; // 包序号（0x00, 0x01, ...）
                        dataPacket[2] = data.GPUModel.type;
                        dataPacket[3] = (byte)payloadLength;

                        // 复制当前包的载荷数据
                        Buffer.BlockCopy(bytes, payloadStart, dataPacket, 4, payloadLength);

                        // 发送当前包
                        Logger.LogInfo($"GPU型号: {data.GPUModel.value}");
                        SendByteData(dataPacket);
                    }
                }
                else
                {
                    // 数据长度 <= 61 字节，直接发送
                    dataPacket = new byte[bytes.Length + 4];
                    dataPacket[0] = 0x00;
                    dataPacket[1] = 0x00; // 包序号（仅一个包）
                    dataPacket[2] = data.GPUModel.type;
                    dataPacket[3] = (byte)bytes.Length;
                    Buffer.BlockCopy(bytes, 0, dataPacket, 4, bytes.Length);
                    Logger.LogInfo($"GPU型号: {data.GPUModel.value}");
                    SendByteData(dataPacket);
                }
            }

            if (data.RAMUsage.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.RAMUsage.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.RAMUsage.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"RAM使用率: {data.RAMUsage.value}");
                SendByteData(dataPacket);
            }
            if (data.AvailableRAM.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.AvailableRAM.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.AvailableRAM.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"可用RAM: {data.AvailableRAM.value}");
                SendByteData(dataPacket);
            }
            if (data.CaseFan1Speed.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CaseFan1Speed.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.CaseFan1Speed.value.Value)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"机箱风扇1转速: {data.CaseFan1Speed.value}");
                SendByteData(dataPacket);
            }
            if (data.CaseFan2Speed.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.CaseFan2Speed.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.CaseFan2Speed.value.Value)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"机箱风扇2转速: {data.CaseFan2Speed.value}");
                SendByteData(dataPacket);
            }
            if (data.HDDTemperature.show)
            {
                dataPacket = new byte[7];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.HDDTemperature.type;
                dataPacket[3] = 3;
                dataPacket[4] = temperatureCelsiusCode;
                BitConverter.GetBytes((short)(data.HDDTemperature.value.Value * 100)).CopyTo(dataPacket, 5);
                Logger.LogInfo($"HDD温度: {data.HDDTemperature.value}");
                SendByteData(dataPacket);
            }
            if (data.HDDUsage.show)
            {
                dataPacket = new byte[6];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.HDDUsage.type;
                dataPacket[3] = 2;
                BitConverter.GetBytes((short)(data.HDDUsage.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"HDD使用率: {data.HDDUsage.value}");
                SendByteData(dataPacket);
            }
            if (data.UploadSpeed.show)
            {
                dataPacket = new byte[8];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.UploadSpeed.type;
                dataPacket[3] = 4;
                BitConverter.GetBytes((int)Convert.ToInt32(data.UploadSpeed.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"上传速度: {data.UploadSpeed.value}");
                SendByteData(dataPacket);
            }
            if (data.DownloadSpeed.show)
            {
                dataPacket = new byte[8];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00;
                dataPacket[2] = data.DownloadSpeed.type;
                dataPacket[3] = 4;
                BitConverter.GetBytes((int)Convert.ToInt32(data.DownloadSpeed.value.Value * 100)).CopyTo(dataPacket, 4);
                Logger.LogInfo($"下载速度: {data.DownloadSpeed.value}");
                SendByteData(dataPacket);
            }
        }

        public void SendCustomStr(string customStr)
        {
            if(customStr == null){
                customStr = "";
            }
            byte[] dataPacket;
            byte type = 0x70;
            byte[] bytes = Encoding.UTF8.GetBytes(customStr);
            int maxPayloadSize = 64 - 4; // 每个包的有效载荷最大长度（64 - 4字节包头）

            if (bytes.Length > maxPayloadSize)
            {
                // 计算需要拆分成多少个包
                int totalPackets = (int)Math.Ceiling((double)bytes.Length / maxPayloadSize);

                for (int packetIndex = 0; packetIndex < totalPackets; packetIndex++)
                {
                    // 计算当前包的载荷起始位置和长度
                    int payloadStart = packetIndex * maxPayloadSize;
                    int payloadLength = Math.Min(maxPayloadSize, bytes.Length - payloadStart);

                    // 构造数据包（包头 + 载荷）
                    dataPacket = new byte[payloadLength + 4];
                    dataPacket[0] = 0x00;
                    dataPacket[1] = (byte)packetIndex; // 包序号（0x00, 0x01, ...）
                    dataPacket[2] = type;
                    dataPacket[3] = (byte)payloadLength;

                    // 复制当前包的载荷数据
                    Buffer.BlockCopy(bytes, payloadStart, dataPacket, 4, payloadLength);

                    // 发送当前包
                    Logger.LogInfo($"自定义字符串: {customStr}");
                    SendByteData(dataPacket);
                }
            }
            else
            {
                // 数据长度 <= 61 字节，直接发送
                dataPacket = new byte[bytes.Length + 4];
                dataPacket[0] = 0x00;
                dataPacket[1] = 0x00; // 包序号（仅一个包）
                dataPacket[2] = type;
                dataPacket[3] = (byte)bytes.Length;
                Buffer.BlockCopy(bytes, 0, dataPacket, 4, bytes.Length);
                Logger.LogInfo($"自定义字符串: {customStr}");
                SendByteData(dataPacket);
            }
        }

        /**
         * 数据格式：0x00 + FrameID + Type + Length + Value
         * 当长度超过64位时，分次发送，FrameID作为包序号
         * Type代表不同的数据
         * Lenght代表Value的长度
         * 温度类型的数据Value前一位为温度制式
         */

        public void SendByteData(byte[] dataPacket)
        {
            string hex = BitConverter.ToString(dataPacket);
            if (device != null && device.IsConnected)
            {
                try
                {
                    bool writeSuccess = device.Write(dataPacket);

                    if (writeSuccess)
                    {
                        Logger.LogInfo($"Data written successfully: {hex}");
                        // 间隔50ms
                        Thread.Sleep(50);
                    }
                    else
                    {
                        Logger.LogInfo($"Failed to write data: {hex}");

                        if (!device.IsConnected)
                        {
                            OpenDevice();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogInfo($"Failed to write data: {hex}");
                    LoggerExtensions.LogExceptionWithCaller(ex, "Failed to write data");
                }
            }
            else
            {
                Logger.LogInfo($"Failed to write data: {hex}");
                OpenDevice();
            }
        }
    }
}
