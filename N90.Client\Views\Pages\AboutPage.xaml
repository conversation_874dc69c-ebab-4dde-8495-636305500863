<Page x:Class="N90.Client.Views.Pages.AboutPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="About"
      Background="Transparent"
      Width="Auto"
      Height="Auto">

    <Page.Resources>
        <!-- 文本样式 -->
        <Style x:Key="AboutTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontFamily" Value="Segoe UI, Microsoft YaHei UI, Arial, sans-serif"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="24"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
            <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        </Style>
    </Page.Resources>

    <Grid>
        <!-- 背景图片 - 限定在上半部分 -->
        <Image Source="/Resources/Images/about.png"
               Stretch="Uniform"
               Opacity="0.8"
               MaxWidth="800"
               MaxHeight="400"
               HorizontalAlignment="Center"
               VerticalAlignment="Top"
               Margin="20,25,20,0"/>

        <!-- 内容区域 - 覆盖整个页面 -->
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="350"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 背景图片区域内的标题 -->
            <TextBlock Grid.Row="0"
                       Text="BRAND STORY"
                       FontSize="25"
                       FontFamily="pack://application:,,,/Resources/Fonts/Roboto-Italic_0"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Top"
                       Margin="0,312,0,0">
            </TextBlock>

            <!-- 版权信息 - 右上角左对齐 -->
            <StackPanel Grid.Row="0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Margin="0,20,80,0">
                <TextBlock Text="Software Version: 2025/06/09"
                           FontSize="12"
                           FontFamily="pack://application:,,,/Resources/Fonts/Roboto-Thin_0"
                           Foreground="#FFFFFF"
                           TextAlignment="Left"
                           >
                </TextBlock>

            </StackPanel>

            <!-- 背景下方的详细内容 -->
            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        Margin="0,0,0,0">

                <TextBlock FontSize="10.5"
                           FontFamily="pack://application:,,,/Resources/Fonts/Roboto-Thin_0"
                           Foreground="White"
                           TextWrapping="NoWrap"
                           LineHeight="10"
                           Margin="0,0,0,0"
                           HorizontalAlignment="Center"
                           TextAlignment="Center">
                    <Run Text="Founded in 2013, GameMax began its journey with a clear vision: to redefine gaming hardware by delivering the best gaming experience through high-performance and good-quality products."/><LineBreak/>
                    <Run Text="From the very beginning, the lion in our logo symbolized strength, courage, and an untamed spirit—a reflection of our relentless pursuit of maximum performance and innovation."/><LineBreak/>
                    <Run Text="Over the years, the brand has faced challenges and uncertainties, but like the king of beasts, we persevered, adapting and evolving with each step."/><LineBreak/>
                
                </TextBlock>

                <TextBlock FontSize="10.5"
                           FontFamily="pack://application:,,,/Resources/Fonts/Roboto-Thin_0"
                           Foreground="White"
                           TextWrapping="NoWrap"
                           LineHeight="10"
                           Margin="0,0,0,0"
                           HorizontalAlignment="Center"
                           TextAlignment="Center">
                    <Run Text="The global pandemic marked a turning point, as a new generation—Gen Z—rose to become the dominant force in the gaming community."/><LineBreak/>
                    <Run Text="With distinct preferences, a passion for self-expression, a prioritization on convenience, and a demand for both style and substance, they reshaped the landscape of gaming. "/><LineBreak/>
                    <Run Text="Recognizing this shift, GameMax made a bold decision in 2024: to transform, evolve, and roar back into the industry stronger than ever."/><LineBreak/>
                    
                </TextBlock>

                <TextBlock FontSize="10.5"
                           FontFamily="pack://application:,,,/Resources/Fonts/Roboto-Thin_0"
                           Foreground="White"
                           TextWrapping="NoWrap"
                           LineHeight="10"
                           Margin="0,0,0,0"
                           HorizontalAlignment="Center"
                           TextAlignment="Center">
                    <Run Text="The rebirth of the lion in our logo brings more powerful and eye-catching than before, the new lion represents our renewed commitment to lead the charge into a new era."/><LineBreak/>
                    <Run Text="This transformation embodies our new core values: to deliver 'affordable luxury' by merging cutting-edge technology with the latest trends for Gen Z gamers; "/><LineBreak/>
                    <Run Text="to offer both pre-configured PC relevant solutions and an enhanced assembly experience, "/><LineBreak/>
                    <Run Text="catering to users who value convenience as well as those who enjoy the creative process of building their own gears; and to drive innovation by focusing on what users truly need."/>
                    
                </TextBlock>
                
            </StackPanel>

        </Grid>

    </Grid>
</Page>
