@echo off
echo Testing Display Preferences Service...
echo.

REM 检查配置文件位置
set "CONFIG_PATH=%PROGRAMDATA%\N90\display_preferences.json"
echo Configuration file path: %CONFIG_PATH%

REM 检查目录是否存在
if exist "%PROGRAMDATA%\N90" (
    echo N90 data directory exists
) else (
    echo N90 data directory does not exist
)

REM 检查配置文件是否存在
if exist "%CONFIG_PATH%" (
    echo Configuration file exists
    echo.
    echo Current configuration:
    type "%CONFIG_PATH%"
) else (
    echo Configuration file does not exist
)

echo.
echo This configuration file will be accessible by both:
echo - N90 Service (running as Windows Service)
echo - N90 Client (running as user application)
echo.
echo The service will no longer depend on user registry access.
echo.
pause
