using System;
using System.Linq;
using System.Windows;

namespace N90.Client
{
    public static class LanguageHelper
    {
        public static void SwitchLanguage(string lang)
        {
            var dict = new ResourceDictionary();
            switch (lang)
            {
                case "zh-CN":
                    dict.Source = new Uri("Resources/Strings.zh-CN.xaml", UriKind.Relative);
                    break;
                default:
                    dict.Source = new Uri("Resources/Strings.en-US.xaml", UriKind.Relative);
                    break;
            }
            var oldDict = Application.Current.Resources.MergedDictionaries
                .FirstOrDefault(d => d.Source != null && d.Source.OriginalString.Contains("Strings."));
            if (oldDict != null)
                Application.Current.Resources.MergedDictionaries.Remove(oldDict);
            Application.Current.Resources.MergedDictionaries.Add(dict);
        }
    }
}
