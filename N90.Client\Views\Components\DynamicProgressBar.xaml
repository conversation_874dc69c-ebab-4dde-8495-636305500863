<UserControl x:Class="N90.Client.Views.Components.DynamicProgressBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 进度条背景样式 -->
        <Style x:Key="ProgressBarBackgroundStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF333333"/>
            <Setter Property="Height" Value="8"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <!-- 进度条前景样式 -->
        <Style x:Key="ProgressBarForegroundStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#B8B8B8" Offset="0"/>
                        <GradientStop Color="#B8B8B8" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Height" Value="8"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <!-- 数据标签样式 -->
        <Style x:Key="DataLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFAAAAAA"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 数据值样式 -->
        <Style x:Key="DataValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 进度条区域 -->
        <Grid Grid.Row="0">
            <!-- 背景条 -->
            <Border x:Name="BackgroundBar" Style="{StaticResource ProgressBarBackgroundStyle}"/>
            
            <!-- 进度条 -->
            <Border x:Name="ProgressBar" Style="{StaticResource ProgressBarForegroundStyle}"/>
        </Grid>

        <!-- 标签和值区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" 
                       x:Name="LabelText" 
                       Text="LABEL" 
                       Style="{StaticResource DataLabelStyle}"/>
            
            <TextBlock Grid.Column="1" 
                       x:Name="ValueText" 
                       Text="0" 
                       Style="{StaticResource DataValueStyle}"/>
        </Grid>
    </Grid>
</UserControl>
