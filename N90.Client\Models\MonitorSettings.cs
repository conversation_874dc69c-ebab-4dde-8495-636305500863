using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace N90.Client.Models
{
    public class MonitorSettings
    {
        public List<string> SelectedSensors { get; set; } = new List<string>();
        public Dictionary<string, string> SensorAliases { get; set; } = new Dictionary<string, string>();
        public bool AutoDiscoverOnStartup { get; set; } = true;

        // GPU设置
        public string SelectedGpuSensor { get; set; } = string.Empty;

        // CPU传感器设置
        public string SelectedCpuTempSensor { get; set; } = string.Empty;
        public string SelectedCpuVoltageSensor { get; set; } = string.Empty;
        public string SelectedCpuUsageSensor { get; set; } = string.Empty;

        // 网络接口设置
        public string SelectedNetworkInterface { get; set; } = string.Empty;

        // 风扇传感器设置
        public string SelectedCpuFanSensor { get; set; } = string.Empty;
        public string SelectedPumpFanSensor { get; set; } = string.Empty;
        public string SelectedCaseFan1Sensor { get; set; } = string.Empty;
        public string SelectedCaseFan2Sensor { get; set; } = string.Empty;

        private static readonly string SettingsPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "N90",
            "monitor_settings.json");

        public static MonitorSettings Load()
        {
            try
            {
                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    return JsonSerializer.Deserialize<MonitorSettings>(json) ?? new MonitorSettings();
                }
            }
            catch
            {
                // 如果加载失败，返回默认设置
            }
            return new MonitorSettings();
        }

        public void Save()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(this, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(SettingsPath, json);
            }
            catch
            {
                // 保存失败时静默处理
            }
        }
    }
}