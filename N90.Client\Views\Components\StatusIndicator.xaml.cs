using System;
using System.Windows;
using System.Windows.Controls;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// StatusIndicator.xaml 的交互逻辑
    /// 可点击的状态指示器组件，类似复选框的功能
    /// </summary>
    public partial class StatusIndicator : UserControl
    {
        /// <summary>
        /// 是否选中的依赖属性
        /// </summary>
        public static readonly DependencyProperty IsCheckedProperty =
            DependencyProperty.Register(
                nameof(IsChecked),
                typeof(bool),
                typeof(StatusIndicator),
                new PropertyMetadata(false, OnIsCheckedChanged));

        /// <summary>
        /// 数据类型的依赖属性，用于标识这是哪个数据项的状态指示器
        /// </summary>
        public static readonly DependencyProperty DataTypeProperty =
            DependencyProperty.Register(
                nameof(DataType),
                typeof(string),
                typeof(StatusIndicator),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否禁用自动加载的依赖属性，用于Settings页面等不需要从HardwareDataManager加载状态的场景
        /// </summary>
        public static readonly DependencyProperty DisableAutoLoadProperty =
            DependencyProperty.Register(
                nameof(DisableAutoLoad),
                typeof(bool),
                typeof(StatusIndicator),
                new PropertyMetadata(false));

        /// <summary>
        /// 状态改变事件
        /// </summary>
        public event EventHandler<StatusChangedEventArgs>? StatusChanged;

        public StatusIndicator()
        {
            InitializeComponent();
            Loaded += StatusIndicator_Loaded;
        }

        /// <summary>
        /// 组件加载完成时初始化状态
        /// </summary>
        private void StatusIndicator_Loaded(object sender, RoutedEventArgs e)
        {
            // 如果禁用自动加载，则不从HardwareDataManager获取状态
            if (DisableAutoLoad)
            {
                return;
            }

            // 从HardwareDataManager获取初始状态
            if (!string.IsNullOrEmpty(DataType))
            {
                // 将N90Page的数据类型名称映射到MainWindow的命名约定
                string mappedDataType = MapDataTypeToMainWindowConvention(DataType);
                IsChecked = N90.Client.Services.HardwareDataManager.Instance.GetUserShowPreference(mappedDataType);
            }
        }

        /// <summary>
        /// 将N90Page的数据类型名称映射到MainWindow的命名约定
        /// </summary>
        /// <param name="n90PageDataType">N90Page使用的数据类型名称</param>
        /// <returns>MainWindow使用的数据类型名称</returns>
        private string MapDataTypeToMainWindowConvention(string n90PageDataType)
        {
            return n90PageDataType switch
            {
                // 直接映射的数据类型
                "Date" => "Date",
                "Time" => "Time",
                "Weekday" => "Weekday",
                "CPUModel" => "CPUModel",
                "GPUModel" => "GPUModel",
                "CustomString" => "CustomString",
                "CPUTemperature" => "CPUTemperature",
                "CPUUsage" => "CPUUsage",
                "CPUPower" => "CPUPower",
                "CPUFanSpeed" => "CPUFanSpeed",
                "GPUTemperature" => "GPUTemperature",
                "GPUMemoryUsage" => "GPUMemoryUsage",
                "GPUPower" => "GPUPower",
                "RAMUsage" => "RAMUsage",
                "CaseFan1Speed" => "CaseFan1Speed",
                "CaseFan2Speed" => "CaseFan2Speed",

                // 需要映射的数据类型
                "UsedRAM" => "AvailableRAM",
                "DriveTemperature" => "HDDTemperature",
                "DriveUsage" => "HDDUsage",
                "NetworkUploadSpeed" => "UploadSpeed",
                "NetworkDownloadSpeed" => "DownloadSpeed",

                // 默认返回原名称
                _ => n90PageDataType
            };
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsChecked
        {
            get => (bool)GetValue(IsCheckedProperty);
            set => SetValue(IsCheckedProperty, value);
        }

        /// <summary>
        /// 数据类型标识
        /// </summary>
        public string DataType
        {
            get => (string)GetValue(DataTypeProperty);
            set => SetValue(DataTypeProperty, value);
        }

        /// <summary>
        /// 是否禁用自动加载
        /// </summary>
        public bool DisableAutoLoad
        {
            get => (bool)GetValue(DisableAutoLoadProperty);
            set => SetValue(DisableAutoLoadProperty, value);
        }

        /// <summary>
        /// IsChecked属性变化时的回调
        /// </summary>
        private static void OnIsCheckedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is StatusIndicator indicator)
            {
                // 可以在这里添加状态变化时的额外逻辑
            }
        }

        /// <summary>
        /// 按钮点击事件处理
        /// </summary>
        private void IndicatorButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换选中状态
            IsChecked = !IsChecked;
            
            // 触发状态改变事件
            StatusChanged?.Invoke(this, new StatusChangedEventArgs(DataType, IsChecked));
        }
    }

    /// <summary>
    /// 状态改变事件参数
    /// </summary>
    public class StatusChangedEventArgs : EventArgs
    {
        public string DataType { get; }
        public bool IsChecked { get; }

        public StatusChangedEventArgs(string dataType, bool isChecked)
        {
            DataType = dataType;
            IsChecked = isChecked;
        }
    }
}
