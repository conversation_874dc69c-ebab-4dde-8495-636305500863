using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace N90.Shared.Services
{
    /// <summary>
    /// 显示偏好服务 - 使用JSON配置文件替代注册表
    /// 解决Windows服务无法访问用户注册表的问题
    /// </summary>
    public class DisplayPreferencesService
    {
        private readonly string _configFilePath;
        private Dictionary<string, bool> _preferences;

        public DisplayPreferencesService()
        {
            // 使用应用程序数据目录，确保服务和客户端都能访问
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var n90DataPath = Path.Combine(appDataPath, "N90");

            // 确保目录存在
            Directory.CreateDirectory(n90DataPath);

            _configFilePath = Path.Combine(n90DataPath, "display_preferences.json");
            _preferences = new Dictionary<string, bool>();

            LoadPreferences();

            // 如果配置文件不存在但有注册表设置，进行迁移
            if (!File.Exists(_configFilePath) && RegistryMigrationService.ShouldMigrate())
            {
                Logger.LogInfo("Migrating display preferences from registry to configuration file...");
                RegistryMigrationService.MigrateDisplayPreferences(this);
            }
        }

        /// <summary>
        /// 获取显示偏好
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>是否显示，默认为true</returns>
        public bool GetShowPreference(string dataType)
        {
            return _preferences.TryGetValue(dataType, out bool show) ? show : true;
        }

        /// <summary>
        /// 设置显示偏好
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="show">是否显示</param>
        public void SetShowPreference(string dataType, bool show)
        {
            _preferences[dataType] = show;
            SavePreferences();
        }

        /// <summary>
        /// 批量设置显示偏好
        /// </summary>
        /// <param name="preferences">偏好设置字典</param>
        public void SetShowPreferences(Dictionary<string, bool> preferences)
        {
            foreach (var kvp in preferences)
            {
                _preferences[kvp.Key] = kvp.Value;
            }
            SavePreferences();
        }

        /// <summary>
        /// 获取所有显示偏好
        /// </summary>
        /// <returns>所有偏好设置</returns>
        public Dictionary<string, bool> GetAllPreferences()
        {
            return new Dictionary<string, bool>(_preferences);
        }

        /// <summary>
        /// 从文件加载偏好设置
        /// </summary>
        private void LoadPreferences()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var loadedPreferences = JsonSerializer.Deserialize<Dictionary<string, bool>>(json);
                    
                    if (loadedPreferences != null)
                    {
                        _preferences = loadedPreferences;
                        Logger.LogInfo($"Loaded {_preferences.Count} display preferences from {_configFilePath}");
                    }
                }
                else
                {
                    // 如果文件不存在，设置默认值
                    SetDefaultPreferences();
                    Logger.LogInfo($"Created default display preferences at {_configFilePath}");
                }
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error loading display preferences: {ex.Message}");
                SetDefaultPreferences();
            }
        }

        /// <summary>
        /// 保存偏好设置到文件
        /// </summary>
        private void SavePreferences()
        {
            try
            {
                var json = JsonSerializer.Serialize(_preferences, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(_configFilePath, json);
                Logger.LogInfo($"Saved display preferences to {_configFilePath}");
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error saving display preferences: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置默认偏好（全部显示）
        /// </summary>
        private void SetDefaultPreferences()
        {
            var defaultDataTypes = new[]
            {
                "Date", "Time", "Weekday", "CPUModel", "GPUModel", "CustomString",
                "CPUTemperature", "CPUUsage", "CPUPower", "CPUFanSpeed",
                "GPUTemperature", "GPUMemoryUsage", "GPUPower",
                "RAMUsage", "AvailableRAM",
                "HDDTemperature", "HDDUsage",
                "CaseFan1Speed", "CaseFan2Speed",
                "UploadSpeed", "DownloadSpeed"
            };

            _preferences.Clear();
            foreach (var dataType in defaultDataTypes)
            {
                _preferences[dataType] = true;
            }
            
            SavePreferences();
        }

        /// <summary>
        /// 重新加载偏好设置（用于响应外部更改）
        /// </summary>
        public void ReloadPreferences()
        {
            LoadPreferences();
        }
    }
}
