﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>..\favicon.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AssemblyName>N90</AssemblyName>
    <AssemblyTitle>N90 Hardware Monitor</AssemblyTitle>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LibreHardwareMonitorLib" Version="0.9.4" />
    <PackageReference Include="System.Windows.Forms" Version="4.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\N90.Shared\N90.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Images\**\*.png" />
    <Resource Include="Resources\Images\**\*.jpg" />
    <Resource Include="Resources\Images\**\*.ico" />
  </ItemGroup>

</Project>
