using LibreHardwareMonitor.Hardware;
using System;
using System.Collections.Generic;
using System.Linq;

namespace N90.Client.Services
{
    public class HardwareMonitorService
    {
        private static HardwareMonitorService _instance;
        private static readonly object _lock = new object();

        private Computer computer;
        private Dictionary<string, Dictionary<string, List<SensorInfo>>> sensorData;
        private bool _isInitialized = false;
        private bool _sensorsDiscovered = false;

        public static HardwareMonitorService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new HardwareMonitorService();
                        }
                    }
                }
                return _instance;
            }
        }

        private HardwareMonitorService()
        {
            // 不在构造函数中初始化硬件，改为延迟初始化
        }

        public async Task InitializeAsync()
        {
            if (_isInitialized) return;

            await Task.Run(() =>
            {
                computer = new Computer
                {
                    IsCpuEnabled = true,
                    IsGpuEnabled = true,
                    IsMemoryEnabled = true,
                    IsMotherboardEnabled = true,
                    IsControllerEnabled = true,
                    IsNetworkEnabled = true,
                    IsStorageEnabled = true
                };
                computer.Open();
                _isInitialized = true;
            });
        }

        public async Task DiscoverSensorsAsync()
        {
            if (_sensorsDiscovered) return;

            await Task.Run(() =>
            {
                if (!_isInitialized)
                {
                    InitializeHardware();
                }
                DiscoverAllSensors();
                _sensorsDiscovered = true;
            });
        }

        private void InitializeHardware()
        {
            if (_isInitialized) return;

            computer = new Computer
            {
                IsCpuEnabled = true,
                IsGpuEnabled = true,
                IsMemoryEnabled = true,
                IsMotherboardEnabled = true,
                IsControllerEnabled = true,
                IsNetworkEnabled = true,
                IsStorageEnabled = true
            };
            computer.Open();
            _isInitialized = true;
        }

        public Dictionary<string, Dictionary<string, List<SensorInfo>>> GetAllAvailableSensors()
        {
            if (!_sensorsDiscovered)
            {
                if (!_isInitialized)
                {
                    InitializeHardware(); // 同步初始化作为后备
                }
                DiscoverAllSensors();
                _sensorsDiscovered = true;
            }
            return sensorData ?? new Dictionary<string, Dictionary<string, List<SensorInfo>>>();
        }

        public bool IsInitialized => _isInitialized;
        public bool SensorsDiscovered => _sensorsDiscovered;

        /// <summary>
        /// 检查服务是否完全准备就绪（硬件已初始化且传感器已发现）
        /// </summary>
        public bool IsReady => _isInitialized && _sensorsDiscovered;

        /// <summary>
        /// 重置传感器发现状态，强制重新扫描
        /// </summary>
        public void ResetSensorDiscovery()
        {
            _sensorsDiscovered = false;
            sensorData = null;
        }

        private void DiscoverAllSensors()
        {
            sensorData = new Dictionary<string, Dictionary<string, List<SensorInfo>>>();

            foreach (var hardware in computer.Hardware)
            {
                hardware.Update();
                string hardwareType = hardware.HardwareType.ToString();
                
                if (!sensorData.ContainsKey(hardwareType))
                    sensorData[hardwareType] = new Dictionary<string, List<SensorInfo>>();

                string hardwareName = hardware.Name;
                if (!sensorData[hardwareType].ContainsKey(hardwareName))
                    sensorData[hardwareType][hardwareName] = new List<SensorInfo>();

                // 添加硬件本身的传感器
                ProcessSensors(hardware.Sensors, hardwareName, sensorData[hardwareType][hardwareName]);

                // 添加子硬件的传感器
                foreach (var subHardware in hardware.SubHardware)
                {
                    subHardware.Update();
                    string subHardwareName = $"{hardwareName}_{subHardware.Name}";
                    if (!sensorData[hardwareType].ContainsKey(subHardwareName))
                        sensorData[hardwareType][subHardwareName] = new List<SensorInfo>();

                    ProcessSensors(subHardware.Sensors, subHardwareName, sensorData[hardwareType][subHardwareName]);
                }
            }
        }

        private void ProcessSensors(IEnumerable<ISensor> sensors, string hardwareName, List<SensorInfo> sensorList)
        {
            foreach (var sensor in sensors)
            {
                string sensorKey = $"{hardwareName}_{sensor.SensorType}_{sensor.Name}";
                sensorList.Add(new SensorInfo
                {
                    Key = sensorKey,
                    Name = sensor.Name,
                    Type = sensor.SensorType.ToString(),
                    HardwareName = hardwareName,
                    Sensor = sensor
                });
            }
        }

        public Dictionary<string, float> GetSelectedValues(List<string> selectedKeys)
        {
            var result = new Dictionary<string, float>();
            if (selectedKeys == null || selectedKeys.Count == 0)
                return result;

            // 更新所有硬件数据
            foreach (var hardware in computer.Hardware)
            {
                hardware.Update();
                foreach (var subHardware in hardware.SubHardware)
                {
                    subHardware.Update();
                }
            }

            // 收集选中的传感器值
            foreach (var hardwareType in sensorData)
            {
                foreach (var hardware in hardwareType.Value)
                {
                    foreach (var sensor in hardware.Value)
                    {
                        if (selectedKeys.Contains(sensor.Key) && sensor.Sensor.Value.HasValue)
                        {
                            result[sensor.Key] = sensor.Sensor.Value.Value;
                        }
                    }
                }
            }

            return result;
        }


        public void Close()
        {
            computer?.Close();
        }
    }

    public class SensorInfo
    {
        public string Key { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string HardwareName { get; set; }
        public ISensor Sensor { get; set; }
        public float? CurrentValue => Sensor?.Value;
    }
}