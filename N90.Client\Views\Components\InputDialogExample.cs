using N90.Client.Views.Components;
using System.Windows;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// InputDialog 使用示例
    /// </summary>
    public static class InputDialogExample
    {
        /// <summary>
        /// 显示输入对话框的示例方法
        /// </summary>
        public static void ShowInputDialog()
        {
            // 方法1：使用静态方法显示对话框
            InputDialog.Show(
                title: "请输入自定义字符串",
                defaultText: "Gaming",
                callback: (isConfirmed, inputText) =>
                {
                    if (isConfirmed)
                    {
                        MessageBox.Show($"您输入的内容是: {inputText}", "确认");
                        // 在这里处理用户输入的文本
                        // 例如：更新某个属性或保存到配置文件
                    }
                    else
                    {
                        MessageBox.Show("用户取消了输入", "取消");
                    }
                });
        }

        /// <summary>
        /// 在按钮点击事件中使用的示例
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public static void OnEditButtonClick(object sender, RoutedEventArgs e)
        {
            InputDialog.Show(
                title: "编辑自定义字符串",
                defaultText: "当前值",
                callback: (isConfirmed, inputText) =>
                {
                    if (isConfirmed && !string.IsNullOrWhiteSpace(inputText))
                    {
                        // 更新UI或数据
                        // 例如：viewModel.CustomStringValue = inputText;
                    }
                });
        }

        /// <summary>
        /// 用于配置文件编辑的示例
        /// </summary>
        public static void ShowConfigEditDialog()
        {
            InputDialog.Show(
                title: "编辑配置信息",
                defaultText: "请输入新的配置值...",
                callback: (isConfirmed, inputText) =>
                {
                    if (isConfirmed)
                    {
                        try
                        {
                            // 保存配置
                            // ConfigManager.SaveConfig(inputText);
                            MessageBox.Show("配置已保存", "成功");
                        }
                        catch (System.Exception ex)
                        {
                            MessageBox.Show($"保存失败: {ex.Message}", "错误");
                        }
                    }
                });
        }
    }
}
