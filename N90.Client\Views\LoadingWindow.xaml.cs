using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using N90.Client.Services;
using N90.Client.Models;
using N90.Shared.Services;

namespace N90.Client.Views
{
    public partial class LoadingWindow : Window
    {
        private readonly Progress<(int progress, string status)> _progressReporter;
        private readonly DispatcherTimer _loadingTextTimer;
        private int _loadingDotCount = 0;

        public LoadingWindow()
        {
            InitializeComponent();

            _progressReporter = new Progress<(int progress, string status)>(UpdateProgress);

            // Initialize loading text animation timer
            _loadingTextTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _loadingTextTimer.Tick += LoadingTextTimer_Tick;

            // Start animations
            Loaded += LoadingWindow_Loaded;
        }
        
        private void LoadingWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Start fade in animation
            var fadeInStoryboard = (Storyboard)FindResource("FadeInAnimation");
            fadeInStoryboard.Begin(this);

            // Start loading animation
            var loadingStoryboard = (Storyboard)FindResource("LoadingAnimation");
            loadingStoryboard.Begin();

            // Start logo glow animation
            var logoGlowStoryboard = (Storyboard)FindResource("LogoGlowAnimation");
            logoGlowStoryboard.Begin();

            // Start dynamic loading text timer
            _loadingTextTimer.Start();
        }
        
        public async Task StartLoadingProcess()
        {
            try
            {
                // 实际的加载任务，不再使用模拟延迟
                ((IProgress<(int progress, string status)>)_progressReporter).Report((10, "Initializing system..."));
                await Task.Delay(200); // 短暂延迟让UI更新

                ((IProgress<(int progress, string status)>)_progressReporter).Report((25, "Starting monitoring service..."));
                // Start service automatically (restored functionality from old MainWindow)
                await EnsureServiceRunning();

                ((IProgress<(int progress, string status)>)_progressReporter).Report((40, "Initializing hardware monitoring..."));
                // 预加载硬件监控服务
                await HardwareMonitorService.Instance.InitializeAsync();

                ((IProgress<(int progress, string status)>)_progressReporter).Report((60, "Discovering sensors..."));
                // 预加载传感器数据，这样后续页面可以直接使用缓存
                await HardwareMonitorService.Instance.DiscoverSensorsAsync();

                ((IProgress<(int progress, string status)>)_progressReporter).Report((80, "Loading application settings..."));
                // 预加载应用设置
                await Task.Run(() =>
                {
                    try
                    {
                        var settings = AppSettings.Load();
                        var tempService = new TemperatureUnitService();
                        tempService.ApplyTemperatureUnitSetting();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error preloading settings: {ex.Message}");
                    }
                });

                ((IProgress<(int progress, string status)>)_progressReporter).Report((90, "Preparing sensor configurations..."));
                // 预加载传感器UI配置，减少Settings页面首次加载时间
                await Task.Run(() =>
                {
                    try
                    {
                        // 预先验证传感器数据完整性
                        var monitorService = HardwareMonitorService.Instance;
                        if (monitorService.IsReady)
                        {
                            var sensorData = monitorService.GetAllAvailableSensors();
                            Console.WriteLine($"Preloaded {sensorData.Count} hardware types with sensors");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error preloading sensor configurations: {ex.Message}");
                    }
                });

                ((IProgress<(int progress, string status)>)_progressReporter).Report((95, "Preparing user interface..."));
                await Task.Delay(100);

                ((IProgress<(int progress, string status)>)_progressReporter).Report((100, "Ready!"));
                await Task.Delay(200);

                // Close loading window and show main window
                Dispatcher.Invoke(() =>
                {
                    var mainWindow = new ModernMainWindow();
                    mainWindow.Show();
                    this.Close();
                });
            }
            catch (Exception ex)
            {
                // Handle loading errors
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Loading failed: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Application.Current.Shutdown();
                });
            }
        }
        
        private async Task SimulateLoadingStage(string status, int startProgress, int endProgress)
        {
            // 简化的加载阶段，减少不必要的延迟
            ((IProgress<(int progress, string status)>)_progressReporter).Report((endProgress, status));
            await Task.Delay(50); // 最小延迟，让UI有时间更新
        }
        
        private void LoadingTextTimer_Tick(object? sender, EventArgs e)
        {
            _loadingDotCount = (_loadingDotCount + 1) % 4;

            string loadingText = "Loading";
            for (int i = 0; i < _loadingDotCount; i++)
            {
                loadingText += ".";
            }

            // 更新Loading TextBox的文字
            Loading.Text = loadingText;
        }

        private void UpdateProgress((int progress, string status) update)
        {
            // 由于当前XAML中没有进度条和状态文本，这里暂时不做任何操作
            // 如果需要显示进度，可以通过其他方式实现
        }
        
        public static async Task ShowLoadingAndStartApplication()
        {
            var loadingWindow = new LoadingWindow();
            loadingWindow.Show();
            
            // Start the loading process
            await loadingWindow.StartLoadingProcess();
        }

        private void TextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {

        }

        protected override void OnClosed(EventArgs e)
        {
            // Stop the timer when window is closed
            _loadingTextTimer?.Stop();
            base.OnClosed(e);
        }

        #region Service Management (Restored from old MainWindow)

        private System.Diagnostics.Process? serviceProcess;

        private async Task EnsureServiceRunning()
        {
            try
            {
                // Check if Windows Service is already running
                if (!IsWindowsServiceRunning())
                {
                    // Start Service in console mode only if Windows Service is not running
                    await StartService();

                    // Wait a moment for service to initialize
                    await Task.Delay(2000);
                }
                else
                {
                    Console.WriteLine("Windows Service is already running, skipping console service startup.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to ensure service running: {ex.Message}");
                // Don't throw - allow app to continue with JSON fallback
            }
        }

        private async Task StartService()
        {
            try
            {
                string serviceExePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "N90.Service.exe");
                if (!System.IO.File.Exists(serviceExePath))
                {
                    Console.WriteLine($"Service executable not found: {serviceExePath}");
                    return;
                }

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = serviceExePath,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = false,
                    RedirectStandardError = false
                };

                serviceProcess = System.Diagnostics.Process.Start(startInfo);
                if (serviceProcess == null)
                {
                    Console.WriteLine("Failed to start service process");
                    return;
                }

                // Wait a moment to ensure it started successfully
                await Task.Delay(1000);

                if (serviceProcess.HasExited)
                {
                    Console.WriteLine($"Service process exited immediately with code: {serviceProcess.ExitCode}");
                    return;
                }

                Console.WriteLine("Service started successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to start service: {ex.Message}");
            }
        }

        private bool IsWindowsServiceRunning()
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "query \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null) return false;

                process.WaitForExit();

                if (process.ExitCode == 0)
                {
                    string output = process.StandardOutput.ReadToEnd();
                    return output.Contains("RUNNING");
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check Windows service status: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
