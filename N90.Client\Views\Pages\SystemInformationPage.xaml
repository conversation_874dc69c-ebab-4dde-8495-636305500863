<Page x:Class="N90.Client.Views.Pages.SystemInformationPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:components="clr-namespace:N90.Client.Views.Components"
      Title="System Information"
      Background="Transparent"
      Width="930"
      Height="Auto"
      MaxWidth="930"
      MaxHeight="650">

    <Page.Resources>
        <!-- 硬件卡片样式 - 透明背景 -->
        <Style x:Key="HardwareCardStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0"/>
        </Style>

        <!-- 硬件标题样式 -->
        <Style x:Key="HardwareTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="15,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 硬件信息项样式 -->
        <Style x:Key="HardwareInfoItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="15,5,15,5"/>
        </Style>

        <!-- 硬件信息标签样式 -->
        <Style x:Key="HardwareInfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFAAAAAA"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 硬件信息值样式 -->
        <Style x:Key="HardwareInfoValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>



        <!-- 数据卡片样式 - 透明背景 -->
        <Style x:Key="DataCardStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- 数据标题样式 -->
        <Style x:Key="DataTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- 数据项样式 -->
        <Style x:Key="DataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,8,0,8"/>
        </Style>

        <!-- 带进度条的数据项样式 -->
        <Style x:Key="ProgressDataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,1,0,1"/>
            <Setter Property="Height" Value="33"/>
            <Setter Property="Width" Value="200"/>
        </Style>
        <Style x:Key="CPUProgressDataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,1,0,1"/>
            <Setter Property="Height" Value="33"/>
            <Setter Property="Width" Value="150"/>
        </Style>
        <Style x:Key="GPUProgressDataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,1,0,1"/>
            <Setter Property="Height" Value="33"/>
            <Setter Property="Width" Value="150"/>
        </Style>
        <Style x:Key="RAMProgressDataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,1,0,1"/>
            <Setter Property="Height" Value="33"/>
            <Setter Property="Width" Value="380"/>
        </Style>
        <Style x:Key="SYSTEMProgressDataItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,1,0,1"/>
            <Setter Property="Height" Value="33"/>
            <Setter Property="Width" Value="200"/>
        </Style>

        <!-- 进度条背景样式 -->
        <Style x:Key="ProgressBarBackgroundStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF333333"/>
            <Setter Property="Height" Value="8"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <!-- 进度条前景样式 -->
        <Style x:Key="ProgressBarForegroundStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#B8B8B8" Offset="0"/>
                        <GradientStop Color="#B8B8B8" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Height" Value="8"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <!-- 数据标签样式 -->
        <Style x:Key="DataLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#FFAAAAAA"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 数据值样式 -->
        <Style x:Key="DataValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <!-- 主要内容区域 -->
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 第一行：CPU和GPU -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- CPU卡片 -->
                    <Border Grid.Column="0" Style="{StaticResource HardwareCardStyle}" VerticalAlignment="Bottom">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- CPU环形进度 -->
                            <components:RingProgressControl Grid.Column="0"
                                                            x:Name="CpuRingProgress"
                                                            Value="59"
                                                            Label=""
                                                            ShowPercentSign="False"
                                                            Width="160"
                                                            Height="160"
                                                            VerticalAlignment="Bottom"
                                                            Margin="0,0,0,3"/>

                            <!-- CPU信息面板 -->
                            <StackPanel Grid.Column="2" VerticalAlignment="Bottom">
                                <TextBlock Text="CPU" Style="{StaticResource HardwareTitleStyle}" HorizontalAlignment="Left" Margin="0,0,0,10"/>

                                <!-- TEMP with dynamic progress bar -->
                                <Grid Style="{StaticResource CPUProgressDataItemStyle}" HorizontalAlignment="Left">
                                    <components:DynamicProgressBar x:Name="CpuTempProgressBar"
                                                                   DataType="CPUTemperature"
                                                                   Label="TEMP"
                                                                   Value="59"/>
                                </Grid>

                                <!-- CLOCK with dynamic progress bar -->
                                <Grid Style="{StaticResource CPUProgressDataItemStyle}" HorizontalAlignment="Left">
                                    <components:DynamicProgressBar x:Name="CpuClockProgressBar"
                                                                   DataType="CPUClock"
                                                                   Label="CLOCK"
                                                                   Value="1246"/>
                                </Grid>

                                <!-- FAN with dynamic progress bar -->
                                <Grid Style="{StaticResource CPUProgressDataItemStyle}" HorizontalAlignment="Left" Margin="0,1,0,15">
                                    <components:DynamicProgressBar x:Name="CpuFanProgressBar"
                                                                   DataType="CPUFan"
                                                                   Label="FAN"
                                                                   Value="0"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- GPU卡片 -->
                    <Border Grid.Column="2" Style="{StaticResource HardwareCardStyle}" VerticalAlignment="Bottom">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- GPU环形进度 -->
                            <components:RingProgressControl Grid.Column="0"
                                                            x:Name="GpuRingProgress"
                                                            Value="44"
                                                            Label=""
                                                            ShowPercentSign="False"
                                                            Width="160"
                                                            Height="160"
                                                            VerticalAlignment="Bottom"
                                                            Margin="0,0,0,3"/>

                            <!-- GPU信息面板 -->
                            <StackPanel Grid.Column="2" VerticalAlignment="Bottom">
                                <TextBlock Text="GPU" Style="{StaticResource HardwareTitleStyle}" HorizontalAlignment="Left" Margin="0,0,0,15"/>

                                <!-- TEMP with dynamic progress bar -->
                                <Grid Style="{StaticResource GPUProgressDataItemStyle}" HorizontalAlignment="Left">
                                    <components:DynamicProgressBar x:Name="GpuTempProgressBar"
                                                                   DataType="GPUTemperature"
                                                                   Label="TEMP"
                                                                   Value="44"/>
                                </Grid>

                                <!-- CLOCK with dynamic progress bar -->
                                <Grid Style="{StaticResource GPUProgressDataItemStyle}" HorizontalAlignment="Left">
                                    <components:DynamicProgressBar x:Name="GpuClockProgressBar"
                                                                   DataType="GPUClock"
                                                                   Label="CLOCK"
                                                                   Value="1531"/>
                                </Grid>

                                <!-- FAN with dynamic progress bar -->
                                <Grid Style="{StaticResource GPUProgressDataItemStyle}" HorizontalAlignment="Left" Margin="0,1,0,15">
                                    <components:DynamicProgressBar x:Name="GpuFanProgressBar"
                                                                   DataType="GPUFan"
                                                                   Label="FAN"
                                                                   Value="0"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- CPU装饰图片 -->
                    <Image Grid.Column="0"
                           Source="/Resources/Images/img6.png"
                           Stretch="Uniform"
                           MaxHeight="115"
                           VerticalAlignment="Top"
                           Margin="0,80,0,0"/>

                    <!-- GPU装饰图片 -->
                    <Image Grid.Column="2"
                           Source="/Resources/Images/img7.png"
                           Stretch="Uniform"
                           MaxHeight="115"
                           VerticalAlignment="Top"
                           Margin="0,80,0,0"/>
                </Grid>

                <!-- 第二行：RAM和SYSTEM -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="65*"/>
                        <ColumnDefinition Width="50"/>
                        <ColumnDefinition Width="35*"/>
                    </Grid.ColumnDefinitions>

                    <!-- RAM卡片 -->
                    <Border Grid.Column="0" Style="{StaticResource HardwareCardStyle}" VerticalAlignment="Bottom">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- RAM环形进度 -->
                            <components:RingProgressControl Grid.Column="0"
                                                            x:Name="RamRingProgress"
                                                            Value="25"
                                                            Label=""
                                                            ShowPercentSign="False"
                                                            Width="120"
                                                            Height="120"
                                                            VerticalAlignment="Bottom"
                                                            Margin="0,0,0,5"/>

                            <!-- RAM信息面板 -->
                            <StackPanel Grid.Column="2" VerticalAlignment="Bottom">
                                <TextBlock Text="RAM" Style="{StaticResource HardwareTitleStyle}" HorizontalAlignment="Left" Margin="0,0,0,15"/>

                                <!-- VALID with progress bar -->
                                <Grid Style="{StaticResource RAMProgressDataItemStyle}" HorizontalAlignment="Left">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid Grid.Row="0">
                                        <Border Style="{StaticResource ProgressBarBackgroundStyle}"/>
                                        <Border Style="{StaticResource ProgressBarForegroundStyle}" Width="160" x:Name="RamValidProgressBar"/>
                                    </Grid>
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="VALID" Style="{StaticResource HardwareInfoLabelStyle}"/>
                                        <TextBlock Grid.Column="1" x:Name="RamValidValue" Text="6346M" Style="{StaticResource HardwareInfoValueStyle}"/>
                                    </Grid>
                                </Grid>

                                <!-- USED with progress bar -->
                                <Grid Style="{StaticResource RAMProgressDataItemStyle}" HorizontalAlignment="Left" Margin="0,1,0,15">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid Grid.Row="0">
                                        <Border Style="{StaticResource ProgressBarBackgroundStyle}"/>
                                        <Border Style="{StaticResource ProgressBarForegroundStyle}" Width="200" x:Name="RamUsedProgressBar"/>
                                    </Grid>
                                    <Grid Grid.Row="1">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="USED" Style="{StaticResource HardwareInfoLabelStyle}"/>
                                        <TextBlock Grid.Column="1" x:Name="RamUsedValue" Text="9775M" Style="{StaticResource HardwareInfoValueStyle}"/>
                                    </Grid>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- SYSTEM卡片 -->
                    <Border Grid.Column="2" Style="{StaticResource HardwareCardStyle}" VerticalAlignment="Bottom">
                        <StackPanel VerticalAlignment="Bottom" Margin="14,0,0,14">
                            <TextBlock Text="SYSTEM" Style="{StaticResource HardwareTitleStyle}" HorizontalAlignment="Left" Margin="0,0,0,15"/>

                            <!-- C:/ with dynamic progress bar -->
                            <Grid Style="{StaticResource SYSTEMProgressDataItemStyle}" HorizontalAlignment="Left">
                                <components:DynamicProgressBar x:Name="SystemCDriveProgressBar"
                                                               DataType="CDriveUsage"
                                                               Label="C:/"
                                                               Value="39"/>
                            </Grid>

                            <!-- D:/ with dynamic progress bar -->
                            <Grid Style="{StaticResource SYSTEMProgressDataItemStyle}" HorizontalAlignment="Left">
                                <components:DynamicProgressBar x:Name="SystemDDriveProgressBar"
                                                               DataType="DDriveUsage"
                                                               Label="D:/"
                                                               Value="17"/>
                            </Grid>

                            <!-- E:/ with dynamic progress bar -->
                            <Grid Style="{StaticResource SYSTEMProgressDataItemStyle}" HorizontalAlignment="Left">
                                <components:DynamicProgressBar x:Name="SystemEDriveProgressBar"
                                                               DataType="EDriveUsage"
                                                               Label="E:/"
                                                               Value="0"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- RAM装饰图片 -->
                    <Image Grid.Column="0"
                           Source="/Resources/Images/img8.png"
                           Stretch="Uniform"
                           MaxHeight="110"
                           Width="500"
                           VerticalAlignment="Top"
                           Margin="0,80,-100,0"/>

                    <!-- SYSTEM装饰图片 -->
                    <Image Grid.Column="2"
                           Source="/Resources/Images/img9.png"
                            Stretch="Uniform"
                             MaxHeight="110"
                             VerticalAlignment="Top"
                           Margin="0,80,45,0"/>
                </Grid>

                <!-- 第三行：DATA和NETWORK -->
                <Grid Grid.Row="2"  Margin="0,10,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="7*"/>
                        <ColumnDefinition Width="3*"/>
                    </Grid.ColumnDefinitions>

                    <!-- DATA卡片 -->
                    <Border Grid.Row="0" Grid.Column="0" Style="{StaticResource DataCardStyle}" Margin="0,0,200,0">
                        <StackPanel>
                            <TextBlock Text="DATA" Style="{StaticResource DataTitleStyle}" Margin="0,0,0,15"/>

                            <!-- 2行2列布局 -->
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 第一行第一列：CPU PACKAGE 3W -->
                                <Grid Grid.Row="0" Grid.Column="0" Style="{StaticResource DataItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="CPU PACKAGE" Style="{StaticResource DataLabelStyle}"/>
                                    <TextBlock Grid.Column="1" x:Name="CpuPackageValue" Text="3W" Style="{StaticResource DataValueStyle}"/>
                                </Grid>

                                <!-- 第一行第二列：GPU PACKAGE 0.68W -->
                                <Grid Grid.Row="0" Grid.Column="2" Style="{StaticResource DataItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="GPU PACKAGE" Style="{StaticResource DataLabelStyle}"/>
                                    <TextBlock Grid.Column="1" x:Name="GpuPackageValue" Text="0W" Style="{StaticResource DataValueStyle}"/>
                                </Grid>

                                <!-- 第二行第一列：CPU VOLTAGE 0W -->
                                <Grid Grid.Row="1" Grid.Column="0" Style="{StaticResource DataItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="CPU VOLTAGE" Style="{StaticResource DataLabelStyle}"/>
                                    <TextBlock Grid.Column="1" x:Name="CpuVoltageValue" Text="0W" Style="{StaticResource DataValueStyle}"/>
                                </Grid>

                                <!-- 第二行第二列：GPU VOLTAGE -->
                                <Grid Grid.Row="1" Grid.Column="2" Style="{StaticResource DataItemStyle}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="GPU VOLTAGE" Style="{StaticResource DataLabelStyle}"/>
                                    <TextBlock Grid.Column="1" x:Name="GpuVoltageValue" Text="0V" Style="{StaticResource DataValueStyle}"/>
                                </Grid>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- NETWORK卡片 -->
                    <Border Grid.Row="0" Grid.Column="1" Style="{StaticResource DataCardStyle}" Margin="0,0,0,0">
                        <StackPanel>
                            <TextBlock Text="NET WORK" Style="{StaticResource DataTitleStyle}"/>

                            <Grid Style="{StaticResource DataItemStyle}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="UploadSpeedValue" Grid.Column="0" Text="UPLOAD : 0KB/s" Style="{StaticResource DataLabelStyle}"/>
                            </Grid>

                            <Grid Style="{StaticResource DataItemStyle}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock x:Name="DownloadSpeedValue" Grid.Column="0" Text="DOWNLOAD: 0KB/s" Style="{StaticResource DataLabelStyle}"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 装饰图片 - 跨越两列 -->
                    <Image Grid.Column="0" Grid.ColumnSpan="2"
                           Source="/Resources/Images/img5.png"
                           Stretch="Uniform"
                           MaxHeight="120"
                           VerticalAlignment="Top"
                           Margin="-30,10,10,0"/>
                </Grid>
            </Grid>
        </Grid>
    </ScrollViewer>
</Page>
