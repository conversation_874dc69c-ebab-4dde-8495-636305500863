<Window x:Class="N90.Client.Views.LoadingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="N90 System Monitor" 
        Height="600" Width="800"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Loading animation storyboard -->
        <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever"/>

        <!-- Fade in animation -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.8"/>
        </Storyboard>

        <!-- GAMEMAX logo glow effect -->
        <Storyboard x:Key="LogoGlowAnimation" RepeatBehavior="Forever"/>
    </Window.Resources>

    <Grid>
        <!-- 背景图片 - 保持原始比例 -->
        <Image Source="/Resources/Images/loading.png"
               Stretch="Uniform"
               HorizontalAlignment="Center"
               VerticalAlignment="Center"/>

        <!-- Loading文字 - 居中底部 -->
        <TextBox x:Name="Loading"
                 Text="Loading..."
                 FontSize="16"
                 FontFamily="Consolas, 'Courier New', monospace"
                 FontWeight="Medium"
                 Foreground="White"
                 Background="Transparent"
                 BorderThickness="0"
                 HorizontalAlignment="Left"
                 VerticalAlignment="Bottom"
                 Margin="46,0,0,182"
                 Width="150"
                 TextAlignment="Left"
                 IsReadOnly="True"
                 IsHitTestVisible="False"
                 IsTabStop="False"
                 Focusable="False"
                 TextChanged="TextBox_TextChanged"/>
        <!-- Main background -->

        <!-- Gradient overlay for depth -->

        <!-- Top-right corner accent -->


        <!-- GAMEMAX Logo -->

        <!-- Logo glow effect -->

        <!-- Main content container -->

        <!-- Progress bar -->

    </Grid>
</Window>
