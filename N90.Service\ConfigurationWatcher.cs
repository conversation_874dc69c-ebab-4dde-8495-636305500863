using System;
using System.IO;
using N90.Shared.Services;

namespace N90.Service
{
    /// <summary>
    /// 配置文件监控器 - 使用FileSystemWatcher实现实时配置更新
    /// </summary>
    public class ConfigurationWatcher : IDisposable
    {
        private readonly FileSystemWatcher _watcher;
        private readonly string _configPath;
        private readonly Action _onConfigChanged;
        private bool _disposed = false;

        public ConfigurationWatcher(string configPath, Action onConfigChanged)
        {
            _configPath = configPath;
            _onConfigChanged = onConfigChanged;
            
            // 确保配置目录存在
            var directory = Path.GetDirectoryName(configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 初始化文件监控器
            _watcher = new FileSystemWatcher(directory)
            {
                Filter = Path.GetFileName(configPath),
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            _watcher.Changed += OnConfigFileChanged;
            Logger.LogInfo($"Configuration watcher initialized for: {configPath}");
        }

        private void OnConfigFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 防止重复触发
                _watcher.EnableRaisingEvents = false;
                
                // 等待文件写入完成
                System.Threading.Thread.Sleep(100);
                
                Logger.LogInfo($"Configuration file changed: {e.FullPath}");
                _onConfigChanged?.Invoke();
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error handling config file change: {ex.Message}");
            }
            finally
            {
                _watcher.EnableRaisingEvents = true;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _watcher?.Dispose();
                _disposed = true;
                Logger.LogInfo("Configuration watcher disposed");
            }
        }
    }
}
