<UserControl x:Class="N90.Client.Views.Components.StatusIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="12" d:DesignWidth="12">
    <UserControl.Resources>
        <!-- 状态指示器按钮样式 -->
        <Style x:Key="StatusIndicatorButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Width="12" Height="12"
                                BorderBrush="#97D200"
                                BorderThickness="0.8"
                                Background="Transparent">
                            <Rectangle Width="5.5" Height="5.5"
                                       Fill="#97D200">
                                <Rectangle.Style>
                                    <Style TargetType="Rectangle">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Rectangle.Style>
                            </Rectangle>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <Button x:Name="IndicatorButton"
            Style="{StaticResource StatusIndicatorButtonStyle}"
            Click="IndicatorButton_Click"/>
</UserControl>
