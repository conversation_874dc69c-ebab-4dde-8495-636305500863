using System;
using System.Windows.Controls;
using System.Windows.Threading;
using N90.Shared;
using N90.Shared.Services;
using N90.Client.Models;
using N90.Client.Services;

namespace N90.Client.Views.Pages
{
    public partial class SystemInformationPage : Page
    {
        private readonly TemperatureUnitService _temperatureUnitService;
        private readonly SystemInfoDataService _systemInfoDataService;

        public SystemInformationPage()
        {
            InitializeComponent();

            _temperatureUnitService = new TemperatureUnitService();
            _temperatureUnitService.ApplyTemperatureUnitSetting();

            // 使用独立的系统信息数据服务
            _systemInfoDataService = SystemInfoDataService.Instance;
            _systemInfoDataService.DataUpdated += OnSystemInfoDataUpdated;

            // 启动独立的数据更新服务（每秒更新一次）
            _systemInfoDataService.StartDataUpdates(1000);

            // 初始化显示（使用默认最低值数据）
            var initialData = _systemInfoDataService.GetCurrentData();
            UpdateSystemInfoDisplay(initialData);
        }

        /// <summary>
        /// 系统信息数据更新事件处理
        /// </summary>
        private void OnSystemInfoDataUpdated(SystemInfoData data)
        {
            Dispatcher.Invoke(() => UpdateSystemInfoDisplay(data));
        }

        /// <summary>
        /// 更新系统信息显示
        /// </summary>
        private void UpdateSystemInfoDisplay(SystemInfoData data)
        {
            try
            {
                // 更新CPU相关进度条
                CpuTempProgressBar.SetValue(data.CPUTemperature);
                CpuClockProgressBar.SetValue(data.CPUClock);

                CpuFanProgressBar.SetValue(data.CPUFanSpeed);

                // 更新GPU相关进度条
                GpuTempProgressBar.SetValue(data.GPUTemperature);
                GpuClockProgressBar.SetValue(data.GPUClock);
                GpuFanProgressBar.SetValue(data.GPUFanSpeed);

                // 更新磁盘使用率进度条
                SystemCDriveProgressBar.SetValue(data.CDriveUsage);
                SystemDDriveProgressBar.SetValue(data.DDriveUsage);
                SystemEDriveProgressBar.SetValue(data.EDriveUsage);

                // 更新环形进度条（保持原有逻辑）
                CpuRingProgress.SetValue(Math.Min(100, Math.Max(0, data.CPUUsage)));
                GpuRingProgress.SetValue(Math.Min(100, Math.Max(0, data.GPUUsage)));
                RamRingProgress.SetValue(data.RAMUsage);

                // 更新RAM文本显示
                RamValidValue.Text = $"{data.RAMAvailable:F2}G";
                RamUsedValue.Text = $"{data.RAMUsed:F2}G";

                // 更新RAM进度条宽度
                // 总内存 = 可用内存 + 已使用内存
                double totalMemory = data.RAMAvailable + data.RAMUsed;
                if (totalMemory > 0)
                {
                    // 计算进度条宽度（最大宽度200px）
                    double maxWidth = 380.0;
                    double validPercentage = data.RAMAvailable / totalMemory;
                    double usedPercentage = data.RAMUsed / totalMemory;

                    RamValidProgressBar.Width = Math.Max(1, maxWidth * validPercentage);
                    RamUsedProgressBar.Width = Math.Max(1, maxWidth * usedPercentage);
                }

                // 更新电源数据显示
                CpuPackageValue.Text = $"{data.CPUPower:F1}W";
                CpuVoltageValue.Text = $"{data.CPUVoltage:F2}V";
                GpuPackageValue.Text = $"{data.GPUPower:F1}W";
                GpuVoltageValue.Text = $"{data.GPUVoltage:F2}V";

                // 更新网络速度显示
                UploadSpeedValue.Text = $"UPLOAD : {data.UploadSpeed:F1}KB/s";
                DownloadSpeedValue.Text = $"DOWNLOAD: {data.DownloadSpeed:F1}KB/s";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating system info display: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动刷新数据
        /// </summary>
        public async void RefreshData()
        {
            await _systemInfoDataService.RefreshDataAsync();
        }

        private string FormatTemperature(float celsius)
        {
            if (_temperatureUnitService.UseFahrenheit)
            {
                var fahrenheit = celsius * 9 / 5 + 32;
                return $"{Math.Truncate(fahrenheit)}°F";
            }
            else
            {
                return $"{Math.Truncate(celsius)}°C";
            }
        }

        private string FormatTemperatureShort(float celsius)
        {
            if (_temperatureUnitService.UseFahrenheit)
            {
                var fahrenheit = celsius * 9 / 5 + 32;
                return $"{Math.Truncate(fahrenheit)}°";
            }
            else
            {
                return $"{Math.Truncate(celsius)}°";
            }
        }

        /// <summary>
        /// 温度单位变化事件处理
        /// </summary>
        private void OnTemperatureUnitChanged()
        {
            // 当温度单位变化时，刷新显示
            var currentData = _systemInfoDataService.GetCurrentData();
            UpdateSystemInfoDisplay(currentData);
        }

        private string FormatNetworkSpeed(double speedKBps)
        {
            if (speedKBps >= 1024)
            {
                var mbps = speedKBps / 1024;
                return $"{Math.Truncate(mbps * 10) / 10:F1} MB/s";
            }
            else
            {
                return $"{Math.Truncate(speedKBps * 10) / 10:F1} KB/s";
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        ~SystemInformationPage()
        {
            _systemInfoDataService?.StopDataUpdates();
        }
    }
}
