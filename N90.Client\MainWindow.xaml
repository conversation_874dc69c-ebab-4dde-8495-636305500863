<Window x:Class="N90.Client.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:N90.Client"
        mc:Ignorable="d"
        Title="N90" Height="660" Width="660"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <DockPanel>
            <Menu DockPanel.Dock="Top">
                <MenuItem x:Name="exitMenu" Header="退出" Click="ExitMenu_Click"/>
                <MenuItem x:Name="languageMenu" Header="语言">
                    <MenuItem x:Name="cnMenuItem" Header="中文" Click="SwitchCN_Click" IsCheckable="True" />
                    <MenuItem x:Name="enMenuItem" Header="English" Click="SwitchEN_Click" IsCheckable="True" />
                </MenuItem>
                <MenuItem x:Name="startupMenu" Header="开机启动" Click="StartupMenu_Click" IsCheckable="True"/>
                <MenuItem x:Name="temperatureMenu" Header="温度单位">
                    <MenuItem x:Name="celsiusMenuItem" Header="摄氏度 (°C)" Click="CelsiusMenu_Click" IsCheckable="True"/>
                    <MenuItem x:Name="fahrenheitMenuItem" Header="华氏度 (°F)" Click="FahrenheitMenu_Click" IsCheckable="True"/>
                </MenuItem>
            </Menu>
            <ScrollViewer DockPanel.Dock="Top" VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    <!-- Header -->
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="Name" FontWeight="Bold"/>
                        <Label Grid.Column="1" Content="Value" FontWeight="Bold"/>
                        <Label Grid.Column="2" Content="Show" FontWeight="Bold"/>
                    </Grid>

                    <!-- Custom String -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" x:Name="customStringNameLabel" Content="自定义字符串"/>
                        <TextBox Grid.Column="1" x:Name="customStringTextBox" Width="180" Height="20"/>
                        <Button Grid.Column="2" x:Name="customStringSaveButton" Content="保存" Width="60" Height="25" Click="CustomStringSave_Click"/>
                        <CheckBox Grid.Column="3" x:Name="customStringCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Date -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="日期"/>
                        <Label Grid.Column="1" x:Name="dateValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="dateCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Time -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="时间"/>
                        <Label Grid.Column="1" x:Name="timeValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="timeCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Weekday -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="星期"/>
                        <Label Grid.Column="1" x:Name="weekdayValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="weekdayCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- CPU Temperature -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="CPU温度"/>
                        <Label Grid.Column="1" x:Name="cpuTemperatureValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="cpuTemperatureCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- CPU Usage -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="CPU使用率"/>
                        <Label Grid.Column="1" x:Name="cpuUsageValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="cpuUsageCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- CPU Power -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="CPU功耗"/>
                        <Label Grid.Column="1" x:Name="cpuPowerValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="cpuPowerCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- CPU Fan Speed -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="CPU风扇转速"/>
                        <Label Grid.Column="1" x:Name="cpuFanSpeedValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="cpuFanSpeedCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- CPU Model -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="CPU型号"/>
                        <Label Grid.Column="1" x:Name="cpuModelValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="cpuModelCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- GPU Temperature -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="GPU温度"/>
                        <Label Grid.Column="1" x:Name="gpuTemperatureValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="gpuTemperatureCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- GPU Memory Usage -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="GPU显存使用率"/>
                        <Label Grid.Column="1" x:Name="gpuMemoryUsageValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="gpuMemoryUsageCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- GPU Power -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="GPU功耗"/>
                        <Label Grid.Column="1" x:Name="gpuPowerValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="gpuPowerCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- GPU Model -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="GPU型号"/>
                        <Label Grid.Column="1" x:Name="gpuModelValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="gpuModelCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- RAM Usage -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="内存使用率"/>
                        <Label Grid.Column="1" x:Name="ramUsageValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="ramUsageCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Available RAM -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="可用内存"/>
                        <Label Grid.Column="1" x:Name="availableRamValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="availableRamCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Case Fan 1 Speed -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="机箱风扇1转速"/>
                        <Label Grid.Column="1" x:Name="caseFan1SpeedValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="caseFan1SpeedCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Case Fan 2 Speed -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="机箱风扇2转速"/>
                        <Label Grid.Column="1" x:Name="caseFan2SpeedValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="caseFan2SpeedCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- HDD Temperature -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="硬盘温度"/>
                        <Label Grid.Column="1" x:Name="hddTemperatureValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="hddTemperatureCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- HDD Usage -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="硬盘使用率"/>
                        <Label Grid.Column="1" x:Name="hddUsageValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="hddUsageCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Upload Speed -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="上传速度"/>
                        <Label Grid.Column="1" x:Name="uploadSpeedValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="uploadSpeedCheckBox" IsChecked="True"/>
                    </Grid>

                    <!-- Download Speed -->
                    <Grid Margin="0,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        <Label Grid.Column="0" Content="下载速度"/>
                        <Label Grid.Column="1" x:Name="downloadSpeedValueLabel" Content="--"/>
                        <CheckBox Grid.Column="2" x:Name="downloadSpeedCheckBox" IsChecked="True"/>
                    </Grid>

                </StackPanel>
            </ScrollViewer>
        </DockPanel>
    </Grid>
</Window>
