using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using N90.Shared;
using N90.Shared.Communication;

namespace N90.Client.Services
{
    /// <summary>
    /// 硬件数据管理器，负责从服务获取数据并分发给各个页面
    /// </summary>
    public class HardwareDataManager
    {
        private static HardwareDataManager? _instance;
        private static readonly object _lock = new object();

        private NamedPipeClient? _pipeClient;
        private HardwareData? _lastKnownData;
        private const int UPDATE_INTERVAL = 2000; // 2 seconds

        // 用户选择的显示状态偏好设置
        private readonly Dictionary<string, bool> _userShowPreferences = new();

        // 事件：当硬件数据更新时触发
        public event Action<HardwareData>? HardwareDataUpdated;

        public static HardwareDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new HardwareDataManager();
                    }
                }
                return _instance;
            }
        }

        private HardwareDataManager()
        {
            // 初始化时加载用户偏好
            LoadAllUserPreferencesFromRegistry();
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            try
            {
                // 初始化通信
                _pipeClient = new NamedPipeClient("N90_Pipe");
                _pipeClient.MessageReceived += OnMessageReceived;

                // 尝试连接到服务
                bool connected = await _pipeClient.ConnectAsync();
                if (!connected)
                {
                    // 如果连接失败，从JSON文件读取数据作为备用
                    _ = Task.Run(ReadFromJsonFile);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"HardwareDataManager initialization failed: {ex.Message}");
                // 启动JSON文件读取作为备用
                _ = Task.Run(ReadFromJsonFile);
            }
        }

        private void OnMessageReceived(IpcMessage message)
        {
            if (message.Type == MessageType.HardwareData)
            {
                try
                {
                    var data = JsonSerializer.Deserialize<HardwareData>(message.Data);
                    if (data != null)
                    {
                        // 应用用户偏好设置
                        ApplyUserPreferences(data);
                        _lastKnownData = data;
                        HardwareDataUpdated?.Invoke(data);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deserializing hardware data: {ex.Message}");
                }
            }
        }

        private async Task ReadFromJsonFile()
        {
            // 使用统一的数据文件路径逻辑
            string filePath = GetDataFilePath();
            Console.WriteLine($"Looking for data file at: {filePath}");

            if (!File.Exists(filePath))
            {
                Console.WriteLine($"Data file not found at: {filePath}. Using demo data.");
                // 如果找不到文件，使用演示数据
                GenerateDemoData();
                return;
            }

            while (true)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        string json = await File.ReadAllTextAsync(filePath);
                        var data = JsonSerializer.Deserialize<HardwareData>(json);
                        if (data != null)
                        {
                            // 应用用户偏好设置
                            ApplyUserPreferences(data);
                            _lastKnownData = data;
                            HardwareDataUpdated?.Invoke(data);
                            Console.WriteLine($"Updated UI with data from JSON at {DateTime.Now:HH:mm:ss}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Data file not found at {DateTime.Now:HH:mm:ss}, using demo data");
                        GenerateDemoData();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error reading JSON file: {ex.Message}");
                    GenerateDemoData();
                }

                await Task.Delay(UPDATE_INTERVAL);
            }
        }

        private void GenerateDemoData()
        {
            var random = new Random();
            var now = DateTime.Now;

            var demoData = new HardwareData
            {
                Date = new Date { value = now.ToString("yyyy-MM-dd"), show = true },
                Time = new Time { value = now.ToString("HH:mm:ss"), show = true },
                Weekday = new Weekday { value = now.ToString("dddd"), show = true },
                
                CPUTemperature = new CPUTemperature { value = 0, show = true },
                CPUUsage = new CPUUsage { value = 0, show = true },
                CPUPower = new CPUPower { value = 0, show = true },
                CPUFanSpeed = new CPUFanSpeed { value = 0, show = true },
                CPUModel = new CPUModel { value = null, show = true },
                
                GPUTemperature = new GPUTemperature { value = 0, show = true },
                GPUMemoryUsage = new GPUMemoryUsage { value = 0, show = true },
                GPUPower = new GPUPower { value = 0, show = true },
                GPUModel = new GPUModel { value = null, show = true },
                
                RAMUsage = new RAMUsage { value = 0, show = true },
                AvailableRAM = new AvailableRAM { value = 0, show = true },
                
                CaseFan1Speed = new CaseFan1Speed { value = 0, show = true },
                CaseFan2Speed = new CaseFan2Speed { value = 0, show = true },
                
                HDDTemperature = new HDDTemperature { value = 0, show = true },
                HDDUsage = new HDDUsage { value = 0, show = true },
                
                UploadSpeed = new UploadSpeed { value = 0, show = true },
                DownloadSpeed = new DownloadSpeed { value = 0, show = true },
                
                CustomString = new CustomString { value = null, show = true }
            };

            // 应用用户偏好设置
            ApplyUserPreferences(demoData);
            _lastKnownData = demoData;
            HardwareDataUpdated?.Invoke(demoData);
        }

        /// <summary>
        /// 获取最后已知的硬件数据
        /// </summary>
        /// <returns>最后已知的硬件数据，如果没有则返回null</returns>
        public HardwareData? GetLastKnownData()
        {
            return _lastKnownData;
        }

        /// <summary>
        /// 发送自定义字符串更新
        /// </summary>
        /// <param name="customString">自定义字符串</param>
        public async Task SendCustomStringUpdate(string customString)
        {
            if (_pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.CustomStringChanged,
                    Data = customString
                };
                await _pipeClient.SendMessageAsync(message);
            }
        }

        /// <summary>
        /// 发送亮度更新到服务
        /// </summary>
        /// <param name="brightness">亮度值(0-100)</param>
        public async Task SendBrightnessUpdate(int brightness)
        {
            if (_pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.BrightnessChanged,
                    Data = brightness.ToString()
                };
                await _pipeClient.SendMessageAsync(message);
                Console.WriteLine($"Sent brightness update: {brightness}%");
            }
            else
            {
                Console.WriteLine($"Cannot send brightness update: pipe client not connected");
            }
        }

        /// <summary>
        /// 发送选中的传感器数据到HID设备
        /// </summary>
        /// <param name="selectedData">选中的传感器数据字典</param>
        public async Task SendSelectedSensorData(Dictionary<string, object> selectedData)
        {
            if (selectedData == null || selectedData.Count == 0)
                return;

            if (_pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.SelectedSensorData,
                    Data = JsonSerializer.Serialize(selectedData)
                };
                await _pipeClient.SendMessageAsync(message);
                Console.WriteLine($"Sent {selectedData.Count} selected sensor values to HID");
            }
            else
            {
                Console.WriteLine($"Cannot send selected sensor data: pipe client not connected");
            }
        }

        /// <summary>
        /// 通知Service显示偏好已更改，让Service重新从注册表加载
        /// </summary>
        private async Task SendDisplayPreferencesToService()
        {
            try
            {
                if (_pipeClient?.IsConnected == true)
                {
                    var message = new IpcMessage
                    {
                        Type = MessageType.DisplayPreferencesChanged,
                        Data = "reload_from_registry" // 只发送通知，不发送具体数据
                    };
                    await _pipeClient.SendMessageAsync(message);
                    Console.WriteLine("Notified Service to reload display preferences from registry");
                }
                else
                {
                    Console.WriteLine("Cannot notify Service: pipe client not connected");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error notifying Service about display preferences change: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送温度单位变更通知到Service
        /// </summary>
        public async Task SendTemperatureUnitChanged()
        {
            if (_pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.TemperatureUnitChanged,
                    Data = ""
                };
                await _pipeClient.SendMessageAsync(message);
                Console.WriteLine("Sent temperature unit change notification to service");
            }
        }

        /// <summary>
        /// 发送语言变更通知到Service
        /// </summary>
        public async Task SendLanguageChanged()
        {
            if (_pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.LanguageChanged,
                    Data = ""
                };
                await _pipeClient.SendMessageAsync(message);
                Console.WriteLine("Sent language change notification to service");
            }
        }

        /// <summary>
        /// 发送配置更改通知到Service
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        public async Task SendConfigurationChanged(string configPath)
        {
            if (_pipeClient?.IsConnected == true)
            {
                var configData = new
                {
                    ConfigPath = configPath,
                    Action = "ConfigurationChanged",
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var message = new IpcMessage
                {
                    Type = MessageType.ConfigurationChanged,
                    Data = JsonSerializer.Serialize(configData)
                };
                await _pipeClient.SendMessageAsync(message);
                Console.WriteLine($"Sent configuration change notification to service: {configPath}");
            }
            else
            {
                Console.WriteLine($"Cannot send configuration change notification: pipe client not connected");
            }
        }

        /// <summary>
        /// 设置用户对某个数据项的显示偏好
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="show">是否显示</param>
        public void SetUserShowPreference(string dataType, bool show)
        {
            _userShowPreferences[dataType] = show;

            // 保存到注册表（与MainWindow逻辑一致）
            SaveUserShowPreferenceToRegistry(dataType, show);

            // 通知Service显示偏好已更改
            _ = Task.Run(async () => await SendDisplayPreferencesToService());

            // 如果有当前数据，立即应用用户偏好并触发更新
            if (_lastKnownData != null)
            {
                ApplyUserPreferences(_lastKnownData);
                HardwareDataUpdated?.Invoke(_lastKnownData);
            }
        }

        /// <summary>
        /// 获取用户对某个数据项的显示偏好
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>用户偏好，如果没有设置则返回true</returns>
        public bool GetUserShowPreference(string dataType)
        {
            // 首先尝试从内存缓存获取
            if (_userShowPreferences.TryGetValue(dataType, out bool preference))
            {
                return preference;
            }

            // 如果内存中没有，从注册表加载
            bool registryValue = LoadUserShowPreferenceFromRegistry(dataType);
            _userShowPreferences[dataType] = registryValue;
            return registryValue;
        }

        /// <summary>
        /// 从注册表加载用户显示偏好
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>显示偏好，默认为true</returns>
        private bool LoadUserShowPreferenceFromRegistry(string dataType)
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");
                return Convert.ToBoolean(registryKey.GetValue(dataType, 1));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading user show preference for {dataType}: {ex.Message}");
                return true; // 默认显示
            }
        }

        /// <summary>
        /// 保存用户显示偏好到注册表
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="show">是否显示</param>
        private void SaveUserShowPreferenceToRegistry(string dataType, bool show)
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");
                registryKey.SetValue(dataType, show ? 1 : 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving user show preference for {dataType}: {ex.Message}");
            }
        }

        /// <summary>
        /// 从注册表加载所有用户显示偏好（与MainWindow逻辑一致）
        /// </summary>
        private void LoadAllUserPreferencesFromRegistry()
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");

                // 加载所有数据项的显示偏好
                var dataTypes = new[]
                {
                    "Date", "Time", "Weekday", "CPUModel", "GPUModel", "CustomString",
                    "CPUTemperature", "CPUUsage", "CPUPower", "CPUFanSpeed",
                    "GPUTemperature", "GPUMemoryUsage", "GPUPower",
                    "RAMUsage", "AvailableRAM", "UsedRAM",
                    "HDDTemperature", "HDDUsage", "DriveTemperature", "DriveUsage",
                    "CaseFan1Speed", "CaseFan2Speed",
                    "UploadSpeed", "DownloadSpeed", "NetworkUploadSpeed", "NetworkDownloadSpeed"
                };

                foreach (var dataType in dataTypes)
                {
                    bool showState = Convert.ToBoolean(registryKey.GetValue(dataType, 1));
                    _userShowPreferences[dataType] = showState;
                }

                Console.WriteLine($"Loaded {_userShowPreferences.Count} user preferences from registry");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading user preferences from registry: {ex.Message}");
                // 如果加载失败，使用默认值（全部显示）
                SetDefaultUserPreferences();
            }
        }

        /// <summary>
        /// 设置默认用户偏好（全部显示）
        /// </summary>
        private void SetDefaultUserPreferences()
        {
            var dataTypes = new[]
            {
                "Date", "Time", "Weekday", "CPUModel", "GPUModel", "CustomString",
                "CPUTemperature", "CPUUsage", "CPUPower", "CPUFanSpeed",
                "GPUTemperature", "GPUMemoryUsage", "GPUPower",
                "RAMUsage", "AvailableRAM", "UsedRAM",
                "HDDTemperature", "HDDUsage", "DriveTemperature", "DriveUsage",
                "CaseFan1Speed", "CaseFan2Speed",
                "UploadSpeed", "DownloadSpeed", "NetworkUploadSpeed", "NetworkDownloadSpeed"
            };

            foreach (var dataType in dataTypes)
            {
                _userShowPreferences[dataType] = true;
            }
        }

        /// <summary>
        /// 获取数据文件路径（根据运行环境）
        /// </summary>
        /// <returns>数据文件的完整路径</returns>
        private string GetDataFilePath()
        {
            const string fileName = "data.json";

#if DEBUG
            // Debug模式：从项目根目录读取
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：从可执行文件同目录读取
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        /// <summary>
        /// 应用用户偏好设置到硬件数据
        /// </summary>
        /// <param name="data">硬件数据</param>
        private void ApplyUserPreferences(HardwareData data)
        {
            // System Info
            if (data.Date != null && _userShowPreferences.ContainsKey("Date"))
                data.Date.show = _userShowPreferences["Date"];
            if (data.Time != null && _userShowPreferences.ContainsKey("Time"))
                data.Time.show = _userShowPreferences["Time"];
            if (data.Weekday != null && _userShowPreferences.ContainsKey("Weekday"))
                data.Weekday.show = _userShowPreferences["Weekday"];
            if (data.CPUModel != null && _userShowPreferences.ContainsKey("CPUModel"))
                data.CPUModel.show = _userShowPreferences["CPUModel"];
            if (data.GPUModel != null && _userShowPreferences.ContainsKey("GPUModel"))
                data.GPUModel.show = _userShowPreferences["GPUModel"];
            if (data.CustomString != null && _userShowPreferences.ContainsKey("CustomString"))
                data.CustomString.show = _userShowPreferences["CustomString"];

            // CPU Info
            if (data.CPUTemperature != null && _userShowPreferences.ContainsKey("CPUTemperature"))
                data.CPUTemperature.show = _userShowPreferences["CPUTemperature"];
            if (data.CPUUsage != null && _userShowPreferences.ContainsKey("CPUUsage"))
                data.CPUUsage.show = _userShowPreferences["CPUUsage"];
            if (data.CPUPower != null && _userShowPreferences.ContainsKey("CPUPower"))
                data.CPUPower.show = _userShowPreferences["CPUPower"];
            if (data.CPUFanSpeed != null && _userShowPreferences.ContainsKey("CPUFanSpeed"))
                data.CPUFanSpeed.show = _userShowPreferences["CPUFanSpeed"];

            // GPU Info
            if (data.GPUTemperature != null && _userShowPreferences.ContainsKey("GPUTemperature"))
                data.GPUTemperature.show = _userShowPreferences["GPUTemperature"];
            if (data.GPUMemoryUsage != null && _userShowPreferences.ContainsKey("GPUMemoryUsage"))
                data.GPUMemoryUsage.show = _userShowPreferences["GPUMemoryUsage"];
            if (data.GPUPower != null && _userShowPreferences.ContainsKey("GPUPower"))
                data.GPUPower.show = _userShowPreferences["GPUPower"];

            // Memory & Storage
            if (data.RAMUsage != null && _userShowPreferences.ContainsKey("RAMUsage"))
                data.RAMUsage.show = _userShowPreferences["RAMUsage"];
            // 支持两种命名方式：MainWindow使用AvailableRAM，N90Page使用UsedRAM
            if (data.AvailableRAM != null)
            {
                if (_userShowPreferences.ContainsKey("AvailableRAM"))
                    data.AvailableRAM.show = _userShowPreferences["AvailableRAM"];
                else if (_userShowPreferences.ContainsKey("UsedRAM"))
                    data.AvailableRAM.show = _userShowPreferences["UsedRAM"];
            }

            // 硬盘：支持两种命名方式
            if (data.HDDTemperature != null)
            {
                if (_userShowPreferences.ContainsKey("HDDTemperature"))
                    data.HDDTemperature.show = _userShowPreferences["HDDTemperature"];
                else if (_userShowPreferences.ContainsKey("DriveTemperature"))
                    data.HDDTemperature.show = _userShowPreferences["DriveTemperature"];
            }
            if (data.HDDUsage != null)
            {
                if (_userShowPreferences.ContainsKey("HDDUsage"))
                    data.HDDUsage.show = _userShowPreferences["HDDUsage"];
                else if (_userShowPreferences.ContainsKey("DriveUsage"))
                    data.HDDUsage.show = _userShowPreferences["DriveUsage"];
            }

            // Fans
            if (data.CaseFan1Speed != null && _userShowPreferences.ContainsKey("CaseFan1Speed"))
                data.CaseFan1Speed.show = _userShowPreferences["CaseFan1Speed"];
            if (data.CaseFan2Speed != null && _userShowPreferences.ContainsKey("CaseFan2Speed"))
                data.CaseFan2Speed.show = _userShowPreferences["CaseFan2Speed"];

            // Network：支持两种命名方式
            if (data.UploadSpeed != null)
            {
                if (_userShowPreferences.ContainsKey("UploadSpeed"))
                    data.UploadSpeed.show = _userShowPreferences["UploadSpeed"];
                else if (_userShowPreferences.ContainsKey("NetworkUploadSpeed"))
                    data.UploadSpeed.show = _userShowPreferences["NetworkUploadSpeed"];
            }
            if (data.DownloadSpeed != null)
            {
                if (_userShowPreferences.ContainsKey("DownloadSpeed"))
                    data.DownloadSpeed.show = _userShowPreferences["DownloadSpeed"];
                else if (_userShowPreferences.ContainsKey("NetworkDownloadSpeed"))
                    data.DownloadSpeed.show = _userShowPreferences["NetworkDownloadSpeed"];
            }
        }
    }
}
