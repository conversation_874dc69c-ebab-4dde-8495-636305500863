using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using N90.Client.Models;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Linq;
using System.Text.Json;
using LibreHardwareMonitor.Hardware;

namespace N90.Client.Services
{
    /// <summary>
    /// System Information 页面专用的数据获取服务
    /// 根据setting.json配置的传感器来获取数据
    /// </summary>
    public class SystemInfoDataService
    {
        private static SystemInfoDataService? _instance;
        private static readonly object _lock = new object();

        private SystemInfoData _currentData;
        private Timer? _updateTimer;
        private bool _isRunning = false;

        // LibreHardwareMonitor 组件
        private Computer? _computer;
        private readonly object _hardwareLock = new object();

        // 传感器配置
        private AppSettings? _appSettings;
        private Dictionary<string, ISensor> _availableSensors = new Dictionary<string, ISensor>();
        private bool _sensorsDiscovered = false;

        // 事件：数据更新时触发
        public event Action<SystemInfoData>? DataUpdated;

        private SystemInfoDataService()
        {
            _currentData = SystemInfoData.CreateDefaultData();
            LoadConfiguration();
            InitializeHardwareMonitor();
        }

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static SystemInfoDataService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SystemInfoDataService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 获取当前数据
        /// </summary>
        /// <returns>当前的系统信息数据</returns>
        public SystemInfoData GetCurrentData()
        {
            return _currentData.Clone();
        }

        /// <summary>
        /// 初始化硬件监控器
        /// </summary>
        private void InitializeHardwareMonitor()
        {
            try
            {
                _computer = new Computer
                {
                    IsCpuEnabled = true,
                    IsGpuEnabled = true,
                    IsMemoryEnabled = true,
                    IsMotherboardEnabled = true,  // 主板硬件包含子硬件（SuperIO芯片）
                    IsStorageEnabled = true,
                    IsNetworkEnabled = true
                };
                _computer.Open();
            }
            catch (Exception ex)
            {
                // 如果硬件监控器初始化失败，记录错误但不阻止服务启动
                System.Diagnostics.Debug.WriteLine($"Failed to initialize hardware monitor: {ex.Message}");
                _computer = null;
            }
        }

        /// <summary>
        /// 启动数据更新服务
        /// </summary>
        /// <param name="updateIntervalMs">更新间隔（毫秒），默认1000ms</param>
        public void StartDataUpdates(int updateIntervalMs = 1000)
        {
            if (_isRunning) return;

            _isRunning = true;
            _updateTimer = new Timer(UpdateDataCallback, null, 0, updateIntervalMs);
        }

        /// <summary>
        /// 停止数据更新服务
        /// </summary>
        public void StopDataUpdates()
        {
            _isRunning = false;
            _updateTimer?.Dispose();
            _updateTimer = null;
        }

        /// <summary>
        /// 定时器回调，更新数据
        /// </summary>
        private void UpdateDataCallback(object? state)
        {
            try
            {
                // 获取真实的硬件数据
                var newData = GetRealHardwareData();
                
                // 如果获取真实数据失败，使用默认最低值数据
                if (newData == null)
                {
                    _currentData = SystemInfoData.CreateDefaultData();
                    newData = _currentData.Clone();
                }
                else
                {
                    _currentData = newData;
                }

                // 触发数据更新事件
                DataUpdated?.Invoke(newData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating system info data: {ex.Message}");

                // 发生错误时使用默认最低值数据
                _currentData = SystemInfoData.CreateDefaultData();
                DataUpdated?.Invoke(_currentData.Clone());
            }
        }

        /// <summary>
        /// 获取真实的硬件数据
        /// </summary>
        /// <returns>真实的硬件数据，如果获取失败返回null</returns>
        private SystemInfoData? GetRealHardwareData()
        {
            try
            {
                var data = new SystemInfoData();

                // 获取CPU使用率
                data.CPUUsage = GetCPUUsage();

                // 获取内存信息
                GetMemoryInfo(data);

                // 获取磁盘使用率
                GetDiskUsage(data);

                // 使用 LibreHardwareMonitor 获取硬件数据
                if (_computer != null)
                {
                    lock (_hardwareLock)
                    {
                        GetHardwareMonitorData(data);
                    }
                }
                else
                {
                    // 如果硬件监控器不可用，设置为最低值
                    SetMinimumHardwareValues(data);
                }

                data.LastUpdated = DateTime.Now;
                return data;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting real hardware data: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        private double GetCPUUsage()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("select * from Win32_PerfRawData_PerfOS_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        if (obj["Name"].ToString() == "_Total")
                        {
                            // 获取失败时返回最低值
                            return 0;
                        }
                    }
                }
            }
            catch
            {
                // 获取失败时返回最低值
                return 0;
            }
            // 如果没有获取到数据，返回最低值
            return 0;
        }

        /// <summary>
        /// 获取内存信息
        /// </summary>
        private void GetMemoryInfo(SystemInfoData data)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var totalMemoryMB = Convert.ToDouble(obj["TotalVisibleMemorySize"]) / 1024; // MB
                        var freeMemoryMB = Convert.ToDouble(obj["FreePhysicalMemory"]) / 1024; // MB
                        var usedMemoryMB = totalMemoryMB - freeMemoryMB;

                        // 转换为GB
                        data.RAMTotal = totalMemoryMB / 1024;
                        data.RAMUsed = usedMemoryMB / 1024;
                        data.RAMAvailable = freeMemoryMB / 1024;
                        data.RAMUsage = (usedMemoryMB / totalMemoryMB) * 100;
                        return;
                    }
                }
            }
            catch
            {
                // 获取失败时使用最低值
                data.RAMTotal = 0;
                data.RAMUsage = 0;
                data.RAMUsed = 0;
                data.RAMAvailable = 0;
            }
        }

        /// <summary>
        /// 获取磁盘使用率
        /// </summary>
        private void GetDiskUsage(SystemInfoData data)
        {
            try
            {
                // C盘
                if (Directory.Exists("C:\\"))
                {
                    var cDrive = new DriveInfo("C");
                    if (cDrive.IsReady)
                    {
                        var usedSpace = cDrive.TotalSize - cDrive.AvailableFreeSpace;
                        data.CDriveUsage = (double)usedSpace / cDrive.TotalSize * 100;
                    }
                }

                // D盘
                if (Directory.Exists("D:\\"))
                {
                    var dDrive = new DriveInfo("D");
                    if (dDrive.IsReady)
                    {
                        var usedSpace = dDrive.TotalSize - dDrive.AvailableFreeSpace;
                        data.DDriveUsage = (double)usedSpace / dDrive.TotalSize * 100;
                    }
                }

                // E盘
                if (Directory.Exists("E:\\"))
                {
                    var eDrive = new DriveInfo("E");
                    if (eDrive.IsReady)
                    {
                        var usedSpace = eDrive.TotalSize - eDrive.AvailableFreeSpace;
                        data.EDriveUsage = (double)usedSpace / eDrive.TotalSize * 100;
                    }
                }
            }
            catch
            {
                // 获取失败时使用最低值
                data.CDriveUsage = 0;
                data.DDriveUsage = 0;
                data.EDriveUsage = 0;
            }
        }

        /// <summary>
        /// 使用 LibreHardwareMonitor 获取硬件数据
        /// </summary>
        private void GetHardwareMonitorData(SystemInfoData data)
        {
            try
            {
                // 确保传感器已发现
                if (!_sensorsDiscovered)
                {
                    DiscoverAllSensors();
                }

                // 更新所有硬件传感器
                foreach (var hardware in _computer?.Hardware ?? Array.Empty<IHardware>())
                {
                    hardware.Update();

                    switch (hardware.HardwareType)
                    {
                        case HardwareType.Cpu:
                            ProcessCPUHardware(hardware, data);
                            break;
                        case HardwareType.GpuNvidia:
                        case HardwareType.GpuAmd:
                        case HardwareType.GpuIntel:
                            ProcessGPUHardware(hardware, data);
                            break;
                        case HardwareType.Motherboard:
                            ProcessMotherboardHardware(hardware, data);
                            break;
                        case HardwareType.SuperIO:
                            ProcessSuperIOHardware(hardware, data);
                            break;
                        case HardwareType.Network:
                            ProcessNetworkHardware(hardware, data);
                            break;
                        case HardwareType.Memory:
                            ProcessMemoryHardware(hardware, data);
                            break;
                        case HardwareType.Storage:
                            ProcessStorageHardware(hardware, data);
                            break;
                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting hardware monitor data: {ex.Message}");
                SetMinimumHardwareValues(data);
            }
        }

        /// <summary>
        /// 处理CPU硬件数据 - 根据配置选择特定传感器
        /// </summary>
        private void ProcessCPUHardware(IHardware hardware, SystemInfoData data)
        {
            // 获取配置中的传感器选择
            string selectedTempSensor = _appSettings?.SelectedCpuTempSensor ?? string.Empty;
            string selectedVoltageSensor = _appSettings?.SelectedCpuVoltageSensor ?? string.Empty;
            string selectedFanSensor = _appSettings?.SelectedCpuFanSensor ?? string.Empty;

            int fanCount = 0;

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Temperature:
                        if (ShouldUseCpuTemperatureSensor(sensor, selectedTempSensor))
                        {
                            data.CPUTemperature = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Clock:
                        if (sensor.Name.Contains("CPU Core #1") || sensor.Name.Contains("Core #1"))
                        {
                            data.CPUClock = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Fan:
                        if (ShouldUseCpuFanSensor(sensor, selectedFanSensor, fanCount))
                        {
                            data.CPUFanSpeed = sensor.Value.GetValueOrDefault();
                            fanCount++;
                        }
                        break;
                    case SensorType.Power:
                        if (ShouldUseCpuVoltageSensor(sensor, selectedVoltageSensor))
                        {
                            data.CPUPower = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Voltage:
                        if (sensor.Name.Contains("CPU") || sensor.Name.Contains("VCore"))
                        {
                            data.CPUVoltage = sensor.Value.GetValueOrDefault();
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 处理GPU硬件数据 - 根据配置选择特定GPU
        /// </summary>
        private void ProcessGPUHardware(IHardware hardware, SystemInfoData data)
        {
            // 获取配置中选择的GPU
            string selectedGpu = _appSettings?.SelectedGpuSensor ?? string.Empty;

            // GPU选择逻辑：
            // 1. 如果配置为空，使用第一个GPU
            // 2. 如果配置了特定GPU，只处理匹配的GPU
            bool shouldProcessGpu = false;

            if (string.IsNullOrEmpty(selectedGpu))
            {
                // 配置为空，使用第一个GPU
                shouldProcessGpu = data.GPUTemperature == 0; // 只处理第一个GPU
            }
            else
            {
                // 配置了特定GPU，检查是否匹配
                shouldProcessGpu = hardware.Name.Contains(selectedGpu, StringComparison.OrdinalIgnoreCase);
            }

            if (!shouldProcessGpu)
            {
                return;
            }

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Temperature:
                        data.GPUTemperature = sensor.Value.GetValueOrDefault();
                        break;
                    case SensorType.Clock:
                        if (sensor.Name.Contains("GPU Core") || sensor.Name.Contains("Graphics"))
                        {
                            data.GPUClock = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Fan:
                        if (data.GPUFanSpeed == 0) // 使用第一个找到的风扇
                        {
                            data.GPUFanSpeed = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Power:
                        data.GPUPower = sensor.Value.GetValueOrDefault();
                        break;
                    case SensorType.Voltage:
                        if (data.GPUVoltage == 0) // 使用第一个找到的电压
                        {
                            data.GPUVoltage = sensor.Value.GetValueOrDefault();
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 处理网络硬件数据 - 根据配置选择特定网络接口
        /// </summary>
        private void ProcessNetworkHardware(IHardware hardware, SystemInfoData data)
        {
            // 获取配置中选择的网络接口
            string selectedInterface = _appSettings?.SelectedNetworkInterface ?? string.Empty;

            // 网络接口选择逻辑：
            // 1. 如果配置为空，处理所有网络接口（累加速度）
            // 2. 如果配置了特定接口，只处理匹配的接口
            bool shouldProcessInterface = false;

            if (string.IsNullOrEmpty(selectedInterface))
            {
                // 配置为空，处理所有网络接口
                shouldProcessInterface = true;
            }
            else
            {
                // 配置了特定接口，检查是否匹配
                shouldProcessInterface = hardware.Name.Contains(selectedInterface, StringComparison.OrdinalIgnoreCase);
            }

            if (!shouldProcessInterface)
            {
                return;
            }

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Throughput:
                        // 网络吞吐量传感器
                        ProcessNetworkThroughputSensor(sensor, data);
                        break;

                    case SensorType.Data:
                        // 数据传输量传感器（累计值）
                        break;

                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 设置最低硬件值
        /// </summary>
        private void SetMinimumHardwareValues(SystemInfoData data)
        {
            data.CPUTemperature = 0;
            data.CPUClock = 0;
            data.CPUFanSpeed = 0;
            data.CPUPower = 0;
            data.CPUVoltage = 0;

            data.GPUTemperature = 0;
            data.GPUClock = 0;
            data.GPUFanSpeed = 0;
            data.GPUPower = 0;

            data.UploadSpeed = 0;
            data.DownloadSpeed = 0;
        }

        /// <summary>
        /// 手动刷新数据
        /// </summary>
        public async Task RefreshDataAsync()
        {
            await Task.Run(() => UpdateDataCallback(null));
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var settingsPath = GetSettingsFilePath();

                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };
                    _appSettings = JsonSerializer.Deserialize<AppSettings>(json, options);
                }
                else
                {
                    _appSettings = new AppSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading configuration: {ex.Message}");
                _appSettings = new AppSettings();
            }
        }

        /// <summary>
        /// 获取设置文件路径
        /// </summary>
        private string GetSettingsFilePath()
        {
            const string fileName = "setting.json";
#if DEBUG
            // Debug模式：保存到项目根目录
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：保存到可执行文件同目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        /// <summary>
        /// 发现所有可用传感器并建立索引
        /// </summary>
        private void DiscoverAllSensors()
        {
            if (_sensorsDiscovered) return;

            try
            {
                _availableSensors.Clear();

                foreach (var hardware in _computer?.Hardware ?? Array.Empty<IHardware>())
                {
                    hardware.Update();
                    string hardwareName = hardware.Name;

                    // 添加硬件本身的传感器
                    ProcessSensors(hardware.Sensors, hardwareName);

                    // 添加子硬件的传感器
                    foreach (var subHardware in hardware.SubHardware)
                    {
                        subHardware.Update();
                        string subHardwareName = $"{hardwareName}_{subHardware.Name}";
                        ProcessSensors(subHardware.Sensors, subHardwareName);
                    }
                }

                _sensorsDiscovered = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error discovering sensors: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理传感器并生成键值
        /// </summary>
        private void ProcessSensors(IEnumerable<ISensor> sensors, string hardwareName)
        {
            foreach (var sensor in sensors)
            {
                string sensorKey = $"{hardwareName}_{sensor.SensorType}_{sensor.Name}";
                if (!_availableSensors.ContainsKey(sensorKey))
                {
                    _availableSensors[sensorKey] = sensor;
                }
            }
        }

        /// <summary>
        /// 从完整的传感器键名中提取传感器名称
        /// </summary>
        private string ExtractSensorNameFromKey(string sensorKey)
        {
            if (string.IsNullOrEmpty(sensorKey))
                return string.Empty;

            var parts = sensorKey.Split('_');
            if (parts.Length >= 3)
            {
                return parts[parts.Length - 1];
            }

            return sensorKey;
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU温度传感器
        /// </summary>
        private bool ShouldUseCpuTemperatureSensor(ISensor sensor, string selectedSensor)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                return sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
            }
            // 默认使用Package温度传感器
            return sensor.Name.Contains("Package") || sensor.Name.Contains("Tctl");
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU电压传感器（对应CPUPower）
        /// </summary>
        private bool ShouldUseCpuVoltageSensor(ISensor sensor, string selectedSensor)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                return sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
            }
            // 默认使用Package功耗传感器
            return sensor.Name.Contains("Package");
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU风扇传感器
        /// </summary>
        private bool ShouldUseCpuFanSensor(ISensor sensor, string selectedSensor, int fanCount)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                return sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
            }
            // 默认使用第一个风扇传感器
            return fanCount == 0;
        }

        /// <summary>
        /// 处理主板硬件数据 - 包含CPU风扇等Fan类型传感器（在子硬件中）
        /// </summary>
        private void ProcessMotherboardHardware(IHardware hardware, SystemInfoData data)
        {
            // 获取配置中的风扇传感器选择
            string selectedCpuFanSensor = _appSettings?.SelectedCpuFanSensor ?? string.Empty;

            // 重要：更新硬件数据以确保SubHardware被正确加载
            hardware.Update();

            int fanCount = 0;

            // 首先检查主板硬件本身的传感器
            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Fan:
                        if (ShouldUseCpuFanSensor(sensor, selectedCpuFanSensor, fanCount))
                        {
                            data.CPUFanSpeed = sensor.Value.GetValueOrDefault();
                        }
                        fanCount++;
                        break;
                    case SensorType.Temperature:
                        break;
                    case SensorType.Voltage:
                        break;
                }
            }

            // 重要：检查子硬件（第三级），这里通常包含SuperIO芯片和风扇传感器
            foreach (var subHardware in hardware.SubHardware)
            {
                // 关键：更新子硬件数据以获取最新的传感器值
                subHardware.Update();

                foreach (var sensor in subHardware.Sensors)
                {
                    switch (sensor.SensorType)
                    {
                        case SensorType.Fan:
                            if (ShouldUseCpuFanSensor(sensor, selectedCpuFanSensor, fanCount))
                            {
                                data.CPUFanSpeed = sensor.Value.GetValueOrDefault();
                            }
                            fanCount++;
                            break;
                        case SensorType.Temperature:
                            break;
                        case SensorType.Voltage:
                            break;
                        case SensorType.Control:
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 处理SuperIO硬件数据 - 根据配置选择特定的风扇传感器
        /// 这里可能包含CPU风扇和机箱风扇
        /// </summary>
        private void ProcessSuperIOHardware(IHardware hardware, SystemInfoData data)
        {
            // 获取配置中的风扇传感器选择
            string selectedCpuFanSensor = _appSettings?.SelectedCpuFanSensor ?? string.Empty;

            int fanCount = 0;

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Fan:
                        // CPU风扇 - 对应selectedCpuFanSensor
                        if (ShouldUseCpuFanSensor(sensor, selectedCpuFanSensor, fanCount))
                        {
                            data.CPUFanSpeed = sensor.Value.GetValueOrDefault();
                        }

                        fanCount++;
                        break;
                }
            }
        }

        /// <summary>
        /// 处理网络吞吐量传感器 - 与HardwareReader.cs保持一致
        /// </summary>
        private void ProcessNetworkThroughputSensor(ISensor sensor, SystemInfoData data)
        {
            double speedKbps = sensor.Value.GetValueOrDefault() / 1024.0;
            string selectedInterface = _appSettings?.SelectedNetworkInterface ?? string.Empty;

            // 网络吞吐量传感器
            if (sensor.Name.Contains("Upload") || sensor.Name.Contains("Sent") || sensor.Name.Contains("Tx"))
            {
                // 上传速度 (通常以 B/s 为单位，转换为 KB/s)
                // 如果配置为空（处理所有接口），则累加速度
                if (string.IsNullOrEmpty(selectedInterface))
                {
                    data.UploadSpeed += speedKbps;
                }
                else
                {
                    data.UploadSpeed = speedKbps;
                }
            }
            else if (sensor.Name.Contains("Download") || sensor.Name.Contains("Received") || sensor.Name.Contains("Rx"))
            {
                // 下载速度 (通常以 B/s 为单位，转换为 KB/s)
                // 如果配置为空（处理所有接口），则累加速度
                if (string.IsNullOrEmpty(selectedInterface))
                {
                    data.DownloadSpeed += speedKbps;
                }
                else
                {
                    data.DownloadSpeed = speedKbps;
                }
            }
            else
            {
                // 如果传感器名称不明确，尝试根据索引分配
                // 通常第一个是下载，第二个是上传
                var hardware = sensor.Hardware;
                var throughputSensors = hardware.Sensors.Where(s => s.SensorType == SensorType.Throughput).ToList();
                var currentIndex = throughputSensors.IndexOf(sensor);

                if (currentIndex == 0)
                {
                    // 下载速度
                    if (string.IsNullOrEmpty(selectedInterface))
                    {
                        data.DownloadSpeed += speedKbps;
                    }
                    else
                    {
                        data.DownloadSpeed = speedKbps;
                    }
                }
                else if (currentIndex == 1)
                {
                    // 上传速度
                    if (string.IsNullOrEmpty(selectedInterface))
                    {
                        data.UploadSpeed += speedKbps;
                    }
                    else
                    {
                        data.UploadSpeed = speedKbps;
                    }
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopDataUpdates();

            // 释放硬件监控器资源
            if (_computer != null)
            {
                try
                {
                    _computer.Close();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error closing hardware monitor: {ex.Message}");
                }
                _computer = null;
            }
        }

        /// <summary>
        /// 处理内存硬件
        /// </summary>
        private void ProcessMemoryHardware(IHardware hardware, SystemInfoData data)
        {
            foreach (var sensor in hardware.Sensors)
            {
                if (sensor.SensorType == SensorType.Data && sensor.Name.Contains("Memory Used"))
                {
                    double memoryUsedGB = sensor.Value.GetValueOrDefault() / (1024.0 * 1024.0 * 1024.0); // Convert bytes to GB
                    // 不要覆盖RAMUsage，它应该保持为百分比值
                    // data.RAMUsage = memoryUsedGB; // 这行导致了问题
                    break;
                }
            }
        }

        /// <summary>
        /// 处理存储硬件
        /// </summary>
        private void ProcessStorageHardware(IHardware hardware, SystemInfoData data)
        {
            foreach (var sensor in hardware.Sensors)
            {
                if (sensor.SensorType == SensorType.Data)
                {
                    // 可以根据需要处理磁盘使用率数据
                    // 这里暂时只记录发现的传感器
                }
            }
        }
    }
}
