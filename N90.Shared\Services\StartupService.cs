using System;
using System.IO;
using System.Linq;
using Microsoft.Win32;

namespace N90.Shared.Services
{
    public class StartupService
    {
        private readonly string appName;
        private readonly string executablePath;

        public StartupService(string appName, string executablePath)
        {
            this.appName = appName;
            this.executablePath = executablePath;
        }

        public StartupService(string appName) : this(appName, GetDefaultExecutablePath())
        {
        }

        private static string GetDefaultExecutablePath()
        {
            // For single-file apps, use AppContext.BaseDirectory instead of Assembly.Location
            var baseDir = AppContext.BaseDirectory;
            var n90Path = Path.Combine(baseDir, "N90.exe");

            if (File.Exists(n90Path))
            {
                return n90Path;
            }

            // Fallback to current executable
            return System.Diagnostics.Process.GetCurrentProcess().MainModule?.FileName ?? "";
        }

        public bool IsStartupSet()
        {
            try
            {
                // 检查注册表中是否有该应用的启动项
                RegistryKey rkApp = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", false);
                return rkApp?.GetValue(appName) != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public void SetStartup()
        {
            try
            {
                // 设置应用程序随系统启动
                RegistryKey rkApp = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true);
                rkApp?.SetValue(appName, executablePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to set startup: {ex.Message}");
            }
        }

        public void RemoveStartup()
        {
            try
            {
                RegistryKey rkApp = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true);
                rkApp?.DeleteValue(appName, false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove startup: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置完整的开机自启：Service作为Windows服务 + Client通过任务计划程序自启（支持管理员权限）
        /// </summary>
        public static bool SetupCompleteAutoStart()
        {
            try
            {
                var baseDir = AppContext.BaseDirectory;
                var servicePath = Path.Combine(baseDir, "N90.Service.exe");
                var n90Path = Path.Combine(baseDir, "N90.exe");

                Console.WriteLine($"Base directory: {baseDir}");
                Console.WriteLine($"Service path: {servicePath}");
                Console.WriteLine($"N90 path: {n90Path}");
                Console.WriteLine($"Service exists: {File.Exists(servicePath)}");
                Console.WriteLine($"N90 exists: {File.Exists(n90Path)}");

                // 如果在开发环境中文件不存在，尝试查找其他可能的路径
                if (!File.Exists(servicePath) || !File.Exists(n90Path))
                {
                    Console.WriteLine("Files not found in base directory, trying alternative paths...");

                    // 尝试在解决方案根目录查找
                    var solutionDir = FindSolutionDirectory(baseDir);
                    if (!string.IsNullOrEmpty(solutionDir))
                    {
                        var altServicePath = FindFileInSolution(solutionDir, "N90.Service.exe");
                        var altN90Path = FindFileInSolution(solutionDir, "N90.exe");

                        if (!string.IsNullOrEmpty(altServicePath) && !string.IsNullOrEmpty(altN90Path))
                        {
                            servicePath = altServicePath;
                            n90Path = altN90Path;
                            Console.WriteLine($"Found alternative service path: {servicePath}");
                            Console.WriteLine($"Found alternative N90 path: {n90Path}");
                        }
                    }
                }

                if (!File.Exists(servicePath) || !File.Exists(n90Path))
                {
                    Console.WriteLine("Required files not found even after searching alternative paths!");
                    Console.WriteLine("This is normal in development environment. Auto-start setup skipped.");
                    return false;
                }

                // 1. 安装Service为Windows服务
                Console.WriteLine("Installing Windows service...");
                var serviceInstalled = InstallWindowsService(servicePath);
                Console.WriteLine($"Service installation result: {serviceInstalled}");

                // 2. 设置N90通过任务计划程序自启（支持管理员权限）
                Console.WriteLine("Setting up task scheduler...");
                var taskScheduled = SetupTaskSchedulerAutoStart(n90Path);
                Console.WriteLine($"Task scheduler setup result: {taskScheduled}");

                var result = serviceInstalled && taskScheduled;
                Console.WriteLine($"Overall setup result: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to setup complete auto start: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除完整的开机自启
        /// </summary>
        public static bool RemoveCompleteAutoStart()
        {
            try
            {
                // 1. 卸载Windows服务
                var serviceRemoved = UninstallWindowsService();

                // 2. 移除Client任务计划程序自启
                var taskRemoved = RemoveTaskSchedulerAutoStart();

                // 3. 移除传统的注册表自启（兼容性）
                var clientStartup = new StartupService("N90HardwareMonitor", "");
                clientStartup.RemoveStartup();

                return serviceRemoved && taskRemoved;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove complete auto start: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否已设置完整的开机自启
        /// </summary>
        public static bool IsCompleteAutoStartSet()
        {
            try
            {
                // 检查Windows服务是否存在
                var serviceExists = IsWindowsServiceInstalled();

                // 检查Client任务计划程序自启是否设置
                var taskExists = IsTaskSchedulerAutoStartSet();

                // 兼容性检查：如果任务计划程序没有设置，检查传统注册表方式
                if (!taskExists)
                {
                    var clientStartup = new StartupService("N90HardwareMonitor", "");
                    taskExists = clientStartup.IsStartupSet();
                }

                return serviceExists && taskExists;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check complete auto start: {ex.Message}");
                return false;
            }
        }

        private static bool InstallWindowsService(string servicePath)
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = $"create \"N90HardwareMonitorService\" binPath= \"\\\"{servicePath}\\\" --service\" DisplayName= \"N90 Hardware Monitor Service\" start= auto",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                process?.WaitForExit();

                return process?.ExitCode == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to install Windows service: {ex.Message}");
                return false;
            }
        }

        private static bool UninstallWindowsService()
        {
            try
            {
                // 先停止服务
                var stopInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "stop \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (var stopProcess = System.Diagnostics.Process.Start(stopInfo))
                {
                    stopProcess?.WaitForExit();
                }

                // 删除服务
                var deleteInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "delete \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var deleteProcess = System.Diagnostics.Process.Start(deleteInfo);
                deleteProcess?.WaitForExit();

                return deleteProcess?.ExitCode == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to uninstall Windows service: {ex.Message}");
                return false;
            }
        }

        private static bool IsWindowsServiceInstalled()
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "query \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                process?.WaitForExit();

                return process?.ExitCode == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check Windows service: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 通过任务计划程序设置程序自启动（支持管理员权限）
        /// </summary>
        private static bool SetupTaskSchedulerAutoStart(string executablePath)
        {
            try
            {
                const string taskName = "N90HardwareMonitorClient";

                // 先删除已存在的任务
                RemoveTaskSchedulerAutoStart();

                // 创建任务计划程序命令
                var arguments = $"/Create /TN \"{taskName}\" " +
                              $"/TR \"\\\"{executablePath}\\\"\" " +
                              "/SC ONLOGON " +
                              "/RL HIGHEST " +
                              "/F";

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = arguments,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                process?.WaitForExit();

                var success = process?.ExitCode == 0;
                if (success)
                {
                    Console.WriteLine($"Task scheduler auto-start configured for: {executablePath}");
                }
                else
                {
                    Console.WriteLine($"Failed to configure task scheduler auto-start. Exit code: {process?.ExitCode}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to setup task scheduler auto start: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除任务计划程序自启动
        /// </summary>
        private static bool RemoveTaskSchedulerAutoStart()
        {
            try
            {
                const string taskName = "N90HardwareMonitorClient";

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/Delete /TN \"{taskName}\" /F",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                process?.WaitForExit();

                // 即使任务不存在，删除操作也可能返回非0退出码，这是正常的
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to remove task scheduler auto start: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查任务计划程序自启动是否已设置
        /// </summary>
        private static bool IsTaskSchedulerAutoStartSet()
        {
            try
            {
                const string taskName = "N90HardwareMonitorClient";

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = $"/Query /TN \"{taskName}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                process?.WaitForExit();

                return process?.ExitCode == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check task scheduler auto start: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找解决方案根目录
        /// </summary>
        private static string FindSolutionDirectory(string startPath)
        {
            var currentDir = new DirectoryInfo(startPath);
            while (currentDir != null)
            {
                // 查找 .sln 文件或特定的项目结构
                if (currentDir.GetFiles("*.sln").Length > 0 ||
                    currentDir.GetDirectories("N90.Service").Length > 0)
                {
                    return currentDir.FullName;
                }
                currentDir = currentDir.Parent;
            }
            return null;
        }

        /// <summary>
        /// 在解决方案目录中查找指定文件，根据开发环境的项目结构
        /// </summary>
        private static string FindFileInSolution(string solutionDir, string fileName)
        {
            try
            {
                Console.WriteLine($"Searching for {fileName} in solution directory: {solutionDir}");

                // 根据文件名确定应该在哪个项目目录中查找
                string projectDir = "";
                if (fileName.Contains("N90.Service"))
                {
                    projectDir = "N90.Service";
                }
                else if (fileName.Contains("N90.exe"))
                {
                    projectDir = "N90.Client";
                }

                if (!string.IsNullOrEmpty(projectDir))
                {
                    // 构建预期的路径：项目根目录/项目名/bin/Debug/net6.0-windows/文件名
                    var expectedPath = Path.Combine(solutionDir, projectDir, "bin", "Debug", "net6.0-windows", fileName);
                    Console.WriteLine($"Checking expected path: {expectedPath}");

                    if (File.Exists(expectedPath))
                    {
                        Console.WriteLine($"Found {fileName} at expected location");
                        return expectedPath;
                    }

                    // 如果Debug目录不存在，尝试Release目录
                    var releasePath = Path.Combine(solutionDir, projectDir, "bin", "Release", "net6.0-windows", fileName);
                    Console.WriteLine($"Checking release path: {releasePath}");

                    if (File.Exists(releasePath))
                    {
                        Console.WriteLine($"Found {fileName} in release directory");
                        return releasePath;
                    }
                }

                // 如果预期路径不存在，进行递归搜索作为备选
                Console.WriteLine($"Expected paths not found, performing recursive search...");
                var files = Directory.GetFiles(solutionDir, fileName, SearchOption.AllDirectories);

                // 优先选择 bin/Debug 目录下的文件
                var debugFile = files.FirstOrDefault(f => f.Contains("bin") && f.Contains("Debug"));
                if (!string.IsNullOrEmpty(debugFile))
                {
                    Console.WriteLine($"Found {fileName} in debug directory: {debugFile}");
                    return debugFile;
                }

                // 其次选择 bin/Release 目录下的文件
                var releaseFile = files.FirstOrDefault(f => f.Contains("bin") && f.Contains("Release"));
                if (!string.IsNullOrEmpty(releaseFile))
                {
                    Console.WriteLine($"Found {fileName} in release directory: {releaseFile}");
                    return releaseFile;
                }

                // 最后返回任何找到的文件
                var anyFile = files.FirstOrDefault();
                if (!string.IsNullOrEmpty(anyFile))
                {
                    Console.WriteLine($"Found {fileName} at: {anyFile}");
                    return anyFile;
                }

                Console.WriteLine($"Could not find {fileName} anywhere in solution");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error searching for {fileName}: {ex.Message}");
                return null;
            }
        }
    }
}
