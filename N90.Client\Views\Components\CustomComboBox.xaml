<UserControl x:Class="N90.Client.Views.Components.CustomComboBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="28" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 透明ToggleButton样式，取消悬停效果 -->
        <Style x:Key="TransparentToggleButtonStyle" TargetType="ToggleButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义下拉框样式 -->
        <Style x:Key="CustomComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <!-- 背景边框图片 -->
                            <Image Source="/Resources/Images/img11.png" 
                                   Stretch="Fill" 
                                   HorizontalAlignment="Stretch" 
                                   VerticalAlignment="Stretch"/>
                            
                            <!-- 内容区域 -->
                            <Grid Margin="8,6">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 显示选中内容 -->
                                <ContentPresenter Grid.Column="0"
                                                  x:Name="ContentSite"
                                                  IsHitTestVisible="False"
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                  ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Left"
                                                  Margin="0,0,5,0"/>
                                
                                <!-- 下拉箭头 -->
                                <Path Grid.Column="1"
                                      x:Name="Arrow"
                                      Fill="White"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                      Margin="0,0,5,0"/>
                                
                                <!-- 透明按钮覆盖整个区域 -->
                                <ToggleButton Grid.ColumnSpan="2"
                                              x:Name="ToggleButton"
                                              Style="{StaticResource TransparentToggleButtonStyle}"
                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                              ClickMode="Press"/>
                            </Grid>
                            
                            <!-- 下拉列表弹出框 -->
                            <Popup x:Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid x:Name="DropDown"
                                      SnapsToDevicePixels="True"
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    
                                    <!-- 下拉列表背景 -->
                                    <Border Background="#FF1A1A1A"
                                            BorderBrush="#FF333333"
                                            BorderThickness="1"
                                            CornerRadius="2">
                                        <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Border>
                                </Grid>
                            </Popup>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <!-- 下拉打开状态 -->
                            <Trigger Property="IsDropDownOpen" Value="True">
                                <Setter TargetName="Arrow" Property="Data" Value="M 0 4 L 4 0 L 8 4 Z"/>
                            </Trigger>
                            
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="#FF666666"/>
                                <Setter TargetName="Arrow" Property="Fill" Value="#FF666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 下拉列表项样式 -->
        <Style TargetType="ComboBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsHighlighted" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FF333333"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FF4A9B4A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <ComboBox x:Name="InternalComboBox"
              Style="{StaticResource CustomComboBoxStyle}"
              SelectionChanged="InternalComboBox_SelectionChanged">
        <!-- 这里会通过代码添加项目 -->
    </ComboBox>
</UserControl>
