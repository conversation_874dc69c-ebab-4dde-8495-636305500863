using System;
using LibreHardwareMonitor.Hardware;
using System.Collections.Generic;
using System.Linq;
using N90.Shared;
using N90.Shared.Services;
using System.Management;
using System.Threading.Tasks;
using System.Diagnostics;
using N90.Service.Models;

namespace N90.Service
{
    public class HardwareReader
    {
        private readonly Computer computer;
        private readonly N90.Service.Utils.UpdateVisitor updateVisitor;
        private int _readCount = 0;

        // 传感器配置
        private ServiceAppSettings _appSettings;
        private Dictionary<string, ISensor> _availableSensors = new Dictionary<string, ISensor>();
        private bool _sensorsDiscovered = false;

        public HardwareReader()
        {
            computer = new Computer();
            updateVisitor = new N90.Service.Utils.UpdateVisitor();
            _appSettings = ServiceAppSettings.Load();
            InitializeComputer();
            Logger.LogInfo("HardwareReader initialized successfully");
        }

        private void InitializeComputer()
        {
            try
            {
                computer.Open();
                computer.IsCpuEnabled = true;
                computer.IsGpuEnabled = true;
                computer.IsMemoryEnabled = true;
                computer.IsMotherboardEnabled = true;
                computer.IsStorageEnabled = true;
                computer.IsNetworkEnabled = true; // 启用网络监控
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Failed to initialize hardware monitoring");
                throw; // 重新抛出异常，因为这是关键初始化步骤
            }
        }

        public HardwareData ReadHardwareData(HardwareData data, bool useFahrenheit = false)
        {
            computer.Accept(updateVisitor);
            var hddCount = 0;

            // 确保传感器已发现
            if (!_sensorsDiscovered)
            {
                DiscoverAllSensors();
            }

            _readCount++;

            // 使用配置中的传感器读取数据
            ReadConfiguredSensors(data, useFahrenheit);

            foreach (var hardwareItem in computer.Hardware)
            {
                hardwareItem.Update();
                switch (hardwareItem.HardwareType)
                {
                    case HardwareType.Cpu:
                        ProcessCPU(hardwareItem, data, useFahrenheit);
                        break;
                    case HardwareType.GpuNvidia:
                    case HardwareType.GpuAmd:
                    case HardwareType.GpuIntel:
                        ProcessGPU(hardwareItem, data, useFahrenheit);
                        break;
                    case HardwareType.Memory:
                        ProcessRAM(hardwareItem, data);
                        break;
                    case HardwareType.Motherboard:
                        ProcessMainboard(hardwareItem, data, useFahrenheit);
                        break;
                    // 根据用户选择的HDD设备处理
                    case HardwareType.Storage:
                        if (ShouldProcessHDD(hardwareItem))
                        {
                            ProcessHDD(hardwareItem, data, useFahrenheit);
                        }
                        break;
                    case HardwareType.SuperIO:
                        ProcessSuperIO(hardwareItem, data);
                        break;
                    case HardwareType.Network:
                        ProcessNetwork(hardwareItem, data);
                        break;
                }
            }

            return data;
        }
        private void ProcessSubHardware(HardwareData data, IHardware[] hardware, bool useFahrenheit = false)
        {
            foreach (var hardwareItem in hardware)
            {
                hardwareItem.Update();

                switch (hardwareItem.HardwareType)
                {
                    case HardwareType.SuperIO:
                        ProcessSuperIO(hardwareItem, data);
                        break;
                }
            }
        }

        private string GetSensorUnit(ISensor sensor)
        {
            switch (sensor.SensorType)
            {
                case SensorType.Voltage:
                    return " V";
                case SensorType.Clock:
                    return " MHz";
                case SensorType.Temperature:
                    return "°C";
                case SensorType.Load:
                    return "%";
                case SensorType.Fan:
                    return " RPM";
                case SensorType.Flow:
                    return " L/h";
                case SensorType.Control:
                    return "%";
                case SensorType.Level:
                    return "%";
                case SensorType.Factor:
                    return "";
                case SensorType.Power:
                    return " W";
                case SensorType.Data:
                    return " GB";
                case SensorType.SmallData:
                    return " MB";
                case SensorType.Throughput:
                    return " MB/s";
                case SensorType.Energy:
                    return " Wh";
                case SensorType.Current:
                    return " A";
                case SensorType.TimeSpan:
                    return " s";
                case SensorType.Noise:
                    return " dB";
                default:
                    //LoggerExtensions.LogDebugWithCaller($"Unknown sensor type: {sensor.SensorType}");
                    return "";
            }
        }

        /// <summary>
        /// 打印硬件关键信息到日志（简化版，只显示最重要的数据）
        /// </summary>
        private void PrintHardwareInfo(IHardware hardware)
        {
            try
            {
                Logger.LogInfo($"=== {hardware.Name} ({hardware.HardwareType}) ===");

                // 提取关键数据
                var keyData = ExtractKeyHardwareData(hardware);

                // 显示关键数据
                foreach (var data in keyData)
                {
                    Logger.LogInfo($"  {data.Key}: {data.Value}");
                }

                // 递归处理子硬件
                var subHardwareCount = hardware.SubHardware?.Count() ?? 0;
                if (subHardwareCount > 0)
                {
                    foreach (var subHardware in hardware.SubHardware)
                    {
                        PrintHardwareInfo(subHardware);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, $"Error printing hardware info for {hardware.Name}");
            }
        }

        /// <summary>
        /// 格式化传感器值，根据传感器类型选择合适的精度
        /// </summary>
        private string FormatSensorValue(ISensor sensor)
        {
            if (!sensor.Value.HasValue)
                return "N/A";

            var value = sensor.Value.Value;

            return sensor.SensorType switch
            {
                SensorType.Temperature => $"{value:F1}",
                SensorType.Voltage => $"{value:F3}",
                SensorType.Clock => $"{value:F0}",
                SensorType.Load => $"{value:F1}",
                SensorType.Fan => $"{value:F0}",
                SensorType.Flow => $"{value:F2}",
                SensorType.Control => $"{value:F1}",
                SensorType.Level => $"{value:F1}",
                SensorType.Power => $"{value:F2}",
                SensorType.Data => $"{value:F2}",
                SensorType.SmallData => $"{value:F1}",
                SensorType.Throughput => $"{value:F2}",
                SensorType.Energy => $"{value:F2}",
                SensorType.Current => $"{value:F3}",
                SensorType.TimeSpan => $"{value:F0}",
                SensorType.Noise => $"{value:F1}",
                SensorType.Factor => $"{value:F4}",
                _ => $"{value:F2}"
            };
        }

        /// <summary>
        /// 处理CPU硬件数据 - 根据配置选择特定传感器
        /// </summary>
        private void ProcessCPU(IHardware hardware, HardwareData data, bool useFahrenheit)
        {
            data.CPUModel.value = hardware.Name;
            //LoggerExtensions.LogDebugWithCaller($"🔧 Processing CPU: {hardware.Name}");

            // 获取配置中的传感器选择 - 对应关系：
            // CPUTemperature - selectedCpuTempSensor
            // CPUUsage - selectedCpuUsageSensor
            // CPUPower - selectedCpuVoltageSensor (注意：这里用的是Voltage配置)
            // CPUFanSpeed - selectedCpuFanSensor
            string selectedTempSensor = _appSettings.SelectedCpuTempSensor;
            string selectedUsageSensor = _appSettings.SelectedCpuUsageSensor;
            string selectedVoltageSensor = _appSettings.SelectedCpuVoltageSensor; // 对应CPUPower
            string selectedFanSensor = _appSettings.SelectedCpuFanSensor;

            //LoggerExtensions.LogDebugWithCaller($"📋 CPU Config - Temp: '{selectedTempSensor}', Usage: '{selectedUsageSensor}', Voltage: '{selectedVoltageSensor}', Fan: '{selectedFanSensor}'");

            int fanCount = 0;

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Temperature:
                        //LoggerExtensions.LogDebugWithCaller($"🌡️ Found CPU Temperature sensor: {sensor.Name} = {sensor.Value}");
                        if (ShouldUseCpuTemperatureSensor(sensor, selectedTempSensor))
                        {
                            if (useFahrenheit)
                            {
                                data.CPUTemperature.value = (sensor.Value.GetValueOrDefault() * 9 / 5) + 32;
                            }
                            else
                            {
                                data.CPUTemperature.value = sensor.Value.GetValueOrDefault();
                            }
                            //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED CPU Temperature: {data.CPUTemperature.value:F1}°{(useFahrenheit ? "F" : "C")} from sensor '{sensor.Name}'");
                        }
                        else
                        {
                            //LoggerExtensions.LogDebugWithCaller($"❌ SKIPPED CPU Temperature sensor: {sensor.Name}");
                        }
                        break;
                    case SensorType.Load:
                        //LoggerExtensions.LogDebugWithCaller($"📊 Found CPU Load sensor: {sensor.Name} = {sensor.Value}");
                        if (ShouldUseCpuUsageSensor(sensor, selectedUsageSensor))
                        {
                            data.CPUUsage.value = sensor.Value.GetValueOrDefault();
                            //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED CPU Usage: {data.CPUUsage.value:F1}% from sensor '{sensor.Name}'");
                        }
                        else
                        {
                            //LoggerExtensions.LogDebugWithCaller($"❌ SKIPPED CPU Load sensor: {sensor.Name}");
                        }
                        break;
                    case SensorType.Power:
                        //LoggerExtensions.LogDebugWithCaller($"⚡ Found CPU Power sensor: {sensor.Name} = {sensor.Value}");
                        // CPUPower对应selectedCpuVoltageSensor配置
                        if (ShouldUseCpuVoltageSensor(sensor, selectedVoltageSensor))
                        {
                            data.CPUPower.value = sensor.Value.GetValueOrDefault();
                            //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED CPU Power: {data.CPUPower.value:F1}W from sensor '{sensor.Name}'");
                        }
                        else
                        {
                            //LoggerExtensions.LogDebugWithCaller($"❌ SKIPPED CPU Power sensor: {sensor.Name}");
                        }
                        break;
                    case SensorType.Fan:
                        //LoggerExtensions.LogDebugWithCaller($"🌀 Found CPU Fan sensor: {sensor.Name} = {sensor.Value} (fanCount: {fanCount})");
                        if (ShouldUseCpuFanSensor(sensor, selectedFanSensor, fanCount))
                        {
                            data.CPUFanSpeed.value = sensor.Value.GetValueOrDefault();
                            //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED CPU Fan: {data.CPUFanSpeed.value:F0} RPM from sensor '{sensor.Name}'");
                            fanCount++;
                        }
                        else
                        {
                            //LoggerExtensions.LogDebugWithCaller($"❌ SKIPPED CPU Fan sensor: {sensor.Name}");
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU温度传感器 - 对应CPUTemperature
        /// </summary>
        private bool ShouldUseCpuTemperatureSensor(ISensor sensor, string selectedSensor)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                //LoggerExtensions.LogDebugWithCaller($"🔍 Checking CPU Temp sensor '{sensor.Name}' against config '{sensorNameFromConfig}' (from '{selectedSensor}')");

                // 检查传感器名称是否匹配
                bool matches = sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
                //LoggerExtensions.LogDebugWithCaller($"🔍 CPU Temp match result: {matches}");
                return matches;
            }
            // 默认使用Package温度传感器
            return sensor.Name.Contains("Package");
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU使用率传感器 - 对应CPUUsage
        /// </summary>
        private bool ShouldUseCpuUsageSensor(ISensor sensor, string selectedSensor)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                //LoggerExtensions.LogDebugWithCaller($"🔍 Checking CPU Usage sensor '{sensor.Name}' against config '{sensorNameFromConfig}' (from '{selectedSensor}')");

                // 检查传感器名称是否匹配
                bool matches = sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
                //LoggerExtensions.LogDebugWithCaller($"🔍 CPU Usage match result: {matches}");
                return matches;
            }
            // 默认使用Total使用率传感器
            return sensor.Name.Contains("Total");
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU电压传感器 - 对应CPUPower
        /// </summary>
        private bool ShouldUseCpuVoltageSensor(ISensor sensor, string selectedSensor)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                //LoggerExtensions.LogDebugWithCaller($"🔍 Checking CPU Voltage sensor '{sensor.Name}' against config '{sensorNameFromConfig}' (from '{selectedSensor}')");

                // 检查传感器名称是否匹配
                bool matches = sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
                //LoggerExtensions.LogDebugWithCaller($"🔍 CPU Voltage match result: {matches}");
                return matches;
            }
            // 默认使用Package功耗传感器
            return sensor.Name.Contains("Package");
        }

        /// <summary>
        /// 判断是否应该使用指定的CPU风扇传感器 - 对应CPUFanSpeed
        /// </summary>
        private bool ShouldUseCpuFanSensor(ISensor sensor, string selectedSensor, int fanCount)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                //LoggerExtensions.LogDebugWithCaller($"🔍 Checking CPU Fan sensor '{sensor.Name}' against config '{sensorNameFromConfig}' (from '{selectedSensor}')");

                // 检查传感器名称是否匹配
                bool matches = sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
                //LoggerExtensions.LogDebugWithCaller($"🔍 CPU Fan match result: {matches}");
                return matches;
            }
            // 默认使用第一个风扇传感器
            return fanCount == 0;
        }

        /// <summary>
        /// 从完整的传感器键名中提取传感器名称
        /// 例如：'Intel Core i9-10900K_Temperature_Core Max' → 'Core Max'
        /// </summary>
        private string ExtractSensorNameFromKey(string sensorKey)
        {
            if (string.IsNullOrEmpty(sensorKey))
                return string.Empty;

            // 传感器键名格式通常是：硬件名_传感器类型_传感器名称
            // 例如：Intel Core i9-10900K_Temperature_Core Max
            var parts = sensorKey.Split('_');
            if (parts.Length >= 3)
            {
                // 取最后一部分作为传感器名称
                string sensorName = parts[parts.Length - 1];
                //LoggerExtensions.LogDebugWithCaller($"🔍 Extracted sensor name '{sensorName}' from key '{sensorKey}'");
                return sensorName;
            }

            // 如果格式不符合预期，返回原始字符串
            //LoggerExtensions.LogDebugWithCaller($"🔍 Could not extract sensor name from key '{sensorKey}', using original");
            return sensorKey;
        }
        /// <summary>
        /// 处理SuperIO硬件数据 - 根据配置选择特定的风扇传感器
        /// 这里可能包含CPU风扇和机箱风扇
        /// </summary>
        private void ProcessSuperIO(IHardware hardware, HardwareData data)
        {
            // 获取配置中的风扇传感器选择
            string selectedCpuFanSensor = _appSettings.SelectedCpuFanSensor;
            string selectedCaseFan1Sensor = _appSettings.SelectedCaseFan1Sensor;
            string selectedCaseFan2Sensor = _appSettings.SelectedCaseFan2Sensor;

            //LoggerExtensions.LogDebugWithCaller($"Processing SuperIO: {hardware.Name}");

            int fanCount = 0;

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Fan:
                        //LoggerExtensions.LogDebugWithCaller($"Found SuperIO fan sensor: {sensor.Name} = {sensor.Value}");

                        // CPU风扇 - 对应selectedCpuFanSensor
                        if (ShouldUseCpuFanSensor(sensor, selectedCpuFanSensor, fanCount))
                        {
                            data.CPUFanSpeed.value = sensor.Value.GetValueOrDefault();
                            LoggerExtensions.LogHardwareData("SuperIO", "CPUFanSpeed", $"{data.CPUFanSpeed.value:F0} RPM (Sensor: {sensor.Name})");
                        }

                        // 机箱风扇1 - 对应selectedCaseFan1Sensor
                        if (ShouldUseCaseFan1Sensor(sensor, selectedCaseFan1Sensor, fanCount))
                        {
                            data.CaseFan1Speed.value = sensor.Value.GetValueOrDefault();
                            LoggerExtensions.LogHardwareData("SuperIO", "CaseFan1Speed", $"{data.CaseFan1Speed.value:F0} RPM (Sensor: {sensor.Name})");
                        }

                        // 机箱风扇2 - 对应selectedCaseFan2Sensor
                        if (ShouldUseCaseFan2Sensor(sensor, selectedCaseFan2Sensor, fanCount))
                        {
                            data.CaseFan2Speed.value = sensor.Value.GetValueOrDefault();
                            LoggerExtensions.LogHardwareData("SuperIO", "CaseFan2Speed", $"{data.CaseFan2Speed.value:F0} RPM (Sensor: {sensor.Name})");
                        }

                        fanCount++;
                        break;
                }
            }
        }

        /// <summary>
        /// 处理网络硬件数据 - UploadSpeed和DownloadSpeed都对应selectedNetworkInterface配置
        /// </summary>
        private void ProcessNetwork(IHardware hardware, HardwareData data)
        {
            //LoggerExtensions.LogDebugWithCaller($"🌐 Processing network hardware: {hardware.Name}");

            // 获取配置中选择的网络接口 - UploadSpeed和DownloadSpeed都使用这个配置
            string selectedInterface = _appSettings.SelectedNetworkInterface;

            //LoggerExtensions.LogDebugWithCaller($"📋 Network Config: '{selectedInterface}'");

            // 网络接口选择逻辑：
            // 1. 如果配置为空，处理所有网络接口（累加速度）
            // 2. 如果配置了特定接口，只处理匹配的接口
            bool shouldProcessInterface = false;

            if (string.IsNullOrEmpty(selectedInterface))
            {
                // 配置为空，处理所有网络接口
                shouldProcessInterface = true;
                //LoggerExtensions.LogDebugWithCaller($"✅ No network interface configured, processing all interfaces: {hardware.Name}");
            }
            else
            {
                // 配置了特定接口，检查是否匹配
                if (hardware.Name.Contains(selectedInterface, StringComparison.OrdinalIgnoreCase))
                {
                    shouldProcessInterface = true;
                    //LoggerExtensions.LogDebugWithCaller($"✅ Found configured network interface: {hardware.Name}");
                }
                else
                {
                    //LoggerExtensions.LogDebugWithCaller($"❌ Skipping network interface '{hardware.Name}' - not matching configured interface '{selectedInterface}'");
                }
            }

            if (!shouldProcessInterface)
            {
                return;
            }

            //LoggerExtensions.LogDebugWithCaller($"🎯 SELECTED Network Interface: {hardware.Name}");

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Throughput:
                        //LoggerExtensions.LogDebugWithCaller($"📈 Found Network Throughput sensor: {sensor.Name} = {sensor.Value}");
                        // UploadSpeed和DownloadSpeed - 都对应selectedNetworkInterface
                        ProcessNetworkThroughputSensor(sensor, data);
                        break;

                    case SensorType.Data:
                        // 数据传输量传感器（累计值）
                        // //LoggerExtensions.LogDebugWithCaller($"📊 Found Network Data sensor: {sensor.Name} = {sensor.Value}");
                        break;

                    default:
                        // //LoggerExtensions.LogDebugWithCaller($"❓ Unknown network sensor type: {sensor.SensorType}");
                        break;
                }
            }
        }

        /// <summary>
        /// 处理网络吞吐量传感器 - 支持累加多个网络接口的速度
        /// </summary>
        private void ProcessNetworkThroughputSensor(ISensor sensor, HardwareData data)
        {
            double speedKbps = sensor.Value.GetValueOrDefault() / 1024.0;

            // 网络吞吐量传感器
            if (sensor.Name.Contains("Upload") || sensor.Name.Contains("Sent") || sensor.Name.Contains("Tx"))
            {
                // 上传速度 (通常以 B/s 为单位，转换为 KB/s)
                // 如果配置为空（处理所有接口），则累加速度
                if (string.IsNullOrEmpty(_appSettings.SelectedNetworkInterface))
                {
                    data.UploadSpeed.value += speedKbps;
                    //LoggerExtensions.LogDebugWithCaller($"✅ ACCUMULATED Upload Speed: +{speedKbps:F2} KB/s (Total: {data.UploadSpeed.value:F2} KB/s) from sensor '{sensor.Name}'");
                }
                else
                {
                    data.UploadSpeed.value = speedKbps;
                    //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED Upload Speed: {speedKbps:F2} KB/s from sensor '{sensor.Name}'");
                }
            }
            else if (sensor.Name.Contains("Download") || sensor.Name.Contains("Received") || sensor.Name.Contains("Rx"))
            {
                // 下载速度 (通常以 B/s 为单位，转换为 KB/s)
                // 如果配置为空（处理所有接口），则累加速度
                if (string.IsNullOrEmpty(_appSettings.SelectedNetworkInterface))
                {
                    data.DownloadSpeed.value += speedKbps;
                    //LoggerExtensions.LogDebugWithCaller($"✅ ACCUMULATED Download Speed: +{speedKbps:F2} KB/s (Total: {data.DownloadSpeed.value:F2} KB/s) from sensor '{sensor.Name}'");
                }
                else
                {
                    data.DownloadSpeed.value = speedKbps;
                    //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED Download Speed: {speedKbps:F2} KB/s from sensor '{sensor.Name}'");
                }
            }
            else
            {
                // 如果传感器名称不明确，尝试根据索引分配
                // 通常第一个是下载，第二个是上传
                var hardware = sensor.Hardware;
                var throughputSensors = hardware.Sensors.Where(s => s.SensorType == SensorType.Throughput).ToList();
                var currentIndex = throughputSensors.IndexOf(sensor);

                //LoggerExtensions.LogDebugWithCaller($"🔍 Unclear sensor name '{sensor.Name}', using index {currentIndex}");

                if (currentIndex == 0)
                {
                    // 下载速度
                    if (string.IsNullOrEmpty(_appSettings.SelectedNetworkInterface))
                    {
                        data.DownloadSpeed.value += speedKbps;
                        //LoggerExtensions.LogDebugWithCaller($"✅ ACCUMULATED Download Speed (index 0): +{speedKbps:F2} KB/s (Total: {data.DownloadSpeed.value:F2} KB/s) from sensor '{sensor.Name}'");
                    }
                    else
                    {
                        data.DownloadSpeed.value = speedKbps;
                        //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED Download Speed (index 0): {speedKbps:F2} KB/s from sensor '{sensor.Name}'");
                    }
                }
                else if (currentIndex == 1)
                {
                    // 上传速度
                    if (string.IsNullOrEmpty(_appSettings.SelectedNetworkInterface))
                    {
                        data.UploadSpeed.value += speedKbps;
                        //LoggerExtensions.LogDebugWithCaller($"✅ ACCUMULATED Upload Speed (index 1): +{speedKbps:F2} KB/s (Total: {data.UploadSpeed.value:F2} KB/s) from sensor '{sensor.Name}'");
                    }
                    else
                    {
                        data.UploadSpeed.value = speedKbps;
                        //LoggerExtensions.LogDebugWithCaller($"✅ SELECTED Upload Speed (index 1): {speedKbps:F2} KB/s from sensor '{sensor.Name}'");
                    }
                }
                else
                {
                    //LoggerExtensions.LogDebugWithCaller($"❌ SKIPPED sensor '{sensor.Name}' - index {currentIndex} not handled");
                }
            }
        }

        /// <summary>
        /// 处理GPU硬件数据 - 所有GPU数据项都对应selectedGpuSensor配置
        /// GPUTemperature, GPUMemoryUsage, GPUPower, GPUModel - 都对应selectedGpuSensor
        /// </summary>
        private void ProcessGPU(IHardware hardware, HardwareData data, bool useFahrenheit)
        {
            // 获取配置中选择的GPU - 所有GPU数据项都使用这个配置
            string selectedGpu = _appSettings.SelectedGpuSensor;

            // GPU选择逻辑：
            // 1. 如果配置为空，使用第一个GPU（通过静态变量控制）
            // 2. 如果配置了特定GPU，只处理匹配的GPU
            bool shouldProcessGpu = false;

            if (string.IsNullOrEmpty(selectedGpu))
            {
                // 配置为空，使用第一个GPU
                if (data.GPUModel.value == null || string.IsNullOrEmpty(data.GPUModel.value))
                {
                    shouldProcessGpu = true;
                }
            }
            else
            {
                // 配置了特定GPU，检查是否匹配
                // 尝试多种匹配方式
                bool exactMatch = hardware.Name.Equals(selectedGpu, StringComparison.OrdinalIgnoreCase);
                bool containsMatch = hardware.Name.Contains(selectedGpu, StringComparison.OrdinalIgnoreCase);
                bool reverseContainsMatch = selectedGpu.Contains(hardware.Name, StringComparison.OrdinalIgnoreCase);

                if (exactMatch || containsMatch || reverseContainsMatch)
                {
                    shouldProcessGpu = true;
                }
            }

            if (!shouldProcessGpu)
            {
                return;
            }

            // 设置GPU型号 - 对应selectedGpuSensor
            data.GPUModel.value = hardware.Name;

            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Temperature:
                        // GPUTemperature - 对应selectedGpuSensor
                        if (useFahrenheit)
                        {
                            data.GPUTemperature.value = (sensor.Value.GetValueOrDefault() * 9 / 5) + 32;
                        }
                        else
                        {
                            data.GPUTemperature.value = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Load:
                        // GPUMemoryUsage - 对应selectedGpuSensor
                        if (sensor.Name.Contains("Memory"))
                        {
                            data.GPUMemoryUsage.value = sensor.Value.GetValueOrDefault();
                        }
                        break;
                    case SensorType.Power:
                        // GPUPower - 对应selectedGpuSensor
                        data.GPUPower.value = sensor.Value.GetValueOrDefault();
                        break;
                }
            }
        }

        private void ProcessRAM(IHardware hardware, HardwareData data)
        {
            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Load:
                        // 内存使用率百分比
                        data.RAMUsage.value = sensor.Value.GetValueOrDefault();
                        break;

                    case SensorType.Data:
                        // 精确匹配 "Memory Available" 传感器
                        if (sensor.Name.Equals("Memory Available", StringComparison.OrdinalIgnoreCase))
                        {
                            data.AvailableRAM.value = sensor.Value.GetValueOrDefault();
                        }
                        else if (sensor.Name.Contains("Available", StringComparison.OrdinalIgnoreCase))
                        {
                            // 备选方案：包含 "Available" 的传感器
                            data.AvailableRAM.value = sensor.Value.GetValueOrDefault();
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 处理主板硬件数据 - 根据配置选择特定的机箱风扇传感器
        /// CaseFan1Speed - selectedCaseFan1Sensor
        /// CaseFan2Speed - selectedCaseFan2Sensor
        /// </summary>
        private void ProcessMainboard(IHardware hardware, HardwareData data, bool useFahrenheit)
        {
            if(hardware.SubHardware?.Any() == true)
            {
                ProcessSubHardware(data, hardware.SubHardware, useFahrenheit);
            }

            // 获取配置中的机箱风扇传感器选择
            string selectedCaseFan1Sensor = _appSettings.SelectedCaseFan1Sensor;
            string selectedCaseFan2Sensor = _appSettings.SelectedCaseFan2Sensor;

            int caseFanCount = 0;

            foreach (var sensor in hardware.Sensors)
            {
                if (sensor.SensorType == SensorType.Fan)
                {
                    // CaseFan1Speed - 对应selectedCaseFan1Sensor
                    if (ShouldUseCaseFan1Sensor(sensor, selectedCaseFan1Sensor, caseFanCount))
                    {
                        data.CaseFan1Speed.value = sensor.Value.GetValueOrDefault();
                    }

                    // CaseFan2Speed - 对应selectedCaseFan2Sensor
                    if (ShouldUseCaseFan2Sensor(sensor, selectedCaseFan2Sensor, caseFanCount))
                    {
                        data.CaseFan2Speed.value = sensor.Value.GetValueOrDefault();
                    }

                    caseFanCount++;
                }
            }
        }

        /// <summary>
        /// 判断是否应该使用指定的机箱风扇1传感器 - 对应CaseFan1Speed
        /// </summary>
        private bool ShouldUseCaseFan1Sensor(ISensor sensor, string selectedSensor, int fanCount)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                // 检查传感器名称是否匹配
                return sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
            }
            // 默认使用第一个风扇传感器
            return fanCount == 0;
        }

        /// <summary>
        /// 判断是否应该使用指定的机箱风扇2传感器 - 对应CaseFan2Speed
        /// </summary>
        private bool ShouldUseCaseFan2Sensor(ISensor sensor, string selectedSensor, int fanCount)
        {
            if (!string.IsNullOrEmpty(selectedSensor))
            {
                // 配置的传感器名称可能是完整键名，需要提取传感器名称部分
                string sensorNameFromConfig = ExtractSensorNameFromKey(selectedSensor);
                // 检查传感器名称是否匹配
                return sensor.Name.Contains(sensorNameFromConfig, StringComparison.OrdinalIgnoreCase);
            }
            // 默认使用第二个风扇传感器
            return fanCount == 1;
        }

        /// <summary>
        /// 判断是否应该处理指定的HDD设备
        /// </summary>
        private bool ShouldProcessHDD(IHardware hardware)
        {
            try
            {
                // 获取用户选择的HDD设备名称
                string selectedHdd = _appSettings.SelectedHddSensor;

                // 如果没有配置选择的HDD，不处理任何HDD（避免默认行为）
                if (string.IsNullOrEmpty(selectedHdd))
                {
                    return false;
                }

                // 检查硬件名称是否匹配用户选择的HDD
                return hardware.Name.Equals(selectedHdd, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error checking HDD selection: {ex.Message}");
                return false;
            }
        }

        private void ProcessHDD(IHardware hardware, HardwareData data, bool useFahrenheit)
        {
            foreach (var sensor in hardware.Sensors)
            {
                switch (sensor.SensorType)
                {
                    case SensorType.Temperature:
                        // HDD 温度传感器
                        if (useFahrenheit)
                        {
                            data.HDDTemperature.value = ConvertToFahrenheit(sensor.Value.GetValueOrDefault());
                        }
                        else
                        {
                            data.HDDTemperature.value = sensor.Value.GetValueOrDefault();
                        }
                        break;

                    case SensorType.Load:
                        // 检查是否为 Used Space 传感器
                        if (sensor.Name.Equals("Used Space", StringComparison.OrdinalIgnoreCase))
                        {
                            // 这是真正的已用空间传感器
                            data.HDDUsage.value = sensor.Value.GetValueOrDefault();
                        }
                        break;

                    case SensorType.Data:
                        // 只有明确是 "Used Space" 的 Data 传感器才考虑作为已用空间
                        if (sensor.Name.Equals("Used Space", StringComparison.OrdinalIgnoreCase))
                        {
                            data.HDDUsage.value = sensor.Value.GetValueOrDefault();
                        }
                        break;

                    case SensorType.Level:
                        // 只有明确是 "Used Space" 的 Level 传感器才考虑作为已用空间
                        if (sensor.Name.Equals("Used Space", StringComparison.OrdinalIgnoreCase))
                        {
                            data.HDDUsage.value = sensor.Value.GetValueOrDefault();
                        }
                        break;

                    default:
                        // 尝试根据传感器名称推断是否为已用空间
                        if (sensor.Name.Contains("Used Space", StringComparison.OrdinalIgnoreCase) ||
                            sensor.Name.Contains("Usage", StringComparison.OrdinalIgnoreCase) ||
                            (sensor.Name.Contains("Used", StringComparison.OrdinalIgnoreCase) &&
                             sensor.Name.Contains("%", StringComparison.OrdinalIgnoreCase)))
                        {
                            data.HDDUsage.value = sensor.Value.GetValueOrDefault();
                        }
                        break;
                }
            }
        }

        public async void GetCpuUsage(HardwareData hardwareData)
        {
            using (var pc = new PerformanceCounter("Processor", "% Processor Time", "_Total"))
            {
                float usage = pc.NextValue();
                await Task.Delay(1000); // 必须等待第二次读取
                usage = pc.NextValue();
                Console.WriteLine($"任务管理器风格: {usage}%");
                hardwareData.CPUUsage.value = usage;
            }
        }

        // GetNetworkBytes方法已移除，现在使用LibreHardwareMonitor的Network硬件类型

        /// <summary>
        /// 记录硬件摘要信息
        /// </summary>
        private void LogHardwareSummary()
        {
            try
            {
                Logger.LogInfo("=== HARDWARE SUMMARY ===");

                var totalSensors = 0;
                var validSensors = 0;
                var hardwareTypes = new Dictionary<HardwareType, int>();
                var sensorTypes = new Dictionary<SensorType, int>();

                foreach (var hardware in computer.Hardware)
                {
                    // 统计硬件类型
                    if (!hardwareTypes.ContainsKey(hardware.HardwareType))
                        hardwareTypes[hardware.HardwareType] = 0;
                    hardwareTypes[hardware.HardwareType]++;

                    // 统计传感器
                    CountSensorsRecursive(hardware, ref totalSensors, ref validSensors, sensorTypes);
                }

                // 记录硬件统计
                Logger.LogInfo($"Hardware Components: {computer.Hardware.Count} total");
                foreach (var kvp in hardwareTypes.OrderBy(x => x.Key.ToString()))
                {
                    Logger.LogInfo($"  {kvp.Key}: {kvp.Value} component(s)");
                }

                // 记录传感器统计
                Logger.LogInfo($"Sensors: {validSensors}/{totalSensors} valid");
                foreach (var kvp in sensorTypes.OrderByDescending(x => x.Value).Take(10))
                {
                    Logger.LogInfo($"  {kvp.Key}: {kvp.Value} sensor(s)");
                }

                Logger.LogInfo("=== END SUMMARY ===");
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error generating hardware summary");
            }
        }

        /// <summary>
        /// 提取硬件的关键数据
        /// </summary>
        private Dictionary<string, string> ExtractKeyHardwareData(IHardware hardware)
        {
            var keyData = new Dictionary<string, string>();

            try
            {
                var sensors = hardware.Sensors?.Where(s => s.Value.HasValue).ToList() ?? new List<ISensor>();

                switch (hardware.HardwareType)
                {
                    case HardwareType.Cpu:
                        ExtractCpuKeyData(sensors, keyData);
                        break;

                    case HardwareType.GpuNvidia:
                    case HardwareType.GpuAmd:
                    case HardwareType.GpuIntel:
                        ExtractGpuKeyData(sensors, keyData);
                        break;

                    case HardwareType.Memory:
                        ExtractMemoryKeyData(sensors, keyData);
                        break;

                    case HardwareType.Motherboard:
                        ExtractMotherboardKeyData(sensors, keyData);
                        break;

                    case HardwareType.Storage:
                        ExtractStorageKeyData(sensors, keyData);
                        break;

                    case HardwareType.Network:
                        ExtractNetworkKeyData(sensors, keyData);
                        break;

                    default:
                        // 对于其他类型，显示基本信息
                        ExtractGenericKeyData(sensors, keyData);
                        break;
                }

                // 如果没有找到关键数据，至少显示传感器数量
                if (keyData.Count == 0)
                {
                    keyData["Status"] = $"{sensors.Count} sensors available";
                }
            }
            catch (Exception ex)
            {
                keyData["Error"] = $"Failed to extract data: {ex.Message}";
            }

            return keyData;
        }

        /// <summary>
        /// 提取CPU关键数据
        /// </summary>
        private void ExtractCpuKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // CPU总使用率
            var totalLoad = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load &&
                (s.Name.Contains("Total") || s.Name.Contains("CPU Total")));
            if (totalLoad != null)
            {
                keyData["CPU Usage"] = $"{totalLoad.Value:F1}%";
            }

            // CPU温度（Package或最高温度）
            var packageTemp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature &&
                (s.Name.Contains("Package") || s.Name.Contains("Tctl")));
            if (packageTemp != null)
            {
                keyData["CPU Temperature"] = $"{packageTemp.Value:F1}°C";
            }

            // CPU功耗
            var packagePower = sensors.FirstOrDefault(s => s.SensorType == SensorType.Power &&
                s.Name.Contains("Package"));
            if (packagePower != null)
            {
                keyData["CPU Power"] = $"{packagePower.Value:F1}W";
            }

            // CPU频率（基础频率）
            var baseClock = sensors.FirstOrDefault(s => s.SensorType == SensorType.Clock &&
                (s.Name.Contains("Bus Speed") || s.Name.Contains("BCLK")));
            if (baseClock != null)
            {
                keyData["CPU Base Clock"] = $"{baseClock.Value:F0} MHz";
            }
        }

        /// <summary>
        /// 提取GPU关键数据
        /// </summary>
        private void ExtractGpuKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // GPU使用率
            var gpuLoad = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load &&
                (s.Name.Contains("GPU Core") || s.Name.Contains("GPU")));
            if (gpuLoad != null)
            {
                keyData["GPU Usage"] = $"{gpuLoad.Value:F1}%";
            }

            // GPU温度
            var gpuTemp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature &&
                s.Name.Contains("GPU"));
            if (gpuTemp != null)
            {
                keyData["GPU Temperature"] = $"{gpuTemp.Value:F1}°C";
            }

            // GPU功耗
            var gpuPower = sensors.FirstOrDefault(s => s.SensorType == SensorType.Power);
            if (gpuPower != null)
            {
                keyData["GPU Power"] = $"{gpuPower.Value:F1}W";
            }

            // 显存使用率
            var memoryLoad = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load &&
                s.Name.Contains("Memory"));
            if (memoryLoad != null)
            {
                keyData["GPU Memory"] = $"{memoryLoad.Value:F1}%";
            }
        }

        /// <summary>
        /// 提取内存关键数据
        /// </summary>
        private void ExtractMemoryKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // 内存使用率
            var memoryLoad = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load);
            if (memoryLoad != null)
            {
                keyData["Memory Usage"] = $"{memoryLoad.Value:F1}%";
            }

            // 已使用内存
            var usedMemory = sensors.FirstOrDefault(s => s.SensorType == SensorType.Data &&
                s.Name.Contains("Used"));
            if (usedMemory != null)
            {
                keyData["Memory Used"] = $"{usedMemory.Value:F1} GB";
            }

            // 可用内存
            var availableMemory = sensors.FirstOrDefault(s => s.SensorType == SensorType.Data &&
                s.Name.Contains("Available"));
            if (availableMemory != null)
            {
                keyData["Memory Available"] = $"{availableMemory.Value:F1} GB";
            }
        }

        /// <summary>
        /// 提取主板关键数据
        /// </summary>
        private void ExtractMotherboardKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // 主板温度
            var systemTemp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature);
            if (systemTemp != null)
            {
                keyData["System Temperature"] = $"{systemTemp.Value:F1}°C";
            }

            // 风扇转速（第一个风扇）
            var fan = sensors.FirstOrDefault(s => s.SensorType == SensorType.Fan);
            if (fan != null)
            {
                keyData["System Fan"] = $"{fan.Value:F0} RPM";
            }

            // 电压（主要电压）
            var voltage = sensors.FirstOrDefault(s => s.SensorType == SensorType.Voltage &&
                (s.Name.Contains("CPU") || s.Name.Contains("VCore")));
            if (voltage != null)
            {
                keyData["CPU Voltage"] = $"{voltage.Value:F3}V";
            }
        }

        /// <summary>
        /// 提取存储设备关键数据
        /// </summary>
        private void ExtractStorageKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // 存储温度
            var temp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature);
            if (temp != null)
            {
                keyData["Storage Temperature"] = $"{temp.Value:F1}°C";
            }

            // 存储使用率
            var load = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load);
            if (load != null)
            {
                keyData["Storage Usage"] = $"{load.Value:F1}%";
            }

            // 读写速度
            var throughput = sensors.FirstOrDefault(s => s.SensorType == SensorType.Throughput);
            if (throughput != null)
            {
                keyData["Storage Throughput"] = $"{throughput.Value:F1} MB/s";
            }
        }

        /// <summary>
        /// 提取网络关键数据
        /// </summary>
        private void ExtractNetworkKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // 网络吞吐量
            var throughput = sensors.FirstOrDefault(s => s.SensorType == SensorType.Throughput);
            if (throughput != null)
            {
                keyData["Network Throughput"] = $"{throughput.Value:F1} MB/s";
            }

            // 数据传输
            var dataUp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Data &&
                s.Name.Contains("Upload"));
            if (dataUp != null)
            {
                keyData["Upload"] = $"{dataUp.Value:F1} MB";
            }

            var dataDown = sensors.FirstOrDefault(s => s.SensorType == SensorType.Data &&
                s.Name.Contains("Download"));
            if (dataDown != null)
            {
                keyData["Download"] = $"{dataDown.Value:F1} MB";
            }
        }

        /// <summary>
        /// 提取通用关键数据
        /// </summary>
        private void ExtractGenericKeyData(List<ISensor> sensors, Dictionary<string, string> keyData)
        {
            // 温度（如果有）
            var temp = sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature);
            if (temp != null)
            {
                keyData["Temperature"] = $"{temp.Value:F1}°C";
            }

            // 负载（如果有）
            var load = sensors.FirstOrDefault(s => s.SensorType == SensorType.Load);
            if (load != null)
            {
                keyData["Load"] = $"{load.Value:F1}%";
            }

            // 功耗（如果有）
            var power = sensors.FirstOrDefault(s => s.SensorType == SensorType.Power);
            if (power != null)
            {
                keyData["Power"] = $"{power.Value:F1}W";
            }

            // 如果以上都没有，显示传感器数量
            if (keyData.Count == 0)
            {
                keyData["Sensors"] = $"{sensors.Count} available";
            }
        }

        /// <summary>
        /// 打印所有传感器的详细信息（确保不遗漏任何数据）
        /// </summary>
        private void PrintAllSensorDetails(IHardware hardware)
        {
            try
            {
                Logger.LogInfo($"=== ALL SENSORS FOR {hardware.Name} ===");

                var allSensors = hardware.Sensors?.ToList() ?? new List<ISensor>();
                Logger.LogInfo($"Total sensors found: {allSensors.Count}");

                for (int i = 0; i < allSensors.Count; i++)
                {
                    var sensor = allSensors[i];
                    string unit = GetSensorUnit(sensor);
                    string formattedValue = FormatSensorValue(sensor);
                    string status = sensor.Value.HasValue ? "VALID" : "INVALID";

                    Logger.LogInfo($"[{i+1:D3}] {sensor.SensorType} | {sensor.Name} | {formattedValue}{unit} | {status}");
                    Logger.LogInfo($"      ID: {sensor.Identifier} | Index: {sensor.Index}");

                    if (sensor.Value.HasValue)
                    {
                        Logger.LogInfo($"      Range: {sensor.Min?.ToString("F2") ?? "N/A"} ~ {sensor.Max?.ToString("F2") ?? "N/A"}");
                    }
                }

                // 递归处理子硬件
                foreach (var subHardware in hardware.SubHardware ?? Enumerable.Empty<IHardware>())
                {
                    PrintAllSensorDetails(subHardware);
                }

                Logger.LogInfo($"=== END ALL SENSORS FOR {hardware.Name} ===");
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, $"Error printing all sensor details for {hardware.Name}");
            }
        }

        /// <summary>
        /// 递归统计传感器数量
        /// </summary>
        private void CountSensorsRecursive(IHardware hardware, ref int totalSensors, ref int validSensors, Dictionary<SensorType, int> sensorTypes)
        {
            foreach (var sensor in hardware.Sensors)
            {
                totalSensors++;
                if (sensor.Value.HasValue)
                {
                    validSensors++;
                }

                // 统计传感器类型
                if (!sensorTypes.ContainsKey(sensor.SensorType))
                    sensorTypes[sensor.SensorType] = 0;
                sensorTypes[sensor.SensorType]++;
            }

            // 递归处理子硬件
            foreach (var subHardware in hardware.SubHardware)
            {
                CountSensorsRecursive(subHardware, ref totalSensors, ref validSensors, sensorTypes);
            }
        }

        /// <summary>
        /// 使用配置中的传感器读取数据 - 现在主要用于特殊传感器处理
        /// </summary>
        private void ReadConfiguredSensors(HardwareData data, bool useFahrenheit)
        {
            try
            {
                //LoggerExtensions.LogDebugWithCaller("📋 === SENSOR CONFIGURATION ===");

                // 记录当前配置的传感器选择
                //LoggerExtensions.LogDebugWithCaller($"🌐 Network Interface: '{_appSettings.SelectedNetworkInterface}'");
                //LoggerExtensions.LogDebugWithCaller($"🎮 GPU Sensor: '{_appSettings.SelectedGpuSensor}'");
                //LoggerExtensions.LogDebugWithCaller($"🌡️ CPU Temp Sensor: '{_appSettings.SelectedCpuTempSensor}'");
                //LoggerExtensions.LogDebugWithCaller($"📊 CPU Usage Sensor: '{_appSettings.SelectedCpuUsageSensor}'");
                //LoggerExtensions.LogDebugWithCaller($"⚡ CPU Voltage Sensor: '{_appSettings.SelectedCpuVoltageSensor}'");
                //LoggerExtensions.LogDebugWithCaller($"🌀 CPU Fan Sensor: '{_appSettings.SelectedCpuFanSensor}'");
                //LoggerExtensions.LogDebugWithCaller($"🌀 Case Fan1 Sensor: '{_appSettings.SelectedCaseFan1Sensor}'");
                //LoggerExtensions.LogDebugWithCaller($"🌀 Case Fan2 Sensor: '{_appSettings.SelectedCaseFan2Sensor}'");
                //LoggerExtensions.LogDebugWithCaller("📋 === END CONFIGURATION ===");

                // 注意：现在传感器选择逻辑已经移到各个Process方法中
                // 这里主要用于特殊的传感器处理或覆盖逻辑

                // var sensorSettings = _appSettings.GetSensorSettings();

                // 如果有特定的传感器键值对配置，可以在这里处理
                // if (sensorSettings.Count > 0)
                // {
                //     //LoggerExtensions.LogDebugWithCaller($"Found {sensorSettings.Count} sensor settings in configuration");
                //
                //     // 读取特定配置的传感器（如果存在键值对形式的配置）
                //     foreach (var setting in sensorSettings)
                //     {
                //         //LoggerExtensions.LogDebugWithCaller($"Sensor setting: {setting.Key} = {setting.Value}");
                //     }
                // }

                // Logger.LogDebug("Configured sensors processing completed");
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error reading configured sensors");
                // 配置读取失败不影响主要的传感器读取流程
                // 因为传感器选择逻辑已经移到各个Process方法中
            }
        }

        /// <summary>
        /// 读取指定传感器的值
        /// </summary>
        private float? ReadSensorValue(string sensorKey)
        {
            if (string.IsNullOrEmpty(sensorKey) || !_availableSensors.TryGetValue(sensorKey, out ISensor sensor))
            {
                return null;
            }

            return sensor.Value;
        }

        /// <summary>
        /// 读取GPU传感器数据
        /// </summary>
        private void ReadGpuSensorData(string gpuSensorKey, HardwareData data, bool useFahrenheit)
        {
            try
            {
                // GPU传感器键通常是硬件名称，需要找到对应的硬件并读取其所有传感器
                var gpuHardware = computer.Hardware.FirstOrDefault(h =>
                    (h.HardwareType == HardwareType.GpuNvidia || h.HardwareType == HardwareType.GpuAmd || h.HardwareType == HardwareType.GpuIntel) &&
                    h.Name == gpuSensorKey);

                if (gpuHardware != null)
                {
                    gpuHardware.Update();

                    // 读取GPU温度
                    var tempSensor = gpuHardware.Sensors.FirstOrDefault(s => s.SensorType == SensorType.Temperature);
                    if (tempSensor?.Value.HasValue == true)
                    {
                        data.GPUTemperature.value = useFahrenheit ? ConvertToFahrenheit(tempSensor.Value.Value) : tempSensor.Value.Value;
                    }

                    // 读取GPU内存使用率
                    var memSensor = gpuHardware.Sensors.FirstOrDefault(s => s.SensorType == SensorType.Load && s.Name.Contains("GPU Memory"));
                    if (memSensor?.Value.HasValue == true)
                    {
                        data.GPUMemoryUsage.value = memSensor.Value.Value;
                    }

                    Logger.LogDebug($"GPU data from configured sensor: Temp={data.GPUTemperature.value}°{(useFahrenheit ? "F" : "C")}, MemUsage={data.GPUMemoryUsage.value}%");
                }
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error reading GPU sensor data");
            }
        }

        /// <summary>
        /// 读取风扇传感器数据
        /// </summary>
        private void ReadFanSensors(Dictionary<string, string> sensorSettings, HardwareData data)
        {
            try
            {
                // 读取CPU风扇
                if (sensorSettings.TryGetValue("selectedCpuFanSensor", out string cpuFanSensor))
                {
                    var fanValue = ReadSensorValue(cpuFanSensor);
                    if (fanValue.HasValue)
                    {
                        data.CPUFanSpeed.value = fanValue.Value;
                        Logger.LogDebug($"CPU Fan from configured sensor: {data.CPUFanSpeed.value} RPM");
                    }
                }

                // 读取机箱风扇1
                if (sensorSettings.TryGetValue("selectedCaseFan1Sensor", out string caseFan1Sensor))
                {
                    var fanValue = ReadSensorValue(caseFan1Sensor);
                    if (fanValue.HasValue)
                    {
                        data.CaseFan1Speed.value = fanValue.Value;
                        Logger.LogDebug($"Case Fan 1 from configured sensor: {data.CaseFan1Speed.value} RPM");
                    }
                }

                // 读取机箱风扇2
                if (sensorSettings.TryGetValue("selectedCaseFan2Sensor", out string caseFan2Sensor))
                {
                    var fanValue = ReadSensorValue(caseFan2Sensor);
                    if (fanValue.HasValue)
                    {
                        data.CaseFan2Speed.value = fanValue.Value;
                        Logger.LogDebug($"Case Fan 2 from configured sensor: {data.CaseFan2Speed.value} RPM");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error reading fan sensors");
            }
        }

        /// <summary>
        /// 回退到默认传感器读取方法
        /// </summary>
        private void ReadDefaultSensors(HardwareData data, bool useFahrenheit)
        {
            //Logger.LogInfo("Falling back to default sensor reading method");

            // 这里保留原来的硬件遍历逻辑作为回退方案
            var hddCount = 0;
            foreach (var hardwareItem in computer.Hardware)
            {
                hardwareItem.Update();

                switch (hardwareItem.HardwareType)
                {
                    case HardwareType.Cpu:
                        ProcessCPU(hardwareItem, data, useFahrenheit);
                        break;
                    case HardwareType.GpuNvidia:
                    case HardwareType.GpuAmd:
                    case HardwareType.GpuIntel:
                        ProcessGPU(hardwareItem, data, useFahrenheit);
                        break;
                    case HardwareType.Memory:
                        ProcessRAM(hardwareItem, data);
                        break;
                    case HardwareType.Motherboard:
                        ProcessMainboard(hardwareItem, data, useFahrenheit);
                        break;
                    case HardwareType.Storage:
                        if (ShouldProcessHDD(hardwareItem))
                        {
                            ProcessHDD(hardwareItem, data, useFahrenheit);
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 转换为华氏度
        /// </summary>
        private float ConvertToFahrenheit(float celsius)
        {
            return celsius * 9.0f / 5.0f + 32.0f;
        }

        /// <summary>
        /// 发现所有可用传感器并建立索引 - 与Client端逻辑保持一致
        /// </summary>
        public void DiscoverAllSensors()
        {
            if (_sensorsDiscovered) return;

            try
            {
                //Logger.LogInfo("Discovering all available sensors...");
                _availableSensors.Clear();

                foreach (var hardware in computer.Hardware)
                {
                    hardware.Update();
                    string hardwareName = hardware.Name;

                    // 添加硬件本身的传感器
                    ProcessSensors(hardware.Sensors, hardwareName);

                    // 添加子硬件的传感器 - 与Client端逻辑保持一致
                    foreach (var subHardware in hardware.SubHardware)
                    {
                        subHardware.Update();
                        string subHardwareName = $"{hardwareName}_{subHardware.Name}";
                        ProcessSensors(subHardware.Sensors, subHardwareName);
                    }
                }

                _sensorsDiscovered = true;
                //Logger.LogInfo($"Sensor discovery completed. Found {_availableSensors.Count} sensors.");
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error discovering sensors");
            }
        }

        /// <summary>
        /// 处理传感器并生成键值 - 与Client端逻辑保持一致
        /// </summary>
        private void ProcessSensors(IEnumerable<ISensor> sensors, string hardwareName)
        {
            foreach (var sensor in sensors)
            {
                string sensorKey = $"{hardwareName}_{sensor.SensorType}_{sensor.Name}";
                if (!_availableSensors.ContainsKey(sensorKey))
                {
                    _availableSensors[sensorKey] = sensor;
                    //Logger.LogDebug($"Added sensor: {sensorKey}");
                }
            }
        }

        /// <summary>
        /// 重新加载配置文件
        /// </summary>
        public void ReloadConfiguration()
        {
            try
            {
                //Logger.LogInfo("Reloading configuration...");
                _appSettings = ServiceAppSettings.Load();

                // 确保传感器已发现
                if (!_sensorsDiscovered)
                {
                    DiscoverAllSensors();
                }

                // 验证配置中的传感器
                ValidateAndUpdateSensorConfiguration();

                //Logger.LogInfo("Configuration reloaded successfully");
            }
            catch (Exception ex)
            {
                //LoggerExtensions.LogExceptionWithCaller(ex, "Error reloading configuration");
            }
        }

        /// <summary>
        /// 验证并更新传感器配置
        /// </summary>
        private void ValidateAndUpdateSensorConfiguration()
        {
            try
            {
                //Logger.LogInfo("Validating sensor configuration...");
                var sensorSettings = _appSettings.GetSensorSettings();
                var validatedSettings = new Dictionary<string, string>();
                var fallbackSettings = new Dictionary<string, string>();

                foreach (var setting in sensorSettings)
                {
                    if (_availableSensors.ContainsKey(setting.Value))
                    {
                        validatedSettings[setting.Key] = setting.Value;
                        //Logger.LogInfo($"✅ Sensor found: {setting.Key} -> {setting.Value}");
                    }
                    else
                    {
                        // 查找回退传感器
                        var fallbackSensor = FindFallbackSensor(setting.Key);
                        if (fallbackSensor != null)
                        {
                            fallbackSettings[setting.Key] = fallbackSensor;
                            //Logger.LogInfo($"⚠️ Sensor not found, using fallback: {setting.Key} -> {fallbackSensor}");
                        }
                        else
                        {
                            //Logger.LogInfo($"❌ No sensor or fallback found for: {setting.Key}");
                        }
                    }
                }

                // 合并验证的设置和回退设置
                foreach (var fallback in fallbackSettings)
                {
                    validatedSettings[fallback.Key] = fallback.Value;
                }

                //Logger.LogInfo($"Sensor validation completed. {validatedSettings.Count} sensors configured.");
            }
            catch (Exception ex)
            {
                //LoggerExtensions.LogExceptionWithCaller(ex, "Error validating sensor configuration");
            }
        }

        /// <summary>
        /// 查找回退传感器（选择第一个可用的同类型传感器）
        /// </summary>
        private string FindFallbackSensor(string sensorType)
        {
            try
            {
                return sensorType switch
                {
                    "selectedNetworkInterface" => FindFirstSensorByType("Network"),
                    "selectedGpuSensor" => FindFirstSensorByHardwareType("Gpu"),
                    "selectedCpuTempSensor" => FindFirstSensorByTypeAndName("Temperature", "Package", "Core Max"),
                    "selectedCpuVoltageSensor" => FindFirstSensorByTypeAndName("Voltage", "CPU Core"),
                    "selectedCpuUsageSensor" => FindFirstSensorByTypeAndName("Load", "CPU Total", "Total"),
                    "selectedCpuFanSensor" => FindFirstSensorByType("Fan"),
                    "selectedPumpFanSensor" => FindFirstSensorByType("Fan"),
                    "selectedCaseFan1Sensor" => FindFirstSensorByType("Fan"),
                    "selectedCaseFan2Sensor" => FindFirstSensorByType("Fan"),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, $"Error finding fallback sensor for {sensorType}");
                return null;
            }
        }

        /// <summary>
        /// 查找第一个指定类型的传感器
        /// </summary>
        private string FindFirstSensorByType(string sensorType)
        {
            return _availableSensors.FirstOrDefault(kvp =>
                kvp.Key.Contains($"_{sensorType}_")).Key;
        }

        /// <summary>
        /// 查找第一个指定硬件类型的传感器
        /// </summary>
        private string FindFirstSensorByHardwareType(string hardwareType)
        {
            return _availableSensors.FirstOrDefault(kvp =>
                kvp.Key.StartsWith(hardwareType, StringComparison.OrdinalIgnoreCase)).Key;
        }

        /// <summary>
        /// 查找第一个指定类型和名称的传感器
        /// </summary>
        private string FindFirstSensorByTypeAndName(string sensorType, params string[] nameKeywords)
        {
            foreach (var keyword in nameKeywords)
            {
                var sensor = _availableSensors.FirstOrDefault(kvp =>
                    kvp.Key.Contains($"_{sensorType}_") &&
                    kvp.Key.Contains(keyword, StringComparison.OrdinalIgnoreCase)).Key;

                if (!string.IsNullOrEmpty(sensor))
                    return sensor;
            }

            // 如果没有找到包含关键词的，返回第一个该类型的传感器
            return FindFirstSensorByType(sensorType);
        }

        public void Close()
        {
            try
            {
                Logger.LogInfo("Closing hardware monitoring system");
                computer.Close();
                Logger.LogInfo("Hardware monitoring system closed successfully");
            }
            catch (Exception ex)
            {
                LoggerExtensions.LogExceptionWithCaller(ex, "Error closing hardware monitoring system");
            }
        }
    }
}
