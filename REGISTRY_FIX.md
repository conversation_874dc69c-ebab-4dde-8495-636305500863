# Windows服务注册表访问问题修复

## 问题分析

您的分析完全正确！问题的根源是：

**Windows服务无法访问用户注册表（HKEY_CURRENT_USER）**
- 服务以系统账户运行，无法访问用户的注册表配置单元
- 当服务自启动且无用户登录时，`Registry.CurrentUser` 指向系统账户的配置单元

## 解决方案

采用最简单的修复方案：**将注册表位置从 `HKEY_CURRENT_USER` 改为 `HKEY_LOCAL_MACHINE`**

### 修改内容

#### 1. 注册表路径变更
- **原路径**: `HKEY_CURRENT_USER\Software\N90\HardwareDisplay`
- **新路径**: `HKEY_LOCAL_MACHINE\SOFTWARE\N90\HardwareDisplay`

#### 2. 修改的文件
- `N90.Service\N90ServiceWorker.cs` - 服务端注册表访问
- `N90.Service\HardwareDisplayService.cs` - 显示服务注册表访问
- `N90.Client\MainWindow.xaml.cs` - 主窗口注册表访问
- `N90.Client\Views\Pages\N90Page.xaml.cs` - N90页面注册表访问
- `N90.Client\Services\HardwareDataManager.cs` - 数据管理器注册表访问

### 优势

✅ **最小变动**: 只改变注册表位置，保持所有现有逻辑不变
✅ **解决权限问题**: `HKEY_LOCAL_MACHINE` 服务和客户端都能访问
✅ **向后兼容**: 不破坏现有的代码结构
✅ **简单可靠**: 无需复杂的配置文件系统

### 注意事项

⚠️ **权限要求**: 写入 `HKEY_LOCAL_MACHINE` 需要管理员权限
- 服务安装时已经需要管理员权限，所以这不是新的限制
- 客户端首次设置偏好时可能需要以管理员身份运行

### 测试验证

1. 重新编译项目
2. 重新安装服务（以管理员身份）
3. 测试服务在无用户登录时是否能正常读取显示偏好
4. 测试客户端是否能正常保存和读取设置

这个解决方案彻底解决了Windows服务无法访问用户注册表的问题，同时保持了代码的简洁性。
