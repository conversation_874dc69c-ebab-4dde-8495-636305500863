<UserControl x:Class="N90.Client.Views.Components.NavigationButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <UserControl.Resources>
        <!-- 普通状态填充 -->
        <SolidColorBrush x:Key="NormalFillBrush" Color="#FF2A2A2A"/>
        <SolidColorBrush x:Key="NormalStrokeBrush" Color="#FF444444"/>

        <!-- 选中状态填充渐变：从 RGB(59,85,2) 到 RGB(0,47,51) -->
        <LinearGradientBrush x:Key="SelectedFillBrush" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#FF3B5502" Offset="0"/>
            <GradientStop Color="#FF002F33" Offset="1"/>
        </LinearGradientBrush>

        <!-- 选中状态边框渐变：从 RGB(151,215,0) 到 RGB(1,233,254) -->
        <LinearGradientBrush x:Key="SelectedStrokeBrush" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#FF97D700" Offset="0"/>
            <GradientStop Color="#FF01E9FE" Offset="1"/>
        </LinearGradientBrush>

        <!-- 普通状态样式 -->
        <Style x:Key="NormalButtonStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource NormalFillBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource NormalStrokeBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 选中状态样式 -->
        <Style x:Key="SelectedButtonStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SelectedFillBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource SelectedStrokeBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Height="50" Width="150" Cursor="Hand">
        <!-- 使用Path绘制特殊切角形状 -->
        <Path x:Name="ButtonPath"
              Stroke="{Binding BorderBrush, ElementName=ButtonBorder}"
              StrokeThickness="{Binding BorderThickness, ElementName=ButtonBorder}"
              Fill="{Binding Background, ElementName=ButtonBorder}">
            <Path.Data>
                <!-- 切角形状路径：左上切角，右上切角，右下切角 -->
                <PathGeometry>
                    <PathFigure StartPoint="20,0" IsClosed="True">
                        <LineSegment Point="170,0"/>
                        <LineSegment Point="150,0"/>
                        <LineSegment Point="150,30"/>
                        <LineSegment Point="130,50"/>
                        <LineSegment Point="0,50"/>
                        <LineSegment Point="0,20"/>
                    </PathFigure>
                </PathGeometry>
            </Path.Data>
        </Path>

        <!-- 隐藏的Border用于样式绑定 -->
        <Border x:Name="ButtonBorder"
                Opacity="0"
                Style="{StaticResource NormalButtonStyle}"/>

        <!-- 文字内容 -->
        <TextBlock x:Name="ButtonTextBlock"
                   Text="{Binding ButtonText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                   FontSize="15"
                   FontWeight="Medium"
                   Foreground="White"
                   FontFamily="Roboto"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   TextWrapping="Wrap"
                   TextAlignment="Center"
                   MaxWidth="130"
                   Margin="10,5"/>

        <!-- 阴影效果 -->
        <Grid.Effect>
            <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
        </Grid.Effect>

        <!-- 移除动画触发器，改用代码后台处理悬停效果 -->
    </Grid>
</UserControl>
