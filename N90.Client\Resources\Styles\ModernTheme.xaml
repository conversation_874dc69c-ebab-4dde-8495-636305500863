<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- Color Palette -->
    <SolidColorBrush x:Key="PrimaryBackgroundBrush" Color="#FF1A1A1A"/>
    <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="#FF2A2A2A"/>
    <SolidColorBrush x:Key="TertiaryBackgroundBrush" Color="#FF0F0F0F"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#FF00FF88"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="#FF00CC66"/>
    <SolidColorBrush x:Key="AccentPressedBrush" Color="#FF009944"/>
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="White"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#FFCCCCCC"/>
    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#FFAAAAAA"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#FF555555"/>
    <SolidColorBrush x:Key="HoverBrush" Color="#FF2A2A2A"/>
    
    <!-- Modern Button Style -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="8" 
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentHoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentPressedBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TertiaryTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8" 
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                            <Setter Property="Foreground" Value="Black"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource AccentHoverBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Modern ComboBox Style -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <ContentPresenter Grid.Column="0"
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"/>
                                
                                <Path Grid.Column="1" 
                                      Data="M 0 0 L 4 4 L 8 0 Z" 
                                      Fill="{StaticResource SecondaryTextBrush}"
                                      Margin="0,0,10,0"
                                      VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        
                        <Popup x:Name="PART_Popup" 
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               Placement="Bottom"
                               PlacementTarget="{Binding ElementName=PART_Popup}">
                            <Border Background="{StaticResource SecondaryBackgroundBrush}"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    BorderThickness="1"
                                    CornerRadius="6"
                                    MinWidth="{TemplateBinding ActualWidth}">
                                <ScrollViewer>
                                    <ItemsPresenter/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource AccentHoverBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Modern CheckBox Style -->
    <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="CheckBoxBorder"
                                Width="18" Height="18"
                                Background="{StaticResource SecondaryBackgroundBrush}"
                                BorderBrush="{StaticResource BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="3"
                                Margin="0,0,8,0">
                            <Path x:Name="CheckMark"
                                  Data="M 2 6 L 6 10 L 14 2"
                                  Stroke="{StaticResource AccentBrush}"
                                  StrokeThickness="2"
                                  Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="CheckBoxBorder" Property="Background" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Toggle Switch Style -->
    <Style x:Key="ToggleSwitchStyle" TargetType="CheckBox">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Border x:Name="SwitchBorder"
                            Width="50" Height="25"
                            CornerRadius="12.5"
                            Background="{StaticResource BorderBrush}"
                            Cursor="Hand">
                        <Ellipse x:Name="SwitchThumb"
                                 Width="19" Height="19"
                                 Fill="White"
                                 HorizontalAlignment="Left"
                                 Margin="3,0,0,0"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="SwitchBorder" Property="Background" Value="{StaticResource AccentBrush}"/>
                            <Setter TargetName="SwitchThumb" Property="HorizontalAlignment" Value="Right"/>
                            <Setter TargetName="SwitchThumb" Property="Margin" Value="0,0,3,0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Card Style -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="2" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Section Header Style -->
    <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Margin" Value="0,0,0,15"/>
    </Style>
    
    <!-- Page Title Style -->
    <Style x:Key="PageTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
    </Style>
    
    <!-- Page Subtitle Style -->
    <Style x:Key="PageSubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TertiaryTextBrush}"/>
        <Setter Property="Margin" Value="0,5,0,0"/>
    </Style>
    
</ResourceDictionary>
