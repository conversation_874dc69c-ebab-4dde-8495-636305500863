@echo off
echo Installing N90 Hardware Monitor Service...

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as administrator...
) else (
    echo This script must be run as administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Get the current directory
set "SERVICE_PATH=%~dp0N90.Service.exe"
set "SERVICE_NAME=N90HardwareMonitorService"
set "SERVICE_DISPLAY_NAME=N90 Hardware Monitor Service"

echo Service executable path: %SERVICE_PATH%

REM Check if service executable exists
if not exist "%SERVICE_PATH%" (
    echo Error: N90.Service.exe not found in current directory!
    echo Please make sure all files are in the same directory.
    pause
    exit /b 1
)

REM Stop service if it's running
echo Stopping service if running...
sc stop "%SERVICE_NAME%" >nul 2>&1

REM Delete existing service if it exists
echo Removing existing service if it exists...
sc delete "%SERVICE_NAME%" >nul 2>&1

REM Create the service
echo Creating Windows service...
sc create "%SERVICE_NAME%" binPath= "\"%SERVICE_PATH%\" --service" DisplayName= "%SERVICE_DISPLAY_NAME%" start= auto

if %errorLevel% == 0 (
    echo Service created successfully!
    
    REM Set service description
    sc description "%SERVICE_NAME%" "N90 Hardware Monitor Service - Monitors hardware data and sends to N90 device"
    
    REM Start the service
    echo Starting service...
    sc start "%SERVICE_NAME%"
    
    if %errorLevel% == 0 (
        echo Service started successfully!
        echo.
        echo N90 Hardware Monitor Service has been installed and started.
        echo The service will automatically start when Windows boots.
        echo.
        echo You can manage the service using:
        echo - Services.msc (Windows Services Manager)
        echo - sc start/stop/query N90HardwareMonitorService
        echo.
    ) else (
        echo Warning: Service created but failed to start.
        echo You can start it manually using: sc start "%SERVICE_NAME%"
    )
) else (
    echo Error: Failed to create service!
    echo Please check if you have administrator privileges.
)

echo.
pause
