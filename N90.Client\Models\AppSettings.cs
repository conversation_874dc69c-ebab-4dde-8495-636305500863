using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace N90.Client.Models
{
    /// <summary>
    /// 应用程序设置配置类 - 统一存储所有设置到JSON文件
    /// </summary>
    public class AppSettings
    {
        // 应用程序设置
        public bool AutoLaunch { get; set; } = true;
        public bool DisableUpdating { get; set; } = false;
        public bool ShowMessageBox { get; set; } = false;
        public bool Fahrenheit { get; set; } = false;
        public bool CompatibilityMode { get; set; } = false;
        public int DelayedStart { get; set; } = 12;

        // 硬件检测设置
        public bool AutoDetectCpu { get; set; } = true;
        public bool AutoDetectGpu { get; set; } = true;
        public bool AutoDetectMemory { get; set; } = true;

        // 传感器选择设置
        [JsonPropertyName("selectedNetworkInterface")]
        public string SelectedNetworkInterface { get; set; } = string.Empty;

        [JsonPropertyName("selectedGpuSensor")]
        public string SelectedGpuSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuTempSensor")]
        public string SelectedCpuTempSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuVoltageSensor")]
        public string SelectedCpuVoltageSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuUsageSensor")]
        public string SelectedCpuUsageSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCpuFanSensor")]
        public string SelectedCpuFanSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedPumpFanSensor")]
        public string SelectedPumpFanSensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCaseFan1Sensor")]
        public string SelectedCaseFan1Sensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedCaseFan2Sensor")]
        public string SelectedCaseFan2Sensor { get; set; } = string.Empty;

        [JsonPropertyName("selectedHddSensor")]
        public string SelectedHddSensor { get; set; } = string.Empty;

        // 时间戳
        public DateTime LastSaved { get; set; } = DateTime.Now;

        private static readonly string SettingsPath = GetSettingsFilePath();

        /// <summary>
        /// 根据运行环境确定设置文件路径
        /// Debug模式：使用绝对路径到项目根目录
        /// Release模式：使用相对路径（与可执行文件同目录）
        /// </summary>
        /// <returns>设置文件的完整路径</returns>
        private static string GetSettingsFilePath()
        {
            const string fileName = "setting.json";

#if DEBUG
            // Debug模式：保存到项目根目录
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：保存到可执行文件同目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        /// <summary>
        /// 获取设置文件路径（用于调试）
        /// </summary>
        /// <returns>设置文件路径</returns>
        public static string GetSettingsPath()
        {
            return SettingsPath;
        }

        /// <summary>
        /// 从JSON文件加载设置
        /// </summary>
        /// <returns>应用设置对象</returns>
        public static AppSettings Load()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Loading AppSettings from: {SettingsPath}");
                System.Diagnostics.Debug.WriteLine($"File exists: {File.Exists(SettingsPath)}");

                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    System.Diagnostics.Debug.WriteLine($"JSON content length: {json.Length}");
                    System.Diagnostics.Debug.WriteLine($"JSON content preview: {json.Substring(0, Math.Min(200, json.Length))}...");

                    // 使用与Save方法相同的JsonSerializerOptions
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var settings = JsonSerializer.Deserialize<AppSettings>(json, options);
                    if (settings != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Successfully loaded settings - GPU: '{settings.SelectedGpuSensor}', CPU Temp: '{settings.SelectedCpuTempSensor}'");
                        return settings;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Deserialized settings is null");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Settings file does not exist, returning default settings");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading app settings: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            System.Diagnostics.Debug.WriteLine("Returning new default AppSettings");
            return new AppSettings();
        }

        /// <summary>
        /// 保存设置到JSON文件
        /// </summary>
        public void Save()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                LastSaved = DateTime.Now;
                var options = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(SettingsPath, json);
                
                System.Diagnostics.Debug.WriteLine($"App settings saved to: {SettingsPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving app settings: {ex.Message}");
            }
        }



        /// <summary>
        /// 获取设置文件的目录路径（用于Service读取）
        /// </summary>
        /// <returns>设置文件所在目录的完整路径</returns>
        public static string GetSettingsDirectory()
        {
            return Path.GetDirectoryName(SettingsPath) ?? AppDomain.CurrentDomain.BaseDirectory;
        }


    }
}
