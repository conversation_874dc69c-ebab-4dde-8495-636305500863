using System;
using System.Collections.Generic;

namespace N90.Client.Models
{
    /// <summary>
    /// 进度条数据类型枚举
    /// </summary>
    public enum ProgressBarDataType
    {
        CPUTemperature,
        CPUClock,        // 假数据，用于演示
        CPUFan,          // 对应 CPUFanSpeed
        GPUTemperature,
        GPUClock,        // 假数据，用于演示
        GPUFan,          // 假数据，用于演示
        RAMUsage,
        CDriveUsage,     // 对应 HDDUsage
        DDriveUsage,     // 假数据，用于演示
        EDriveUsage      // 假数据，用于演示
    }

    /// <summary>
    /// 进度条配置信息
    /// </summary>
    public class ProgressBarConfig
    {
        /// <summary>
        /// 数据类型
        /// </summary>
        public ProgressBarDataType DataType { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public double MaxValue { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public double MinValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 是否为温度类型（需要支持摄氏度/华氏度转换）
        /// </summary>
        public bool IsTemperature { get; set; }

        /// <summary>
        /// 显示格式（小数位数）
        /// </summary>
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// 进度条颜色（可选，用于不同类型的视觉区分）
        /// </summary>
        public string ProgressColor { get; set; }

        public ProgressBarConfig()
        {
            MinValue = 0;
            DecimalPlaces = 0;
            Unit = "";
            ProgressColor = "#B8B8B8"; // 默认灰色
        }
    }

    /// <summary>
    /// 进度条配置管理器
    /// </summary>
    public static class ProgressBarConfigManager
    {
        private static readonly Dictionary<ProgressBarDataType, ProgressBarConfig> _configs;

        static ProgressBarConfigManager()
        {
            _configs = new Dictionary<ProgressBarDataType, ProgressBarConfig>
            {
                // CPU 温度：0-100°C
                [ProgressBarDataType.CPUTemperature] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.CPUTemperature,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "°",
                    IsTemperature = true,
                    DecimalPlaces = 0,
                    ProgressColor = "#FF6B6B" // 红色系，表示温度
                },

                // CPU 时钟：0-5000MHz
                [ProgressBarDataType.CPUClock] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.CPUClock,
                    MaxValue = 5000,
                    MinValue = 0,
                    Unit = "M",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#4ECDC4" // 青色系
                },

                // CPU 风扇：0-3000RPM
                [ProgressBarDataType.CPUFan] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.CPUFan,
                    MaxValue = 3000,
                    MinValue = 0,
                    Unit = "R",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#45B7D1" // 蓝色系
                },

                // GPU 温度：0-100°C
                [ProgressBarDataType.GPUTemperature] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.GPUTemperature,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "°",
                    IsTemperature = true,
                    DecimalPlaces = 0,
                    ProgressColor = "#FF6B6B" // 红色系，表示温度
                },

                // GPU 时钟：0-3000MHz
                [ProgressBarDataType.GPUClock] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.GPUClock,
                    MaxValue = 3000,
                    MinValue = 0,
                    Unit = "M",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#4ECDC4" // 青色系
                },

                // GPU 风扇：0-4000RPM
                [ProgressBarDataType.GPUFan] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.GPUFan,
                    MaxValue = 4000,
                    MinValue = 0,
                    Unit = "R",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#45B7D1" // 蓝色系
                },

                // RAM 使用率：0-100%
                [ProgressBarDataType.RAMUsage] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.RAMUsage,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "%",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#96CEB4" // 绿色系
                },

                // C盘使用率：0-100%
                [ProgressBarDataType.CDriveUsage] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.CDriveUsage,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "%",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#FECA57" // 黄色系
                },

                // D盘使用率：0-100%
                [ProgressBarDataType.DDriveUsage] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.DDriveUsage,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "%",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#FECA57" // 黄色系
                },

                // E盘使用率：0-100%
                [ProgressBarDataType.EDriveUsage] = new ProgressBarConfig
                {
                    DataType = ProgressBarDataType.EDriveUsage,
                    MaxValue = 100,
                    MinValue = 0,
                    Unit = "%",
                    IsTemperature = false,
                    DecimalPlaces = 0,
                    ProgressColor = "#FECA57" // 黄色系
                }
            };
        }

        /// <summary>
        /// 获取指定数据类型的配置
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>配置信息</returns>
        public static ProgressBarConfig GetConfig(ProgressBarDataType dataType)
        {
            return _configs.TryGetValue(dataType, out var config) ? config : new ProgressBarConfig();
        }

        /// <summary>
        /// 计算进度百分比
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="currentValue">当前值</param>
        /// <returns>进度百分比（0-100）</returns>
        public static double CalculateProgressPercentage(ProgressBarDataType dataType, double currentValue)
        {
            var config = GetConfig(dataType);
            var range = config.MaxValue - config.MinValue;
            if (range <= 0) return 0;

            var normalizedValue = Math.Max(config.MinValue, Math.Min(config.MaxValue, currentValue));
            return ((normalizedValue - config.MinValue) / range) * 100.0;
        }

        /// <summary>
        /// 格式化显示值
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="currentValue">当前值</param>
        /// <param name="useFahrenheit">是否使用华氏度（仅对温度有效）</param>
        /// <returns>格式化后的显示字符串</returns>
        public static string FormatDisplayValue(ProgressBarDataType dataType, double currentValue, bool useFahrenheit = false)
        {
            var config = GetConfig(dataType);
            
            // 处理温度转换
            if (config.IsTemperature && useFahrenheit)
            {
                currentValue = currentValue * 9.0 / 5.0 + 32.0;
            }

            // 格式化数值
            var formatString = config.DecimalPlaces > 0 ? $"F{config.DecimalPlaces}" : "F0";
            var formattedValue = currentValue.ToString(formatString);

            return $"{formattedValue}{config.Unit}";
        }

        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns>所有配置的字典</returns>
        public static Dictionary<ProgressBarDataType, ProgressBarConfig> GetAllConfigs()
        {
            return new Dictionary<ProgressBarDataType, ProgressBarConfig>(_configs);
        }
    }
}
