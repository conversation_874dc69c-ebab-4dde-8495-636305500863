using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace N90.Shared.Communication
{
    public class NamedPipeServer : IDisposable
    {
        private readonly string pipeName;
        private NamedPipeServerStream pipeServer;
        private CancellationTokenSource cancellationTokenSource;
        private bool disposed = false;

        public event Action<IpcMessage> MessageReceived;

        public NamedPipeServer(string pipeName)
        {
            this.pipeName = pipeName;
            cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task StartAsync()
        {
            await Task.Run(async () =>
            {
                while (!cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        pipeServer = new NamedPipeServerStream(pipeName, PipeDirection.InOut, 1, PipeTransmissionMode.Byte, PipeOptions.Asynchronous);
                        
                        Console.WriteLine($"Waiting for client connection on pipe: {pipeName}");
                        await pipeServer.WaitForConnectionAsync(cancellationTokenSource.Token);
                        
                        Console.WriteLine("Client connected to pipe");
                        
                        // Handle client communication
                        await HandleClientAsync(pipeServer, cancellationTokenSource.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Pipe server error: {ex.Message}");
                    }
                    finally
                    {
                        pipeServer?.Dispose();
                        pipeServer = null;
                    }
                }
            });
        }

        private async Task HandleClientAsync(NamedPipeServerStream pipe, CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];
            
            while (pipe.IsConnected && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    int bytesRead = await pipe.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        string messageJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        var message = JsonSerializer.Deserialize<IpcMessage>(messageJson);
                        MessageReceived?.Invoke(message);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error reading from pipe: {ex.Message}");
                    break;
                }
            }
        }

        public async Task SendMessageAsync(IpcMessage message)
        {
            if (pipeServer?.IsConnected == true)
            {
                try
                {
                    string messageJson = JsonSerializer.Serialize(message);
                    byte[] data = Encoding.UTF8.GetBytes(messageJson);
                    await pipeServer.WriteAsync(data, 0, data.Length);
                    await pipeServer.FlushAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error sending message: {ex.Message}");
                }
            }
        }

        public void Stop()
        {
            cancellationTokenSource?.Cancel();
        }

        public void Dispose()
        {
            if (!disposed)
            {
                Stop();
                pipeServer?.Dispose();
                cancellationTokenSource?.Dispose();
                disposed = true;
            }
        }
    }
}
