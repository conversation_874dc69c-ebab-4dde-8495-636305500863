<Window x:Class="N90.Client.Views.ModernMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:components="clr-namespace:N90.Client.Views.Components"
        Title="N90 System Monitor" 
        Height="720" Width="1280"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="Transparent"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Modern button style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF2A2A2A"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF00FF88"/>
                                <Setter Property="Foreground" Value="Black"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Window control button style -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="50"/> 
            <Setter Property="Height" Value="35"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <!-- 悬停时不改变背景色 -->
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Foreground" Value="#FFAAAAAA"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Navigation button style -->
        <Style x:Key="NavButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Height" Value="50"/>
        </Style>

        <!-- Active navigation button style -->
        <Style x:Key="ActiveNavButtonStyle" TargetType="Button" BasedOn="{StaticResource NavButtonStyle}">
            <Setter Property="Background" Value="#FF00FF88"/>
            <Setter Property="Foreground" Value="Black"/>
        </Style>

        <!-- Card style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF2A2A2A"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Height="720" VerticalAlignment="Top">
        <Border.Background>
            <ImageBrush ImageSource="/Resources/Images/background.png"
                        Stretch="Uniform"
                        AlignmentX="Center"
                        AlignmentY="Center"/>
        </Border.Background>

        <Grid Margin="10,12,-10,-12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 窗口控制按钮区域 -->
            <StackPanel Grid.Column="1"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Orientation="Horizontal"
                        Margin="0,-5,30,0">
                <Button Style="{StaticResource WindowControlButtonStyle}"
                        Click="MinimizeButton_Click"
                        ToolTip="最小化">
                    <Image Source="/Resources/Images/img15.png" Width="16" Height="16"/>
                </Button>
                <Button Style="{StaticResource WindowControlButtonStyle}"
                        Click="CloseButton_Click"
                        ToolTip="关闭">
                    <Image Source="/Resources/Images/img16.png" Width="16" Height="16"/>
                </Button>
            </StackPanel>

            <!-- 左侧导航按钮区域 -->
            <StackPanel Grid.Column="0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Margin="50,60,0,0">

                <components:NavigationButton x:Name="SystemInfoButton"
                                           ButtonText="System Information"
                                           IsSelected="True"
                                           Click="NavigateToSystemInfo_Click"
                                           Margin="0,20"/>

                <components:NavigationButton x:Name="N90Button"
                                           ButtonText="N90"
                                           IsSelected="False"
                                           Click="NavigateToN90_Click"
                                           Margin="0,20"/>

                <components:NavigationButton x:Name="SettingsButton"
                                           ButtonText="Settings"
                                           IsSelected="False"
                                           Click="NavigateToSettings_Click"
                                           Margin="0,20"/>

                <components:NavigationButton x:Name="AboutButton"
                                           ButtonText="About"
                                           IsSelected="False"
                                           Click="NavigateToAbout_Click"
                                           Margin="0,20"/>

            </StackPanel>

            <!-- 右侧内容区域 -->
            <Border Grid.Column="1"
                    Background="Transparent"
                    CornerRadius="12"
                    Margin="0,100,0,0"
                    Width="980"
                    Height="600"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="0">
                    <Frame x:Name="ContentFrame"
                           NavigationUIVisibility="Hidden"
                           Background="Transparent"
                           Width="auto"
                           Height="Auto"
                           HorizontalAlignment="Stretch"
                           VerticalAlignment="Top"/>
                </ScrollViewer>
            </Border>
            <TextBlock HorizontalAlignment="Left" Margin="60,650,0,0" TextWrapping="Wrap" Text="POWEREO BY GAMEMAX&#xD;&#xA;      FOUNDED IN 2013" VerticalAlignment="Top" Width="122" Foreground="White" FontSize="10" FontFamily="Roboto" Height="28"/>

        </Grid>
    </Border>
</Window>
