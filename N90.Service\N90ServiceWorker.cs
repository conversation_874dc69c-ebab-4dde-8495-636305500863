using Microsoft.Extensions.Hosting;
using N90.Shared;
using N90.Shared.Services;
using N90.Shared.Communication;
using System.Text.Json;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace N90.Service
{
    public class N90ServiceWorker : BackgroundService
    {
        private readonly HardwareReader hardwareReader;
        private readonly HidSender hidSender;
        private readonly HardwareDisplayService hardwareDisplayService;
        private readonly TemperatureUnitService temperatureUnitService;
        private readonly LanguageService languageService;
        private readonly HardwareData hardwareData;
        private readonly DisplayPreferencesService displayPreferencesService;
        private const int UPDATE_INTERVAL = 2000; // 2 seconds
        private NamedPipeServer _pipeServer;
        private Dictionary<string, string> _selectedSensors = new Dictionary<string, string>();

        // 缓存配置状态，避免重复应用
        private byte _currentTemperatureCode = 1; // 默认摄氏度
        private bool _configurationChanged = false;

        public N90ServiceWorker(
            HardwareReader hardwareReader,
            HidSender hidSender,
            HardwareDisplayService hardwareDisplayService,
            TemperatureUnitService temperatureUnitService,
            LanguageService languageService,
            HardwareData hardwareData,
            DisplayPreferencesService displayPreferencesService)
        {
            this.hardwareReader = hardwareReader;
            this.hidSender = hidSender;
            this.hardwareDisplayService = hardwareDisplayService;
            this.temperatureUnitService = temperatureUnitService;
            this.languageService = languageService;
            this.hardwareData = hardwareData;
            this.displayPreferencesService = displayPreferencesService;

            InitializeHardwareData();
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            Logger.LogInfo("N90 Service Worker starting...");

            // Initialize NamedPipe server for real-time communication
            _pipeServer = new NamedPipeServer("N90_Pipe");
            _pipeServer.MessageReceived += OnMessageReceived;
            _ = Task.Run(async () => await _pipeServer.StartAsync());
            Logger.LogInfo("NamedPipe server started");

            // Initialize sensor discovery and configuration
            Logger.LogInfo("Initializing sensor discovery...");
            hardwareReader.DiscoverAllSensors();
            hardwareReader.ReloadConfiguration();
            Logger.LogInfo("Sensor discovery and configuration initialized");

            // Load initial settings (只在启动时加载一次)
            hardwareDisplayService.LoadHardwareDisplayState(hardwareData);
            temperatureUnitService.ApplyTemperatureUnitSetting();
            _currentTemperatureCode = temperatureUnitService.TemperatureCelsiusCode;
            languageService.ApplyLanguageSetting();

            // Load display preferences from configuration file on startup
            Logger.LogInfo("Loading display preferences from configuration file...");
            LoadDisplayPreferencesFromConfig();

            Logger.LogInfo("N90 Service Worker started successfully.");

            await base.StartAsync(cancellationToken);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 不再轮询配置，使用缓存的配置状态
                    // 配置更改通过NamedPipe实时通知

                    // Read hardware data
                    hardwareReader.ReadHardwareData(hardwareData, _currentTemperatureCode == 1);

                    // Fill time data
                    var now = DateTime.Now;
                    hardwareData.Date.value = now.ToString("yyyy-MM-dd");
                    hardwareData.Time.value = now.ToString("HH:mm:ss");
                    hardwareData.Weekday.value = GetWeekdayString(now.DayOfWeek);

                    // Save json for debugging
                    string filePath = GetDataFilePath();
                    //Logger.LogInfo($"Attempting to write data to: {filePath}");
                    string json = JsonSerializer.Serialize(hardwareData, new JsonSerializerOptions { WriteIndented = true });
                    await File.WriteAllTextAsync(filePath, json, stoppingToken);
                    //Logger.LogInfo($"Successfully wrote {json.Length} characters to data file");

                    // Send data via HID (现在会根据show字段过滤数据)
                    hidSender.SendData(hardwareData, _currentTemperatureCode);

                    // 通知Client数据已更新
                    await NotifyClientDataUpdated(filePath);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] - Error: {ex.Message}";
                    Logger.LogInfo(errorMessage);
                    Logger.LogInfo($"Stack trace: {ex.StackTrace}");
                }

                await Task.Delay(UPDATE_INTERVAL, stoppingToken);
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            Logger.LogInfo("N90 Service Worker stopping...");

            try
            {
                // Dispose NamedPipe server
                _pipeServer?.Dispose();
                Logger.LogInfo("NamedPipe server stopped");

                // HardwareReader and HidSender don't implement IDisposable in current implementation
                // Just log the stop message
                Logger.LogInfo("N90 Service Worker stopped.");
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error during cleanup: {ex.Message}");
            }

            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// 处理来自Client的消息
        /// </summary>
        private void OnMessageReceived(IpcMessage message)
        {
            try
            {
                Logger.LogInfo($"Received message: {message.Type}");

                switch (message.Type)
                {
                    case MessageType.SelectedSensorData:
                        HandleSelectedSensorData(message.Data);
                        break;
                    case MessageType.TemperatureUnitChanged:
                        temperatureUnitService.ApplyTemperatureUnitSetting();
                        _currentTemperatureCode = temperatureUnitService.TemperatureCelsiusCode;
                        // 修正日志显示逻辑：0=摄氏度，1=华氏度
                        Logger.LogInfo($"Temperature unit setting updated via pipe: {(_currentTemperatureCode == 0 ? "Celsius" : "Fahrenheit")}");
                        Logger.LogInfo($"Temperature code: {_currentTemperatureCode}, UseFahrenheit: {temperatureUnitService.UseFahrenheit}");
                        break;
                    case MessageType.LanguageChanged:
                        languageService.ApplyLanguageSetting();
                        Logger.LogInfo("Language setting updated via pipe");
                        break;
                    case MessageType.CustomStringChanged:
                        // Handle custom string change
                        Logger.LogInfo($"Custom string updated: {message.Data}");
                        // 立即发送自定义字符串到HID设备
                        hidSender.SendCustomStr(message.Data);
                        break;
                    case MessageType.BrightnessChanged:
                        // Handle brightness change
                        Logger.LogInfo($"Brightness updated: {message.Data}");
                        break;
                    case MessageType.ConfigurationChanged:
                        HandleConfigurationChanged(message.Data);
                        break;
                    case MessageType.DisplayPreferencesChanged:
                        HandleDisplayPreferencesChanged(message.Data);
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error handling message: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理传感器选择数据
        /// </summary>
        private void HandleSelectedSensorData(string data)
        {
            try
            {
                var selectedSensors = JsonSerializer.Deserialize<Dictionary<string, string>>(data);
                if (selectedSensors != null)
                {
                    _selectedSensors = selectedSensors;
                    Logger.LogInfo($"Updated selected sensors: {selectedSensors.Count} sensors");

                    // 可以在这里通知HardwareReader只读取选中的传感器
                    // 这需要修改HardwareReader来支持传感器过滤
                }
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error parsing selected sensor data: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理显示偏好变化 - 重新从配置文件加载
        /// </summary>
        private void HandleDisplayPreferencesChanged(string data)
        {
            try
            {
                Logger.LogInfo("Received display preferences change notification, reloading from configuration file");

                // 重新从配置文件加载显示偏好
                LoadDisplayPreferencesFromConfig();
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error reloading display preferences from configuration file: {ex.Message}");
            }
        }

        /// <summary>
        /// 从配置文件加载显示偏好并应用到hardwareData
        /// </summary>
        private void LoadDisplayPreferencesFromConfig()
        {
            try
            {
                // 重新加载配置文件
                displayPreferencesService.ReloadPreferences();

                // 定义所有数据类型
                var dataTypes = new[]
                {
                    "Date", "Time", "Weekday", "CPUModel", "GPUModel", "CustomString",
                    "CPUTemperature", "CPUUsage", "CPUPower", "CPUFanSpeed",
                    "GPUTemperature", "GPUMemoryUsage", "GPUPower",
                    "RAMUsage", "AvailableRAM",
                    "HDDTemperature", "HDDUsage",
                    "CaseFan1Speed", "CaseFan2Speed",
                    "UploadSpeed", "DownloadSpeed"
                };

                int loadedCount = 0;
                foreach (var dataType in dataTypes)
                {
                    bool show = displayPreferencesService.GetShowPreference(dataType);

                    // 应用到hardwareData
                    switch (dataType)
                    {
                        case "Date":
                            if (hardwareData.Date != null) hardwareData.Date.show = show;
                            break;
                        case "Time":
                            if (hardwareData.Time != null) hardwareData.Time.show = show;
                            break;
                        case "Weekday":
                            if (hardwareData.Weekday != null) hardwareData.Weekday.show = show;
                            break;
                        case "CPUTemperature":
                            if (hardwareData.CPUTemperature != null) hardwareData.CPUTemperature.show = show;
                            break;
                        case "CPUUsage":
                            if (hardwareData.CPUUsage != null) hardwareData.CPUUsage.show = show;
                            break;
                        case "CPUPower":
                            if (hardwareData.CPUPower != null) hardwareData.CPUPower.show = show;
                            break;
                        case "CPUFanSpeed":
                            if (hardwareData.CPUFanSpeed != null) hardwareData.CPUFanSpeed.show = show;
                            break;
                        case "CPUModel":
                            if (hardwareData.CPUModel != null) hardwareData.CPUModel.show = show;
                            break;
                        case "GPUTemperature":
                            if (hardwareData.GPUTemperature != null) hardwareData.GPUTemperature.show = show;
                            break;
                        case "GPUMemoryUsage":
                            if (hardwareData.GPUMemoryUsage != null) hardwareData.GPUMemoryUsage.show = show;
                            break;
                        case "GPUPower":
                            if (hardwareData.GPUPower != null) hardwareData.GPUPower.show = show;
                            break;
                        case "GPUModel":
                            if (hardwareData.GPUModel != null) hardwareData.GPUModel.show = show;
                            break;
                        case "RAMUsage":
                            if (hardwareData.RAMUsage != null) hardwareData.RAMUsage.show = show;
                            break;
                        case "AvailableRAM":
                            if (hardwareData.AvailableRAM != null) hardwareData.AvailableRAM.show = show;
                            break;
                        case "CaseFan1Speed":
                            if (hardwareData.CaseFan1Speed != null) hardwareData.CaseFan1Speed.show = show;
                            break;
                        case "CaseFan2Speed":
                            if (hardwareData.CaseFan2Speed != null) hardwareData.CaseFan2Speed.show = show;
                            break;
                        case "HDDTemperature":
                            if (hardwareData.HDDTemperature != null) hardwareData.HDDTemperature.show = show;
                            break;
                        case "HDDUsage":
                            if (hardwareData.HDDUsage != null) hardwareData.HDDUsage.show = show;
                            break;
                        case "UploadSpeed":
                            if (hardwareData.UploadSpeed != null) hardwareData.UploadSpeed.show = show;
                            break;
                        case "DownloadSpeed":
                            if (hardwareData.DownloadSpeed != null) hardwareData.DownloadSpeed.show = show;
                            break;
                        case "CustomString":
                            if (hardwareData.CustomString != null) hardwareData.CustomString.show = show;
                            break;
                    }

                    loadedCount++;
                }

                Logger.LogInfo($"Loaded {loadedCount} display preferences from configuration file");
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error loading display preferences from configuration file: {ex.Message}");
                // 如果加载失败，使用默认值（全部显示）
                SetDefaultDisplayPreferences();
            }
        }

        /// <summary>
        /// 设置默认显示偏好（全部显示）
        /// </summary>
        private void SetDefaultDisplayPreferences()
        {
            if (hardwareData != null)
            {
                if (hardwareData.Date != null) hardwareData.Date.show = true;
                if (hardwareData.Time != null) hardwareData.Time.show = true;
                if (hardwareData.Weekday != null) hardwareData.Weekday.show = true;
                if (hardwareData.CPUTemperature != null) hardwareData.CPUTemperature.show = true;
                if (hardwareData.CPUUsage != null) hardwareData.CPUUsage.show = true;
                if (hardwareData.CPUPower != null) hardwareData.CPUPower.show = true;
                if (hardwareData.CPUFanSpeed != null) hardwareData.CPUFanSpeed.show = true;
                if (hardwareData.CPUModel != null) hardwareData.CPUModel.show = true;
                if (hardwareData.GPUTemperature != null) hardwareData.GPUTemperature.show = true;
                if (hardwareData.GPUMemoryUsage != null) hardwareData.GPUMemoryUsage.show = true;
                if (hardwareData.GPUPower != null) hardwareData.GPUPower.show = true;
                if (hardwareData.GPUModel != null) hardwareData.GPUModel.show = true;
                if (hardwareData.RAMUsage != null) hardwareData.RAMUsage.show = true;
                if (hardwareData.AvailableRAM != null) hardwareData.AvailableRAM.show = true;
                if (hardwareData.CaseFan1Speed != null) hardwareData.CaseFan1Speed.show = true;
                if (hardwareData.CaseFan2Speed != null) hardwareData.CaseFan2Speed.show = true;
                if (hardwareData.HDDTemperature != null) hardwareData.HDDTemperature.show = true;
                if (hardwareData.HDDUsage != null) hardwareData.HDDUsage.show = true;
                if (hardwareData.UploadSpeed != null) hardwareData.UploadSpeed.show = true;
                if (hardwareData.DownloadSpeed != null) hardwareData.DownloadSpeed.show = true;
                if (hardwareData.CustomString != null) hardwareData.CustomString.show = true;

                Logger.LogInfo("Set default display preferences (all visible)");
            }
        }



        /// <summary>
        /// 通知Client数据已更新
        /// </summary>
        private async Task NotifyClientDataUpdated(string dataFilePath)
        {
            try
            {
                if (_pipeServer != null)
                {
                    var notificationData = new
                    {
                        DataFilePath = dataFilePath,
                        Action = "DataUpdated",
                        Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    };

                    var message = new IpcMessage
                    {
                        Type = MessageType.DataUpdated,
                        Data = JsonSerializer.Serialize(notificationData)
                    };

                    await _pipeServer.SendMessageAsync(message);
                    //Logger.LogInfo($"Data update notification sent to client at {DateTime.Now:HH:mm:ss.fff}");
                }
            }
            catch (Exception ex)
            {
                //Logger.LogInfo($"Error sending data update notification: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理配置更改通知
        /// </summary>
        private void HandleConfigurationChanged(string data)
        {
            try
            {
                Logger.LogInfo("Configuration changed notification received");

                // 通知HardwareReader重新加载配置
                hardwareReader.ReloadConfiguration();
                Logger.LogInfo("HardwareReader configuration reloaded");

                // 使用Service自己的路径逻辑来读取配置文件
                var configPath = GetConfigFilePath();
                Logger.LogInfo($"Reading configuration from: {configPath}");

                // 读取配置文件
                if (File.Exists(configPath))
                {
                    var configJson = File.ReadAllText(configPath);
                    var appSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(configJson);

                    if (appSettings != null)
                    {
                        Logger.LogInfo($"Successfully loaded configuration from: {configPath}");

                        // 根据配置更新相应的服务设置
                        ApplyConfigurationSettings(appSettings);
                    }
                    else
                    {
                        Logger.LogInfo("Configuration file is empty or invalid");
                    }
                }
                else
                {
                    Logger.LogInfo($"Configuration file not found: {configPath}");
                }
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error handling configuration change: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用配置设置
        /// </summary>
        private void ApplyConfigurationSettings(Dictionary<string, object> settings)
        {
            try
            {
                // 应用温度单位设置
                if (settings.ContainsKey("fahrenheit"))
                {
                    var useFahrenheit = Convert.ToBoolean(settings["fahrenheit"]);
                    temperatureUnitService.UseFahrenheit = useFahrenheit;
                    temperatureUnitService.SaveTemperatureUnitSetting();
                    _currentTemperatureCode = temperatureUnitService.TemperatureCelsiusCode;
                    Logger.LogInfo($"Applied temperature unit from config: {(useFahrenheit ? "Fahrenheit" : "Celsius")}");
                }

                // 应用传感器选择设置
                var sensorSettings = new Dictionary<string, string>();
                var sensorKeys = new[] { "selectedNetworkInterface", "selectedGpuSensor", "selectedCpuTempSensor",
                                       "selectedCpuVoltageSensor", "selectedCpuUsageSensor", "selectedCpuFanSensor",
                                       "selectedPumpFanSensor", "selectedCaseFan1Sensor", "selectedCaseFan2Sensor",
                                       "selectedHddSensor" };

                foreach (var key in sensorKeys)
                {
                    if (settings.ContainsKey(key) && settings[key] != null)
                    {
                        var value = settings[key].ToString();
                        if (!string.IsNullOrEmpty(value))
                        {
                            sensorSettings[key] = value;
                        }
                    }
                }

                if (sensorSettings.Count > 0)
                {
                    _selectedSensors = sensorSettings;
                    Logger.LogInfo($"Applied sensor settings from config: {sensorSettings.Count} sensors");
                }

                Logger.LogInfo("Configuration settings applied successfully");
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error applying configuration settings: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取配置文件路径（根据运行环境）
        /// </summary>
        /// <returns>配置文件的完整路径</returns>
        private string GetConfigFilePath()
        {
            const string fileName = "setting.json";

#if DEBUG
            // Debug模式：从项目根目录读取
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, fileName);
#else
            // Release模式：从可执行文件同目录读取
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileName);
#endif
        }

        /// <summary>
        /// 获取数据文件路径（与Client端保持一致）
        /// </summary>
        /// <returns>数据文件的完整路径</returns>
        private string GetDataFilePath()
        {
#if DEBUG
            // Debug模式：使用项目根目录（与Client端一致）
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, "data.json");
#else
            // Release模式：使用可执行文件同目录（与Client端一致）
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data.json");
#endif
        }

        private void InitializeHardwareData()
        {
            hardwareData.Date = new Date();
            hardwareData.Time = new Time();
            hardwareData.Weekday = new Weekday();

            hardwareData.CPUTemperature = new CPUTemperature();
            hardwareData.CPUUsage = new CPUUsage();
            hardwareData.CPUPower = new CPUPower();
            hardwareData.CPUFanSpeed = new CPUFanSpeed();
            hardwareData.CPUModel = new CPUModel();

            hardwareData.GPUTemperature = new GPUTemperature();
            hardwareData.GPUMemoryUsage = new GPUMemoryUsage();
            hardwareData.GPUPower = new GPUPower();
            hardwareData.GPUModel = new GPUModel();

            hardwareData.RAMUsage = new RAMUsage();
            hardwareData.AvailableRAM = new AvailableRAM();

            hardwareData.CaseFan1Speed = new CaseFan1Speed();
            hardwareData.CaseFan2Speed = new CaseFan2Speed();

            hardwareData.HDDTemperature = new HDDTemperature();
            hardwareData.HDDUsage = new HDDUsage();

            hardwareData.UploadSpeed = new UploadSpeed();
            hardwareData.DownloadSpeed = new DownloadSpeed();

            hardwareData.CustomString = new CustomString();
        }

        private string GetWeekdayString(DayOfWeek dayOfWeek)
        {
            // Always reload and apply language setting to ensure thread culture is set
            languageService.ApplyLanguageSetting();
            var currentLanguage = languageService.LoadLanguageSetting();

            Logger.LogInfo($"[DEBUG] Current language setting: {currentLanguage}");
            Logger.LogInfo($"[DEBUG] Current thread culture: {System.Threading.Thread.CurrentThread.CurrentUICulture.Name}");

            if (currentLanguage == "en-US")
            {
                return dayOfWeek switch
                {
                    DayOfWeek.Monday => "Monday",
                    DayOfWeek.Tuesday => "Tuesday",
                    DayOfWeek.Wednesday => "Wednesday",
                    DayOfWeek.Thursday => "Thursday",
                    DayOfWeek.Friday => "Friday",
                    DayOfWeek.Saturday => "Saturday",
                    DayOfWeek.Sunday => "Sunday",
                    _ => "Monday"
                };
            }
            else // Default to Chinese
            {
                return dayOfWeek switch
                {
                    DayOfWeek.Monday => "星期一",
                    DayOfWeek.Tuesday => "星期二",
                    DayOfWeek.Wednesday => "星期三",
                    DayOfWeek.Thursday => "星期四",
                    DayOfWeek.Friday => "星期五",
                    DayOfWeek.Saturday => "星期六",
                    DayOfWeek.Sunday => "星期日",
                    _ => "星期一"
                };
            }
        }
    }
}
