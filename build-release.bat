@echo off
echo Building N90 Hardware Monitor Release Package...

REM Set variables
set "BUILD_CONFIG=Release"
set "OUTPUT_DIR=Release"
set "PACKAGE_DIR=N90-HardwareMonitor"

REM Clean previous builds
echo Cleaning previous builds...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"
dotnet clean --configuration %BUILD_CONFIG%

REM Build all projects
echo Building projects...
dotnet build --configuration %BUILD_CONFIG% --no-restore

if %errorLevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM Publish projects
echo Publishing Service...
dotnet publish N90.Service -c %BUILD_CONFIG% -o "%OUTPUT_DIR%\%PACKAGE_DIR%" --self-contained false --no-build

echo Publishing N90 (Main Application)...
dotnet publish N90.Client -c %BUILD_CONFIG% -o "%OUTPUT_DIR%\%PACKAGE_DIR%" --self-contained false --no-build

REM Copy additional files
echo Copying additional files...
copy "install-service.bat" "%OUTPUT_DIR%\%PACKAGE_DIR%\"
copy "uninstall-service.bat" "%OUTPUT_DIR%\%PACKAGE_DIR%\"

REM Copy icon
copy "favicon.ico" "%OUTPUT_DIR%\%PACKAGE_DIR%\"

REM Create README
echo Creating README...
echo N90 Hardware Monitor > "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo Usage: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 1. Double-click N90.Client.exe to start the application >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 2. Right-click system tray icon for options >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 3. For auto-startup, enable "Start with Windows" in the tray menu >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo Files: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - N90.Client.exe: Main application >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - N90.Service.exe: Hardware monitoring service >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - install-service.bat: Install Windows service (Run as Administrator) >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - uninstall-service.bat: Uninstall Windows service (Run as Administrator) >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo New Features: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - Simplified startup: Only need to run one program >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - Smart permission handling: Elevates privileges only when needed >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - Shared settings: Service and Client share configuration >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - Language settings take effect immediately >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"

REM Create ZIP package
echo Creating ZIP package...
if exist "%OUTPUT_DIR%\N90-HardwareMonitor.zip" del "%OUTPUT_DIR%\N90-HardwareMonitor.zip"
powershell -command "Compress-Archive -Path '%OUTPUT_DIR%\%PACKAGE_DIR%\*' -DestinationPath '%OUTPUT_DIR%\N90-HardwareMonitor.zip'"

echo.
echo Build completed successfully!
echo Package location: %OUTPUT_DIR%\N90-HardwareMonitor.zip
echo Folder location: %OUTPUT_DIR%\%PACKAGE_DIR%\
echo.
pause
