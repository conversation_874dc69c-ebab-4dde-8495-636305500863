using System;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace N90.Shared.Communication
{
    public class NamedPipeClient : IDisposable
    {
        private readonly string pipeName;
        private NamedPipeClientStream pipeClient;
        private CancellationTokenSource cancellationTokenSource;
        private bool disposed = false;

        public event Action<IpcMessage> MessageReceived;
        public bool IsConnected => pipeClient?.IsConnected == true;

        public NamedPipeClient(string pipeName)
        {
            this.pipeName = pipeName;
            cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task<bool> ConnectAsync(int timeoutMs = 5000)
        {
            try
            {
                pipeClient = new NamedPipeClientStream(".", pipeName, PipeDirection.InOut, PipeOptions.Asynchronous);
                await pipeClient.ConnectAsync(timeoutMs);
                
                Console.WriteLine("Connected to pipe server");
                
                // Start listening for messages
                _ = Task.Run(async () => await ListenForMessagesAsync(cancellationTokenSource.Token));
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to pipe: {ex.Message}");
                return false;
            }
        }

        private async Task ListenForMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];
            
            while (pipeClient?.IsConnected == true && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    int bytesRead = await pipeClient.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        string messageJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        var message = JsonSerializer.Deserialize<IpcMessage>(messageJson);
                        MessageReceived?.Invoke(message);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error reading from pipe: {ex.Message}");
                    break;
                }
            }
        }

        public async Task SendMessageAsync(IpcMessage message)
        {
            if (pipeClient?.IsConnected == true)
            {
                try
                {
                    string messageJson = JsonSerializer.Serialize(message);
                    byte[] data = Encoding.UTF8.GetBytes(messageJson);
                    await pipeClient.WriteAsync(data, 0, data.Length);
                    await pipeClient.FlushAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error sending message: {ex.Message}");
                }
            }
        }

        public void Disconnect()
        {
            cancellationTokenSource?.Cancel();
            pipeClient?.Close();
        }

        public void Dispose()
        {
            if (!disposed)
            {
                Disconnect();
                pipeClient?.Dispose();
                cancellationTokenSource?.Dispose();
                disposed = true;
            }
        }
    }
}
