using System;
using System.Collections.Generic;
using Microsoft.Win32;

namespace N90.Shared.Services
{
    /// <summary>
    /// 注册表迁移服务 - 将现有的注册表设置迁移到新的配置文件系统
    /// </summary>
    public static class RegistryMigrationService
    {
        /// <summary>
        /// 将注册表中的显示偏好迁移到配置文件
        /// </summary>
        /// <param name="displayPreferencesService">显示偏好服务实例</param>
        /// <returns>是否成功迁移</returns>
        public static bool MigrateDisplayPreferences(DisplayPreferencesService displayPreferencesService)
        {
            try
            {
                Logger.LogInfo("Starting migration of display preferences from registry to configuration file...");

                using var registryKey = Registry.CurrentUser.OpenSubKey("Software\\N90\\HardwareDisplay", false);
                
                if (registryKey == null)
                {
                    Logger.LogInfo("No existing registry settings found, using defaults");
                    return true; // 没有现有设置，不需要迁移
                }

                var dataTypes = new[]
                {
                    "Date", "Time", "Weekday", "CPUModel", "GPUModel", "CustomString",
                    "CPUTemperature", "CPUUsage", "CPUPower", "CPUFanSpeed",
                    "GPUTemperature", "GPUMemoryUsage", "GPUPower",
                    "RAMUsage", "AvailableRAM",
                    "HDDTemperature", "HDDUsage",
                    "CaseFan1Speed", "CaseFan2Speed",
                    "UploadSpeed", "DownloadSpeed"
                };

                var migratedPreferences = new Dictionary<string, bool>();
                int migratedCount = 0;

                foreach (var dataType in dataTypes)
                {
                    try
                    {
                        var value = registryKey.GetValue(dataType);
                        if (value != null)
                        {
                            bool show = Convert.ToBoolean(value);
                            migratedPreferences[dataType] = show;
                            migratedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogInfo($"Error migrating {dataType}: {ex.Message}");
                    }
                }

                if (migratedCount > 0)
                {
                    // 批量设置迁移的偏好
                    displayPreferencesService.SetShowPreferences(migratedPreferences);
                    Logger.LogInfo($"Successfully migrated {migratedCount} display preferences from registry to configuration file");
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error during display preferences migration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否需要进行迁移
        /// </summary>
        /// <returns>如果存在注册表设置但配置文件不存在，则返回true</returns>
        public static bool ShouldMigrate()
        {
            try
            {
                // 检查是否存在注册表设置
                using var registryKey = Registry.CurrentUser.OpenSubKey("Software\\N90\\HardwareDisplay", false);
                if (registryKey == null)
                {
                    return false; // 没有注册表设置
                }

                // 检查是否有任何值
                var valueNames = registryKey.GetValueNames();
                return valueNames.Length > 0;
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error checking migration requirement: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清理注册表中的旧设置（可选，在确认迁移成功后调用）
        /// </summary>
        /// <returns>是否成功清理</returns>
        public static bool CleanupRegistrySettings()
        {
            try
            {
                Logger.LogInfo("Cleaning up old registry settings...");

                using var parentKey = Registry.CurrentUser.OpenSubKey("Software\\N90", true);
                if (parentKey != null)
                {
                    parentKey.DeleteSubKeyTree("HardwareDisplay", false);
                    Logger.LogInfo("Successfully cleaned up old registry settings");
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogInfo($"Error cleaning up registry settings: {ex.Message}");
                return false;
            }
        }
    }
}
