using System;

namespace N90.Shared.Communication
{
    public enum MessageType
    {
        HardwareData,
        TemperatureUnitChanged,
        LanguageChanged,
        CustomStringChanged,
        BrightnessChanged,
        ServiceStatus,
        SelectedSensorData,
        ConfigurationChanged,
        DataUpdated,
        DisplayPreferencesChanged
    }

    public class IpcMessage
    {
        public MessageType Type { get; set; }
        public string Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public class ServiceStatusMessage
    {
        public bool IsRunning { get; set; }
        public bool HidConnected { get; set; }
        public string LastError { get; set; }
    }
}
