using System;

namespace N90.Client.Models
{
    /// <summary>
    /// System Information 页面专用的数据模型
    /// </summary>
    public class SystemInfoData
    {
        // CPU 相关数据
        public double CPUTemperature { get; set; } = 0.0;
        public double CPUClock { get; set; } = 0.0;        // MHz
        public double CPUFanSpeed { get; set; } = 0.0;     // RPM
        public double CPUUsage { get; set; } = 0.0;        // %

        // GPU 相关数据
        public double GPUTemperature { get; set; } = 0.0;
        public double GPUClock { get; set; } = 0.0;        // MHz
        public double GPUFanSpeed { get; set; } = 0.0;     // RPM
        public double GPUUsage { get; set; } = 0.0;        // %

        // RAM 相关数据
        public double RAMUsage { get; set; } = 0.0;        // %
        public double RAMUsed { get; set; } = 0.0;         // GB
        public double RAMAvailable { get; set; } = 0.0;    // GB
        public double RAMTotal { get; set; } = 0.0;        // GB

        // 磁盘使用率数据
        public double CDriveUsage { get; set; } = 0.0;     // %
        public double DDriveUsage { get; set; } = 0.0;     // %
        public double EDriveUsage { get; set; } = 0.0;     // %

        // 网络数据
        public double UploadSpeed { get; set; } = 0.0;     // KB/s
        public double DownloadSpeed { get; set; } = 0.0;   // KB/s

        // 电源数据
        public double CPUPower { get; set; } = 0.0;        // W
        public double CPUVoltage { get; set; } = 0.0;      // V
        public double GPUPower { get; set; } = 0.0;        // W
        public double GPUVoltage { get; set; } = 0.0;      // V

        // 时间戳
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建默认最低值数据（当无法获取真实数据时使用）
        /// </summary>
        /// <returns>包含最低值的 SystemInfoData 实例</returns>
        public static SystemInfoData CreateDefaultData()
        {
            return new SystemInfoData
            {
                // CPU 数据 - 设置为最低值
                CPUTemperature = 0,
                CPUClock = 0,
                CPUFanSpeed = 0,
                CPUUsage = 0,

                // GPU 数据 - 设置为最低值
                GPUTemperature = 0,
                GPUClock = 0,
                GPUFanSpeed = 0,
                GPUUsage = 0,

                // RAM 数据 - 设置为最低值
                RAMUsage = 0,
                RAMUsed = 0,
                RAMAvailable = 0,
                RAMTotal = 0,

                // 磁盘使用率 - 设置为最低值
                CDriveUsage = 0,
                DDriveUsage = 0,
                EDriveUsage = 0,

                // 网络数据 - 设置为最低值
                UploadSpeed = 0,
                DownloadSpeed = 0,

                // 电源数据 - 设置为最低值
                CPUPower = 0,
                CPUVoltage = 0,
                GPUPower = 0,
                GPUVoltage = 0,

                LastUpdated = DateTime.Now
            };
        }



        /// <summary>
        /// 复制当前实例
        /// </summary>
        /// <returns>当前实例的副本</returns>
        public SystemInfoData Clone()
        {
            return new SystemInfoData
            {
                CPUTemperature = this.CPUTemperature,
                CPUClock = this.CPUClock,
                CPUFanSpeed = this.CPUFanSpeed,
                CPUUsage = this.CPUUsage,
                GPUTemperature = this.GPUTemperature,
                GPUClock = this.GPUClock,
                GPUFanSpeed = this.GPUFanSpeed,
                GPUUsage = this.GPUUsage,
                RAMUsage = this.RAMUsage,
                RAMUsed = this.RAMUsed,
                RAMAvailable = this.RAMAvailable,
                RAMTotal = this.RAMTotal,
                CDriveUsage = this.CDriveUsage,
                DDriveUsage = this.DDriveUsage,
                EDriveUsage = this.EDriveUsage,
                UploadSpeed = this.UploadSpeed,
                DownloadSpeed = this.DownloadSpeed,
                CPUPower = this.CPUPower,
                CPUVoltage = this.CPUVoltage,
                GPUPower = this.GPUPower,
                GPUVoltage = this.GPUVoltage,
                LastUpdated = this.LastUpdated
            };
        }
    }
}
