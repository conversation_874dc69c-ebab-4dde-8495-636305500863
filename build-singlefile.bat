@echo off
echo Building N90 Hardware Monitor Single File Package...

REM Set variables
set "BUILD_CONFIG=Release"
set "OUTPUT_DIR=Release-SingleFile"
set "PACKAGE_DIR=N90-HardwareMonitor-SingleFile"
set "RUNTIME=win-x64"

REM Clean previous builds
echo Cleaning previous builds...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"
dotnet clean --configuration %BUILD_CONFIG%

REM Restore packages
echo Restoring packages...
dotnet restore

REM Publish projects as single files
echo Publishing Service (Single File)...
dotnet publish N90.Service -c %BUILD_CONFIG% -r %RUNTIME% -o "%OUTPUT_DIR%\%PACKAGE_DIR%" --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=false -p:IncludeNativeLibrariesForSelfExtract=true

echo Publishing Client (Single File)...
dotnet publish N90.Client -c %BUILD_CONFIG% -r %RUNTIME% -o "%OUTPUT_DIR%\%PACKAGE_DIR%" --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=false -p:IncludeNativeLibrariesForSelfExtract=true

REM Copy additional files
echo Copying additional files...
copy "install-service.bat" "%OUTPUT_DIR%\%PACKAGE_DIR%\"
copy "uninstall-service.bat" "%OUTPUT_DIR%\%PACKAGE_DIR%\"

REM Copy icon
copy "favicon.ico" "%OUTPUT_DIR%\%PACKAGE_DIR%\"

REM Create README
echo Creating README...
echo N90 Hardware Monitor - Single File Version > "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo This is a single-file version with minimal dependencies. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo Files: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - N90.Service.exe: Hardware monitoring service (single file) >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - N90.Client.exe: User interface (single file) >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo Usage: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 1. Double-click N90.Client.exe to start the application >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 2. Right-click system tray icon for options >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo 3. For auto-startup, run install-service.bat as administrator >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo. >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo System Requirements: >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - Windows 10/11 x64 >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"
echo - No additional runtime required >> "%OUTPUT_DIR%\%PACKAGE_DIR%\README.txt"

REM Create ZIP package
echo Creating ZIP package...
if exist "%OUTPUT_DIR%\N90-HardwareMonitor-SingleFile.zip" del "%OUTPUT_DIR%\N90-HardwareMonitor-SingleFile.zip"
powershell -command "Compress-Archive -Path '%OUTPUT_DIR%\%PACKAGE_DIR%\*' -DestinationPath '%OUTPUT_DIR%\N90-HardwareMonitor-SingleFile.zip'"

echo.
echo Single file build completed successfully!
echo Package location: %OUTPUT_DIR%\N90-HardwareMonitor-SingleFile.zip
echo Folder location: %OUTPUT_DIR%\%PACKAGE_DIR%\
echo.
echo Note: This package contains optimized single-file executables.
echo.
pause
