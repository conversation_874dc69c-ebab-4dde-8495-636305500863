using System;
using System.Runtime.CompilerServices;

namespace N90.Shared.Services
{
    /// <summary>
    /// 日志扩展方法，提供更便捷的日志记录功能
    /// </summary>
    public static class LoggerExtensions
    {
        /// <summary>
        /// 记录带有调用者信息的调试日志
        /// </summary>
        public static void LogDebugWithCaller(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
#if DEBUG
            var fileName = System.IO.Path.GetFileName(filePath);
            var logMessage = $"[{fileName}:{memberName}:{lineNumber}] {message}";
            Logger.LogDebug(logMessage);
#endif
        }

        /// <summary>
        /// 记录带有调用者信息的错误日志
        /// </summary>
        public static void LogErrorWithCaller(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            var fileName = System.IO.Path.GetFileName(filePath);
            var logMessage = $"[{fileName}:{memberName}:{lineNumber}] {message}";
            Logger.LogError(logMessage);
        }

        /// <summary>
        /// 记录带有调用者信息的异常日志
        /// </summary>
        public static void LogExceptionWithCaller(
            Exception exception,
            string message = null,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            var fileName = System.IO.Path.GetFileName(filePath);
            var callerInfo = $"[{fileName}:{memberName}:{lineNumber}]";
            var logMessage = string.IsNullOrEmpty(message) 
                ? $"{callerInfo} Exception occurred" 
                : $"{callerInfo} {message}";
            
            Logger.LogError(exception, logMessage);
        }

        /// <summary>
        /// 记录方法进入日志（仅在DEBUG模式下）
        /// </summary>
        public static void LogMethodEnter(
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "")
        {
#if DEBUG
            var fileName = System.IO.Path.GetFileName(filePath);
            Logger.LogDebug($"[{fileName}:{memberName}] Method entered");
#endif
        }

        /// <summary>
        /// 记录方法退出日志（仅在DEBUG模式下）
        /// </summary>
        public static void LogMethodExit(
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "")
        {
#if DEBUG
            var fileName = System.IO.Path.GetFileName(filePath);
            Logger.LogDebug($"[{fileName}:{memberName}] Method exited");
#endif
        }

        /// <summary>
        /// 记录性能测量日志
        /// </summary>
        public static void LogPerformance(
            string operation,
            TimeSpan elapsed,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "")
        {
            var fileName = System.IO.Path.GetFileName(filePath);
            var logMessage = $"[{fileName}:{memberName}] {operation} took {elapsed.TotalMilliseconds:F2}ms";
            
            if (elapsed.TotalMilliseconds > 1000) // 超过1秒记录为警告
            {
                Logger.LogWarning(logMessage);
            }
            else
            {
                Logger.LogInfo(logMessage);
            }
        }

        /// <summary>
        /// 记录硬件数据日志
        /// </summary>
        public static void LogHardwareData(string component, string metric, object value)
        {
            Logger.LogDebug($"Hardware [{component}] {metric}: {value}");
        }

        /// <summary>
        /// 记录HID通信日志
        /// </summary>
        public static void LogHidCommunication(string operation, string data, bool success)
        {
            var status = success ? "SUCCESS" : "FAILED";
            var message = $"HID [{operation}] {status}: {data}";
            
            if (success)
            {
                Logger.LogDebug(message);
            }
            else
            {
                Logger.LogError(message);
            }
        }

        /// <summary>
        /// 记录服务状态变化日志
        /// </summary>
        public static void LogServiceStatus(string serviceName, string status, string details = null)
        {
            var message = string.IsNullOrEmpty(details) 
                ? $"Service [{serviceName}] {status}" 
                : $"Service [{serviceName}] {status}: {details}";
            
            Logger.LogInfo(message);
        }

        /// <summary>
        /// 记录配置变更日志
        /// </summary>
        public static void LogConfigurationChange(string setting, object oldValue, object newValue)
        {
            var message = $"Configuration [{setting}] changed from '{oldValue}' to '{newValue}'";
            Logger.LogInfo(message);
        }
    }

    /// <summary>
    /// 性能测量辅助类
    /// </summary>
    public class PerformanceLogger : IDisposable
    {
        private readonly string _operation;
        private readonly DateTime _startTime;
        private readonly string _memberName;
        private readonly string _filePath;

        public PerformanceLogger(
            string operation,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "")
        {
            _operation = operation;
            _startTime = DateTime.Now;
            _memberName = memberName;
            _filePath = filePath;
        }

        public void Dispose()
        {
            var elapsed = DateTime.Now - _startTime;
            LoggerExtensions.LogPerformance(_operation, elapsed, _memberName, _filePath);
        }
    }
}
