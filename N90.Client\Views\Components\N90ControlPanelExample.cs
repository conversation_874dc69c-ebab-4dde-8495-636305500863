using System;
using System.Windows;
using System.Windows.Controls;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// N90ControlPanel组件使用示例
    /// 展示如何在其他页面或窗口中使用N90ControlPanel组件
    /// </summary>
    public class N90ControlPanelExample
    {
        /// <summary>
        /// 在代码中创建和配置N90ControlPanel组件的示例
        /// </summary>
        public static N90ControlPanel CreateControlPanel()
        {
            var controlPanel = new N90ControlPanel
            {
                // 设置组件属性
                Title = "N90",
                ImageSource = "/Resources/Images/img4.png",
                IconSource = "/Resources/Images/img1.png",
                ControlTitle = "Running Light Switch Brightness",
                BrightnessValue = 75, // 设置初始亮度为75%
                Width = 270,
                HorizontalAlignment = HorizontalAlignment.Left,
                VerticalAlignment = VerticalAlignment.Top
            };

            // 订阅事件
            controlPanel.LightSwitchChanged += OnLightSwitchChanged;
            controlPanel.BrightnessChanged += OnBrightnessChanged;

            return controlPanel;
        }

        /// <summary>
        /// 灯光开关状态改变事件处理示例
        /// </summary>
        private static void OnLightSwitchChanged(object sender, LightSwitchEventArgs e)
        {
            Console.WriteLine($"示例：灯光状态改变为 {(e.IsOn ? "开启" : "关闭")}");
            
            // 在这里可以添加自定义的业务逻辑
            // 例如：保存设置到配置文件、发送命令到硬件设备等
        }

        /// <summary>
        /// 亮度改变事件处理示例
        /// </summary>
        private static void OnBrightnessChanged(object sender, BrightnessChangedEventArgs e)
        {
            Console.WriteLine($"示例：亮度改变为 {e.Brightness}%");
            
            // 在这里可以添加自定义的业务逻辑
            // 例如：保存亮度设置、调用API更新硬件亮度等
        }

        /// <summary>
        /// 演示如何通过代码控制组件状态
        /// </summary>
        public static void DemonstrateControlMethods(N90ControlPanel controlPanel)
        {
            if (controlPanel == null) return;

            // 设置灯光开关状态
            controlPanel.SetLightSwitch(true);  // 开启灯光
            
            // 设置亮度
            controlPanel.SetBrightness(80);     // 设置亮度为80%
            
            // 修改显示文本
            controlPanel.Title = "自定义标题";
            controlPanel.ControlTitle = "自定义控制标题";
            
            // 修改图片源
            controlPanel.ImageSource = "/Resources/Images/custom_image.png";
            controlPanel.IconSource = "/Resources/Images/custom_icon.png";
        }
    }
}

/*
XAML中使用N90ControlPanel组件的示例：

<Window x:Class="YourNamespace.YourWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:components="clr-namespace:N90.Client.Views.Components">
    
    <Grid>
        <!-- 基本使用 -->
        <components:N90ControlPanel x:Name="ControlPanel1"
                                    Title="N90"
                                    ImageSource="/Resources/Images/img4.png"
                                    IconSource="/Resources/Images/img1.png"
                                    ControlTitle="Running Light Switch Brightness"
                                    BrightnessValue="50"
                                    LightSwitchChanged="ControlPanel_LightSwitchChanged"
                                    BrightnessChanged="ControlPanel_BrightnessChanged"/>
        
        <!-- 自定义配置 -->
        <components:N90ControlPanel x:Name="ControlPanel2"
                                    Grid.Column="1"
                                    Title="自定义设备"
                                    ImageSource="/Resources/Images/custom_device.png"
                                    IconSource="/Resources/Images/custom_icon.png"
                                    ControlTitle="设备亮度控制"
                                    BrightnessValue="75"
                                    LightSwitchChanged="ControlPanel_LightSwitchChanged"
                                    BrightnessChanged="ControlPanel_BrightnessChanged"/>
    </Grid>
</Window>

对应的代码文件事件处理：

private void ControlPanel_LightSwitchChanged(object sender, Components.LightSwitchEventArgs e)
{
    // 处理灯光开关状态改变
    MessageBox.Show($"灯光状态: {(e.IsOn ? "开启" : "关闭")}");
}

private void ControlPanel_BrightnessChanged(object sender, Components.BrightnessChangedEventArgs e)
{
    // 处理亮度改变
    MessageBox.Show($"亮度: {e.Brightness}%");
}
*/
