using System;
using System.Windows;
using System.Windows.Controls;

namespace N90.Client.Views.Components
{
    public partial class InputDialog : UserControl
    {
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(InputDialog), new PropertyMetadata("Enter Content"));

        public static readonly DependencyProperty InputTextProperty =
            DependencyProperty.Register("InputText", typeof(string), typeof(InputDialog), new PropertyMetadata(string.Empty));

        public static readonly DependencyProperty PlaceholderProperty =
            DependencyProperty.Register("Placeholder", typeof(string), typeof(InputDialog), new PropertyMetadata("Enter text..."));

        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public string InputText
        {
            get { return (string)GetValue(InputTextProperty); }
            set { SetValue(InputTextProperty, value); }
        }

        public string Placeholder
        {
            get { return (string)GetValue(PlaceholderProperty); }
            set { SetValue(PlaceholderProperty, value); }
        }

        public event EventHandler<DialogResultEventArgs> DialogResult;

        public InputDialog()
        {
            try
            {
                InitializeComponent();
                Loaded += InputDialog_Loaded;
            }
            catch (Exception ex)
            {
                // 使用统一的日志系统记录XAML加载错误
                N90.Shared.Services.Logger.LogError(ex, "InputDialog XAML loading failed");

                // 重新抛出异常以便捕获
                throw;
            }
        }

        private void InputDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // 更新占位符显示
            UpdatePlaceholderVisibility();

            // 自动聚焦到输入框
            InputTextBox.Focus();

            // 如果有默认文本，选中所有文本方便用户替换
            if (!string.IsNullOrEmpty(InputTextBox.Text))
            {
                InputTextBox.SelectAll();
            }

            // 添加键盘事件处理
            KeyDown += InputDialog_KeyDown;
        }

        private void InputDialog_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // ESC键取消
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                CancelButton_Click(this, new RoutedEventArgs());
            }
            // Ctrl+Enter确认
            else if (e.Key == System.Windows.Input.Key.Enter &&
                     (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
            {
                ConfirmButton_Click(this, new RoutedEventArgs());
            }
        }

        private void InputTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePlaceholderVisibility();
        }

        private void UpdatePlaceholderVisibility()
        {
            if (PlaceholderTextBlock != null && InputTextBox != null)
            {
                PlaceholderTextBlock.Visibility = string.IsNullOrEmpty(InputTextBox.Text) ?
                    Visibility.Visible : Visibility.Collapsed;
            }
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult?.Invoke(this, new DialogResultEventArgs(true, InputText));
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult?.Invoke(this, new DialogResultEventArgs(false, InputText));
        }

        // 静态方法用于显示对话框
        public static void Show(string title, string defaultText = "", Action<bool, string> callback = null)
        {
            try
            {
                var dialog = new InputDialog
                {
                    Title = title,
                    InputText = defaultText
                };

                var window = new Window
                {
                    Content = dialog,
                    Title = title,
                    WindowStyle = WindowStyle.None,
                    AllowsTransparency = true,
                    Background = System.Windows.Media.Brushes.Transparent,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    Width = 600,
                    Height = 350,
                    ResizeMode = ResizeMode.NoResize,
                    ShowInTaskbar = false,
                    Topmost = true
                };

                dialog.DialogResult += (s, args) =>
                {
                    window.Close();
                    callback?.Invoke(args.IsConfirmed, args.InputText);
                };

                // 使用同步上下文确保在UI线程上执行
                var dispatcher = Application.Current?.Dispatcher ?? System.Windows.Threading.Dispatcher.CurrentDispatcher;
                dispatcher.Invoke(() =>
                {
                    try
                    {
                        window.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不使用备用对话框，让用户看到错误
                        throw new Exception($"Custom dialog loading failed, error details: {ex.Message}\n\nPlease check error.log file for detailed information", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                // 记录详细异常信息到可访问的位置
                try
                {
                    var logPath = System.IO.Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "N90_Error.log");
                    
                    System.IO.File.AppendAllText(logPath,
                        $"[{DateTime.Now}] InputDialog display failed: {ex}\n");
                }
                catch
                {
                    // 如果日志写入失败，忽略
                }
                
                // 抛出异常让调用者知道发生了什么
                throw new Exception($"Custom dialog loading failed: {ex.Message}", ex);
            }
        }
    }

    public class DialogResultEventArgs : EventArgs
    {
        public bool IsConfirmed { get; }
        public string InputText { get; }

        public DialogResultEventArgs(bool isConfirmed, string inputText)
        {
            IsConfirmed = isConfirmed;
            InputText = inputText;
        }
    }
}
