using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace N90.Client.Views.Components
{
    public partial class NavigationButton : UserControl
    {
        public static readonly DependencyProperty ButtonTextProperty =
            DependencyProperty.Register("ButtonText", typeof(string), typeof(NavigationButton), new PropertyMetadata("Button"));

        public static readonly DependencyProperty IsSelectedProperty =
            DependencyProperty.Register("IsSelected", typeof(bool), typeof(NavigationButton), new PropertyMetadata(false, OnIsSelectedChanged));

        public string ButtonText
        {
            get { return (string)GetValue(ButtonTextProperty); }
            set { SetValue(ButtonTextProperty, value); }
        }

        public bool IsSelected
        {
            get { return (bool)GetValue(IsSelectedProperty); }
            set { SetValue(IsSelectedProperty, value); }
        }

        public event RoutedEventHandler Click;

        public NavigationButton()
        {
            InitializeComponent();
            this.MouseLeftButtonDown += NavigationButton_MouseLeftButtonDown;
            this.MouseEnter += NavigationButton_MouseEnter;
            this.MouseLeave += NavigationButton_MouseLeave;
        }

        private void NavigationButton_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Click?.Invoke(this, new RoutedEventArgs());
        }

        private void NavigationButton_MouseEnter(object sender, MouseEventArgs e)
        {
            if (!IsSelected)
            {
                ButtonPath.Fill = new SolidColorBrush(Color.FromRgb(0x3A, 0x3A, 0x3A));
            }
        }

        private void NavigationButton_MouseLeave(object sender, MouseEventArgs e)
        {
            if (!IsSelected)
            {
                ButtonPath.Fill = (Brush)FindResource("NormalFillBrush");
            }
        }

        private static void OnIsSelectedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var button = (NavigationButton)d;
            button.UpdateButtonStyle();
        }

        private void UpdateButtonStyle()
        {
            if (IsSelected)
            {
                ButtonBorder.Style = (Style)FindResource("SelectedButtonStyle");
                ButtonPath.Fill = (Brush)FindResource("SelectedFillBrush");
                ButtonPath.Stroke = (Brush)FindResource("SelectedStrokeBrush");
                ButtonPath.StrokeThickness = 2;
            }
            else
            {
                ButtonBorder.Style = (Style)FindResource("NormalButtonStyle");
                ButtonPath.Fill = (Brush)FindResource("NormalFillBrush");
                ButtonPath.Stroke = (Brush)FindResource("NormalStrokeBrush");
                ButtonPath.StrokeThickness = 1;
            }
        }
    }
}
