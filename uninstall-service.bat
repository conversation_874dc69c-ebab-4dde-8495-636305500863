@echo off
echo Uninstalling N90 Hardware Monitor Service...

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as administrator...
) else (
    echo This script must be run as administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

set "SERVICE_NAME=N90HardwareMonitorService"

REM Stop the service
echo Stopping service...
sc stop "%SERVICE_NAME%"

if %errorLevel% == 0 (
    echo Service stopped successfully.
) else (
    echo Service was not running or failed to stop.
)

REM Wait a moment for service to fully stop
timeout /t 3 /nobreak >nul

REM Delete the service
echo Removing service...
sc delete "%SERVICE_NAME%"

if %errorLevel% == 0 (
    echo Service removed successfully!
    echo.
    echo N90 Hardware Monitor Service has been uninstalled.
    echo.
) else (
    echo Error: Failed to remove service!
    echo The service may not exist or you may not have sufficient privileges.
)

echo.
pause
