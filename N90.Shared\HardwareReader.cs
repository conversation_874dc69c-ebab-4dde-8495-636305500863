// 采集监控数据的核心接口，适合服务端直接调用
using System;
using System.Threading.Tasks;

namespace N90.Shared
{
    public class HardwareReader
    {
        // 这里只迁移接口与核心逻辑，底层依赖如 OpenHardwareMonitor 仍需服务端项目引用
        public HardwareReader() { /* ...初始化... */ }
        public HardwareData ReadHardwareData(HardwareData data, bool useFahrenheit = false)
        {
            // 采集逻辑建议保留在服务端实现，接口定义可放在Shared
            throw new NotImplementedException("请在服务端实现具体采集逻辑");
        }
    }
}
