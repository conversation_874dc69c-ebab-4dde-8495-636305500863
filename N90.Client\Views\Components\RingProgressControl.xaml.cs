using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// RingProgressControl.xaml 的交互逻辑
    /// 环形进度控件，根据传入的值（0-100）动态显示进度
    /// </summary>
    public partial class RingProgressControl : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 进度值（0-100）
        /// </summary>
        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register(
                nameof(Value),
                typeof(double),
                typeof(RingProgressControl),
                new PropertyMetadata(0.0, OnValueChanged));

        /// <summary>
        /// 显示标签
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(
                nameof(Label),
                typeof(string),
                typeof(RingProgressControl),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否显示百分号
        /// </summary>
        public static readonly DependencyProperty ShowPercentSignProperty =
            DependencyProperty.Register(
                nameof(ShowPercentSign),
                typeof(bool),
                typeof(RingProgressControl),
                new PropertyMetadata(true, OnShowPercentSignChanged));

        /// <summary>
        /// 数值格式化字符串
        /// </summary>
        public static readonly DependencyProperty ValueFormatProperty =
            DependencyProperty.Register(
                nameof(ValueFormat),
                typeof(string),
                typeof(RingProgressControl),
                new PropertyMetadata("F0", OnValueFormatChanged));

        #endregion

        #region 属性

        /// <summary>
        /// 进度值（0-100）
        /// </summary>
        public double Value
        {
            get => (double)GetValue(ValueProperty);
            set => SetValue(ValueProperty, value);
        }

        /// <summary>
        /// 显示标签
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 是否显示百分号
        /// </summary>
        public bool ShowPercentSign
        {
            get => (bool)GetValue(ShowPercentSignProperty);
            set => SetValue(ShowPercentSignProperty, value);
        }

        /// <summary>
        /// 数值格式化字符串
        /// </summary>
        public string ValueFormat
        {
            get => (string)GetValue(ValueFormatProperty);
            set => SetValue(ValueFormatProperty, value);
        }

        #endregion

        #region 私有字段

        /// <summary>
        /// 分段图片控件列表
        /// </summary>
        private List<Image> _segmentImages;

        #endregion

        public RingProgressControl()
        {
            InitializeComponent();
            
            // 初始化分段图片列表
            _segmentImages = new List<Image>
            {
                Segment1, Segment2, Segment3, Segment4, Segment5,
                Segment6, Segment7, Segment8, Segment9, Segment10
            };

            // 初始化显示
            Loaded += (sender, e) => UpdateProgress();
        }

        #region 依赖属性回调

        /// <summary>
        /// 值改变时的回调
        /// </summary>
        private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RingProgressControl control)
            {
                control.UpdateProgress();
                control.UpdateValueText();
            }
        }

        /// <summary>
        /// 显示百分号属性改变时的回调
        /// </summary>
        private static void OnShowPercentSignChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RingProgressControl control)
            {
                control.UpdateValueText();
            }
        }

        /// <summary>
        /// 数值格式改变时的回调
        /// </summary>
        private static void OnValueFormatChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RingProgressControl control)
            {
                control.UpdateValueText();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新进度显示
        /// </summary>
        private void UpdateProgress()
        {
            try
            {
                // 确保值在有效范围内
                var clampedValue = Math.Max(0, Math.Min(100, Value));
                
                // 计算需要显示的分段数量（每个分段代表10%）
                var segmentCount = (int)Math.Ceiling(clampedValue / 10.0);
                
                // 更新分段显示
                for (int i = 0; i < _segmentImages.Count; i++)
                {
                    if (_segmentImages[i] != null)
                    {
                        _segmentImages[i].Visibility = i < segmentCount ? Visibility.Visible : Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新环形进度时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新数值文本显示
        /// </summary>
        private void UpdateValueText()
        {
            try
            {
                if (ValueText != null)
                {
                    var formattedValue = Value.ToString(ValueFormat);
                    ValueText.Text = ShowPercentSign ? $"{formattedValue}%" : formattedValue;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新数值文本时出错: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置进度值（带动画效果的版本，可以后续扩展）
        /// </summary>
        /// <param name="targetValue">目标值（0-100）</param>
        public void SetValue(double targetValue)
        {
            Value = Math.Max(0, Math.Min(100, targetValue));
        }

        /// <summary>
        /// 重置进度到0
        /// </summary>
        public void Reset()
        {
            Value = 0;
        }

        /// <summary>
        /// 设置为满进度
        /// </summary>
        public void SetFull()
        {
            Value = 100;
        }

        #endregion
    }
}
