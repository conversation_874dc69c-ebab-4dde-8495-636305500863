using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using N90.Shared;
using N90.Shared.Services;
using N90.Shared.Communication;
using N90.Client.Services;

namespace N90.Client
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private Dictionary<string, (System.Windows.Controls.Label valueLabel, System.Windows.Controls.CheckBox showCheckBox)> hardwareControls = null!;
        private readonly LanguageService languageService;
        private readonly TemperatureUnitService temperatureUnitService;
        private readonly StartupService startupService;
        private HardwareData hardwareData = null!;
        private NamedPipeClient pipeClient = null!;
        private NotifyIcon notifyIcon = null!;
        private (ToolStripMenuItem chinese, ToolStripMenuItem english) languageMenuItems;
        private (ToolStripMenuItem celsius, ToolStripMenuItem fahrenheit) temperatureMenuItems;
        private ToolStripMenuItem? startupMenuItem;
        private const int UPDATE_INTERVAL = 2000; // 2 seconds

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // Initialize services
                languageService = new LanguageService();
                temperatureUnitService = new TemperatureUnitService();
                startupService = new StartupService("N90HardwareMonitor");

                InitHardwareData();
                InitHardwareControls();

                // Apply settings first
                ApplyLanguageSetting();
                ApplyTemperatureSetting();
                UpdateStartupMenuState();

                // Initialize NotifyIcon after language settings are applied
                InitializeNotifyIcon();

                // Update language display after initialization
                UpdateLanguageDisplay();

                // Start Service if needed (integrated launcher functionality)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EnsureServiceRunning();
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            Console.WriteLine($"Failed to ensure service running: {ex.Message}");
                        });
                    }
                });

                // Initialize communication
                InitializeCommunication();

                this.Closing += MainWindow_Closing;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error initializing application: {ex.Message}", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitHardwareData()
        {
            hardwareData = new HardwareData();
            hardwareData.Date = new Date();
            hardwareData.Time = new Time();
            hardwareData.Weekday = new Weekday();
            hardwareData.CPUTemperature = new CPUTemperature();
            hardwareData.CPUUsage = new CPUUsage();
            hardwareData.CPUPower = new CPUPower();
            hardwareData.CPUFanSpeed = new CPUFanSpeed();
            hardwareData.CPUModel = new CPUModel();
            hardwareData.GPUTemperature = new GPUTemperature();
            hardwareData.GPUMemoryUsage = new GPUMemoryUsage();
            hardwareData.GPUPower = new GPUPower();
            hardwareData.GPUModel = new GPUModel();
            hardwareData.RAMUsage = new RAMUsage();
            hardwareData.AvailableRAM = new AvailableRAM();
            hardwareData.CaseFan1Speed = new CaseFan1Speed();
            hardwareData.CaseFan2Speed = new CaseFan2Speed();
            hardwareData.HDDTemperature = new HDDTemperature();
            hardwareData.HDDUsage = new HDDUsage();
            hardwareData.UploadSpeed = new UploadSpeed();
            hardwareData.DownloadSpeed = new DownloadSpeed();
            hardwareData.CustomString = new CustomString();

            // Load saved show states from registry
            LoadHardwareDisplayStatesFromRegistry();
        }

        private void LoadHardwareDisplayStatesFromRegistry()
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");

                // Load Date/Time
                hardwareData.Date.show = Convert.ToBoolean(registryKey.GetValue("Date", 1));
                hardwareData.Time.show = Convert.ToBoolean(registryKey.GetValue("Time", 1));
                hardwareData.Weekday.show = Convert.ToBoolean(registryKey.GetValue("Weekday", 1));

                // Load CPU
                hardwareData.CPUTemperature.show = Convert.ToBoolean(registryKey.GetValue("CPUTemperature", 1));
                hardwareData.CPUUsage.show = Convert.ToBoolean(registryKey.GetValue("CPUUsage", 1));
                hardwareData.CPUPower.show = Convert.ToBoolean(registryKey.GetValue("CPUPower", 1));
                hardwareData.CPUFanSpeed.show = Convert.ToBoolean(registryKey.GetValue("CPUFanSpeed", 1));
                hardwareData.CPUModel.show = Convert.ToBoolean(registryKey.GetValue("CPUModel", 1));

                // Load GPU
                hardwareData.GPUTemperature.show = Convert.ToBoolean(registryKey.GetValue("GPUTemperature", 1));
                hardwareData.GPUMemoryUsage.show = Convert.ToBoolean(registryKey.GetValue("GPUMemoryUsage", 1));
                hardwareData.GPUPower.show = Convert.ToBoolean(registryKey.GetValue("GPUPower", 1));
                hardwareData.GPUModel.show = Convert.ToBoolean(registryKey.GetValue("GPUModel", 1));

                // Load RAM
                hardwareData.RAMUsage.show = Convert.ToBoolean(registryKey.GetValue("RAMUsage", 1));
                hardwareData.AvailableRAM.show = Convert.ToBoolean(registryKey.GetValue("AvailableRAM", 1));

                // Load Fans
                hardwareData.CaseFan1Speed.show = Convert.ToBoolean(registryKey.GetValue("CaseFan1Speed", 1));
                hardwareData.CaseFan2Speed.show = Convert.ToBoolean(registryKey.GetValue("CaseFan2Speed", 1));

                // Load HDD
                hardwareData.HDDTemperature.show = Convert.ToBoolean(registryKey.GetValue("HDDTemperature", 1));
                hardwareData.HDDUsage.show = Convert.ToBoolean(registryKey.GetValue("HDDUsage", 1));

                // Load Network
                hardwareData.UploadSpeed.show = Convert.ToBoolean(registryKey.GetValue("UploadSpeed", 1));
                hardwareData.DownloadSpeed.show = Convert.ToBoolean(registryKey.GetValue("DownloadSpeed", 1));

                // Load CustomString
                hardwareData.CustomString.show = Convert.ToBoolean(registryKey.GetValue("CustomString", 1));
                hardwareData.CustomString.value = registryKey.GetValue("CustomStringValue", "") as string;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading hardware display state: {ex.Message}");
                // If loading fails, use default values (all true)
                SetDefaultShowStates();
            }
        }

        private void SetDefaultShowStates()
        {
            hardwareData.Date.show = true;
            hardwareData.Time.show = true;
            hardwareData.Weekday.show = true;
            hardwareData.CPUTemperature.show = true;
            hardwareData.CPUUsage.show = true;
            hardwareData.CPUPower.show = true;
            hardwareData.CPUFanSpeed.show = true;
            hardwareData.CPUModel.show = true;
            hardwareData.GPUTemperature.show = true;
            hardwareData.GPUMemoryUsage.show = true;
            hardwareData.GPUPower.show = true;
            hardwareData.GPUModel.show = true;
            hardwareData.RAMUsage.show = true;
            hardwareData.AvailableRAM.show = true;
            hardwareData.CaseFan1Speed.show = true;
            hardwareData.CaseFan2Speed.show = true;
            hardwareData.HDDTemperature.show = true;
            hardwareData.HDDUsage.show = true;
            hardwareData.UploadSpeed.show = true;
            hardwareData.DownloadSpeed.show = true;
            hardwareData.CustomString.show = true;
        }

        private void InitHardwareControls()
        {
            hardwareControls = new Dictionary<string, (System.Windows.Controls.Label, System.Windows.Controls.CheckBox)>
            {
                { "Date", (dateValueLabel, dateCheckBox) },
                { "Time", (timeValueLabel, timeCheckBox) },
                { "Weekday", (weekdayValueLabel, weekdayCheckBox) },
                { "CPUTemperature", (cpuTemperatureValueLabel, cpuTemperatureCheckBox) },
                { "CPUUsage", (cpuUsageValueLabel, cpuUsageCheckBox) },
                { "CPUPower", (cpuPowerValueLabel, cpuPowerCheckBox) },
                { "CPUFanSpeed", (cpuFanSpeedValueLabel, cpuFanSpeedCheckBox) },
                { "CPUModel", (cpuModelValueLabel, cpuModelCheckBox) },
                { "GPUTemperature", (gpuTemperatureValueLabel, gpuTemperatureCheckBox) },
                { "GPUMemoryUsage", (gpuMemoryUsageValueLabel, gpuMemoryUsageCheckBox) },
                { "GPUPower", (gpuPowerValueLabel, gpuPowerCheckBox) },
                { "GPUModel", (gpuModelValueLabel, gpuModelCheckBox) },
                { "RAMUsage", (ramUsageValueLabel, ramUsageCheckBox) },
                { "AvailableRAM", (availableRamValueLabel, availableRamCheckBox) },
                { "CaseFan1Speed", (caseFan1SpeedValueLabel, caseFan1SpeedCheckBox) },
                { "CaseFan2Speed", (caseFan2SpeedValueLabel, caseFan2SpeedCheckBox) },
                { "HDDTemperature", (hddTemperatureValueLabel, hddTemperatureCheckBox) },
                { "HDDUsage", (hddUsageValueLabel, hddUsageCheckBox) },
                { "UploadSpeed", (uploadSpeedValueLabel, uploadSpeedCheckBox) },
                { "DownloadSpeed", (downloadSpeedValueLabel, downloadSpeedCheckBox) },
            };

            // Bind CheckBox events and load initial states
            BindCheckBoxEvents();
            LoadHardwareDisplayStates();
        }

        private void BindCheckBoxEvents()
        {
            // Bind CheckBox events for all hardware items
            foreach (var kvp in hardwareControls)
            {
                var checkBox = kvp.Value.showCheckBox;
                var key = kvp.Key;
                checkBox.Checked += (s, e) => OnHardwareShowChanged(key, true);
                checkBox.Unchecked += (s, e) => OnHardwareShowChanged(key, false);
            }
        }

        private void OnHardwareShowChanged(string key, bool isChecked)
        {
            // Update hardwareData show state
            switch (key)
            {
                case "Date": hardwareData.Date.show = isChecked; break;
                case "Time": hardwareData.Time.show = isChecked; break;
                case "Weekday": hardwareData.Weekday.show = isChecked; break;
                case "CPUTemperature": hardwareData.CPUTemperature.show = isChecked; break;
                case "CPUUsage": hardwareData.CPUUsage.show = isChecked; break;
                case "CPUPower": hardwareData.CPUPower.show = isChecked; break;
                case "CPUFanSpeed": hardwareData.CPUFanSpeed.show = isChecked; break;
                case "CPUModel": hardwareData.CPUModel.show = isChecked; break;
                case "GPUTemperature": hardwareData.GPUTemperature.show = isChecked; break;
                case "GPUMemoryUsage": hardwareData.GPUMemoryUsage.show = isChecked; break;
                case "GPUPower": hardwareData.GPUPower.show = isChecked; break;
                case "GPUModel": hardwareData.GPUModel.show = isChecked; break;
                case "RAMUsage": hardwareData.RAMUsage.show = isChecked; break;
                case "AvailableRAM": hardwareData.AvailableRAM.show = isChecked; break;
                case "CaseFan1Speed": hardwareData.CaseFan1Speed.show = isChecked; break;
                case "CaseFan2Speed": hardwareData.CaseFan2Speed.show = isChecked; break;
                case "HDDTemperature": hardwareData.HDDTemperature.show = isChecked; break;
                case "HDDUsage": hardwareData.HDDUsage.show = isChecked; break;
                case "UploadSpeed": hardwareData.UploadSpeed.show = isChecked; break;
                case "DownloadSpeed": hardwareData.DownloadSpeed.show = isChecked; break;
            }

            // Save to configuration
            SaveHardwareDisplayStates();
        }

        private void LoadHardwareDisplayStates()
        {
            // Load show states from hardwareData to CheckBoxes
            foreach (var kvp in hardwareControls)
            {
                var checkBox = kvp.Value.showCheckBox;
                var key = kvp.Key;

                bool showState = GetHardwareShowState(key);
                checkBox.IsChecked = showState;
            }
        }

        private bool GetHardwareShowState(string key)
        {
            return key switch
            {
                "Date" => hardwareData.Date.show,
                "Time" => hardwareData.Time.show,
                "Weekday" => hardwareData.Weekday.show,
                "CPUTemperature" => hardwareData.CPUTemperature.show,
                "CPUUsage" => hardwareData.CPUUsage.show,
                "CPUPower" => hardwareData.CPUPower.show,
                "CPUFanSpeed" => hardwareData.CPUFanSpeed.show,
                "CPUModel" => hardwareData.CPUModel.show,
                "GPUTemperature" => hardwareData.GPUTemperature.show,
                "GPUMemoryUsage" => hardwareData.GPUMemoryUsage.show,
                "GPUPower" => hardwareData.GPUPower.show,
                "GPUModel" => hardwareData.GPUModel.show,
                "RAMUsage" => hardwareData.RAMUsage.show,
                "AvailableRAM" => hardwareData.AvailableRAM.show,
                "CaseFan1Speed" => hardwareData.CaseFan1Speed.show,
                "CaseFan2Speed" => hardwareData.CaseFan2Speed.show,
                "HDDTemperature" => hardwareData.HDDTemperature.show,
                "HDDUsage" => hardwareData.HDDUsage.show,
                "UploadSpeed" => hardwareData.UploadSpeed.show,
                "DownloadSpeed" => hardwareData.DownloadSpeed.show,
                _ => true // Default to true for unknown keys
            };
        }

        private void SaveHardwareDisplayStates()
        {
            try
            {
                // Use registry to save states (similar to Service)
                using var registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");

                // Save Date/Time
                registryKey.SetValue("Date", hardwareData.Date.show ? 1 : 0);
                registryKey.SetValue("Time", hardwareData.Time.show ? 1 : 0);
                registryKey.SetValue("Weekday", hardwareData.Weekday.show ? 1 : 0);

                // Save CPU
                registryKey.SetValue("CPUTemperature", hardwareData.CPUTemperature.show ? 1 : 0);
                registryKey.SetValue("CPUUsage", hardwareData.CPUUsage.show ? 1 : 0);
                registryKey.SetValue("CPUPower", hardwareData.CPUPower.show ? 1 : 0);
                registryKey.SetValue("CPUFanSpeed", hardwareData.CPUFanSpeed.show ? 1 : 0);
                registryKey.SetValue("CPUModel", hardwareData.CPUModel.show ? 1 : 0);

                // Save GPU
                registryKey.SetValue("GPUTemperature", hardwareData.GPUTemperature.show ? 1 : 0);
                registryKey.SetValue("GPUMemoryUsage", hardwareData.GPUMemoryUsage.show ? 1 : 0);
                registryKey.SetValue("GPUPower", hardwareData.GPUPower.show ? 1 : 0);
                registryKey.SetValue("GPUModel", hardwareData.GPUModel.show ? 1 : 0);

                // Save RAM
                registryKey.SetValue("RAMUsage", hardwareData.RAMUsage.show ? 1 : 0);
                registryKey.SetValue("AvailableRAM", hardwareData.AvailableRAM.show ? 1 : 0);

                // Save Fans
                registryKey.SetValue("CaseFan1Speed", hardwareData.CaseFan1Speed.show ? 1 : 0);
                registryKey.SetValue("CaseFan2Speed", hardwareData.CaseFan2Speed.show ? 1 : 0);

                // Save HDD
                registryKey.SetValue("HDDTemperature", hardwareData.HDDTemperature.show ? 1 : 0);
                registryKey.SetValue("HDDUsage", hardwareData.HDDUsage.show ? 1 : 0);

                // Save Network
                registryKey.SetValue("UploadSpeed", hardwareData.UploadSpeed.show ? 1 : 0);
                registryKey.SetValue("DownloadSpeed", hardwareData.DownloadSpeed.show ? 1 : 0);

                // Save CustomString
                registryKey.SetValue("CustomString", hardwareData.CustomString.show ? 1 : 0);
                registryKey.SetValue("CustomStringValue", hardwareData.CustomString.value ?? "");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving hardware display state: {ex.Message}");
            }
        }

        private void InitializeNotifyIcon()
        {
            try
            {
                notifyIcon = new NotifyIcon();

                // Try to load custom icon, fallback to system icon
                try
                {
                    string iconPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "favicon.ico");
                    if (System.IO.File.Exists(iconPath))
                    {
                        notifyIcon.Icon = new System.Drawing.Icon(iconPath);
                    }
                    else
                    {
                        notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                    }
                }
                catch
                {
                    notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                }

                notifyIcon.Text = "N90";
                notifyIcon.Visible = true;

                // Create context menu
                var contextMenu = new ContextMenuStrip();

                // Language submenu
                var languageMenu = new ToolStripMenuItem("Language");
                var chineseMenu = new ToolStripMenuItem("中文", null, (s, e) => SwitchLanguage("zh-CN"));
                var englishMenu = new ToolStripMenuItem("English", null, (s, e) => SwitchLanguage("en-US"));
                languageMenu.DropDownItems.Add(chineseMenu);
                languageMenu.DropDownItems.Add(englishMenu);

                // Temperature unit submenu
                var temperatureMenu = new ToolStripMenuItem("Temperature Unit");
                var celsiusMenu = new ToolStripMenuItem("Celsius (°C)", null, (s, e) => SwitchTemperatureUnit(false));
                var fahrenheitMenu = new ToolStripMenuItem("Fahrenheit (°F)", null, (s, e) => SwitchTemperatureUnit(true));
                temperatureMenu.DropDownItems.Add(celsiusMenu);
                temperatureMenu.DropDownItems.Add(fahrenheitMenu);

                // Startup menu
                var startupMenu = new ToolStripMenuItem("Start with Windows", null, (s, e) => ToggleStartup());

                // Show/Hide menu
                var showMenu = new ToolStripMenuItem("Show", null, (s, e) => ShowWindow());

                // Exit menu
                var exitMenu = new ToolStripMenuItem("Exit", null, (s, e) => ExitApplication());

                // Add items to context menu
                contextMenu.Items.Add(showMenu);
                contextMenu.Items.Add(new ToolStripSeparator());
                contextMenu.Items.Add(languageMenu);
                contextMenu.Items.Add(temperatureMenu);
                contextMenu.Items.Add(new ToolStripSeparator());
                contextMenu.Items.Add(startupMenu);
                contextMenu.Items.Add(new ToolStripSeparator());
                contextMenu.Items.Add(exitMenu);

                notifyIcon.ContextMenuStrip = contextMenu;

                // Store menu references for updating
                languageMenuItems = (chineseMenu, englishMenu);
                temperatureMenuItems = (celsiusMenu, fahrenheitMenu);
                startupMenuItem = startupMenu;

                // Double click to show window
                notifyIcon.DoubleClick += (s, e) => ShowWindow();

                // Update menu states
                UpdateContextMenuStates();

                // Start minimized to tray
                this.WindowState = WindowState.Minimized;
                this.ShowInTaskbar = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"NotifyIcon initialization failed: {ex.Message}");
                // Continue without NotifyIcon if it fails
            }
        }

        private async void InitializeCommunication()
        {
            pipeClient = new NamedPipeClient("N90_Pipe");
            pipeClient.MessageReceived += OnMessageReceived;

            // Try to connect to service
            bool connected = await pipeClient.ConnectAsync();
            if (!connected)
            {
                // Start reading from JSON file as fallback
                _ = Task.Run(ReadFromJsonFile);
            }
        }

        private void OnMessageReceived(IpcMessage message)
        {
            if (message.Type == MessageType.HardwareData)
            {
                try
                {
                    var data = JsonSerializer.Deserialize<HardwareData>(message.Data);
                    Dispatcher.Invoke(() => UpdateHardwareDisplay(data));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deserializing hardware data: {ex.Message}");
                }
            }
        }

        private async Task ReadFromJsonFile()
        {
            // Try multiple possible paths for the JSON file
            string[] possiblePaths = {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "N90.Service", "bin", "Debug", "net8.0", "data.json"),
                @"D:\Code\2025\WPF\N90\N90.Service\bin\Debug\net8.0\data.json",
                @"D:\Code\2025\WPF\N90\data.json"
            };

            string filePath = null;

            // Find the first existing file
            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    filePath = path;
                    Console.WriteLine($"Found JSON file at: {filePath}");
                    break;
                }
            }

            if (filePath == null)
            {
                Console.WriteLine("JSON file not found in any expected location. Checking paths:");
                foreach (var path in possiblePaths)
                {
                    Console.WriteLine($"  Checked: {path} - Exists: {File.Exists(path)}");
                }
            }

            while (true)
            {
                try
                {
                    // Re-check if file exists if we haven't found it yet
                    if (filePath == null)
                    {
                        foreach (var path in possiblePaths)
                        {
                            if (File.Exists(path))
                            {
                                filePath = path;
                                Console.WriteLine($"Found JSON file at: {filePath}");
                                break;
                            }
                        }
                    }

                    if (filePath != null && File.Exists(filePath))
                    {
                        string json = await File.ReadAllTextAsync(filePath);
                        var data = JsonSerializer.Deserialize<HardwareData>(json);
                        Dispatcher.Invoke(() => {
                            UpdateHardwareDisplay(data);
                            Console.WriteLine($"Updated UI with data at {DateTime.Now:HH:mm:ss}");
                        });
                    }
                    else
                    {
                        Console.WriteLine($"JSON file not found at {DateTime.Now:HH:mm:ss}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error reading JSON file: {ex.Message}");
                }

                await Task.Delay(UPDATE_INTERVAL);
            }
        }

        private void UpdateHardwareDisplay(HardwareData data)
        {
            if (data == null)
            {
                Console.WriteLine("UpdateHardwareDisplay: data is null");
                return;
            }

            Console.WriteLine($"UpdateHardwareDisplay: Updating UI with data");

            // Update time data
            if (hardwareControls.ContainsKey("Date"))
            {
                hardwareControls["Date"].valueLabel.Content = data.Date?.value ?? "--";
                Console.WriteLine($"Date: {data.Date?.value}");
            }
            if (hardwareControls.ContainsKey("Time"))
            {
                hardwareControls["Time"].valueLabel.Content = data.Time?.value ?? "--";
                Console.WriteLine($"Time: {data.Time?.value}");
            }
            if (hardwareControls.ContainsKey("Weekday"))
            {
                hardwareControls["Weekday"].valueLabel.Content = data.Weekday?.value ?? "--";
                Console.WriteLine($"Weekday: {data.Weekday?.value}");
            }

            // Update CPU data (using old project formatting)
            UpdateTemperatureValue("CPUTemperature", data.CPUTemperature?.value);
            UpdatePercentageValue("CPUUsage", data.CPUUsage?.value);
            UpdatePowerValue("CPUPower", data.CPUPower?.value);
            UpdateFanSpeedValue("CPUFanSpeed", data.CPUFanSpeed?.value);
            if (hardwareControls.ContainsKey("CPUModel"))
                hardwareControls["CPUModel"].valueLabel.Content = data.CPUModel?.value ?? "--";

            // Update GPU data (using old project formatting)
            UpdateTemperatureValue("GPUTemperature", data.GPUTemperature?.value);
            UpdatePercentageValue("GPUMemoryUsage", data.GPUMemoryUsage?.value);
            UpdatePowerValue("GPUPower", data.GPUPower?.value);
            if (hardwareControls.ContainsKey("GPUModel"))
                hardwareControls["GPUModel"].valueLabel.Content = data.GPUModel?.value ?? "--";

            // Update RAM data (using old project formatting)
            UpdatePercentageValue("RAMUsage", data.RAMUsage?.value);
            UpdateMemoryValue("AvailableRAM", data.AvailableRAM?.value);

            // Update Fan data (using old project formatting)
            UpdateFanSpeedValue("CaseFan1Speed", data.CaseFan1Speed?.value);
            UpdateFanSpeedValue("CaseFan2Speed", data.CaseFan2Speed?.value);

            // Update HDD data (using old project formatting)
            UpdateTemperatureValue("HDDTemperature", data.HDDTemperature?.value);
            UpdatePercentageValue("HDDUsage", data.HDDUsage?.value);

            // Update Network data (using old project formatting)
            UpdateSpeedValue("UploadSpeed", data.UploadSpeed?.value);
            UpdateSpeedValue("DownloadSpeed", data.DownloadSpeed?.value);

            // Update custom string
            if (data.CustomString != null)
            {
                customStringTextBox.Text = data.CustomString.value ?? "";
            }
        }

        // 温度格式化（截断到整数，与旧项目一致）
        private void UpdateTemperatureValue(string key, float? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    string unit = temperatureUnitService.UseFahrenheit ? "°F" : "°C";
                    string displayValue = $"{Math.Truncate(value.Value)}{unit}";
                    hardwareControls[key].valueLabel.Content = displayValue;
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // 百分比格式化（截断到1位小数，与旧项目一致）
        private void UpdatePercentageValue(string key, float? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    float truncated = (float)(Math.Truncate(value.Value * 10) / 10);
                    hardwareControls[key].valueLabel.Content = $"{truncated:F1}%";
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // 功耗格式化（截断到1位小数，与旧项目一致）
        private void UpdatePowerValue(string key, float? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    float truncated = (float)(Math.Truncate(value.Value * 10) / 10);
                    hardwareControls[key].valueLabel.Content = $"{truncated:F1}W";
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // 风扇转速格式化（整数，与旧项目一致）
        private void UpdateFanSpeedValue(string key, float? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    hardwareControls[key].valueLabel.Content = $"{value.Value:F0}RPM";
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // 内存格式化（截断到1位小数，转换为GB，与旧项目一致）
        private void UpdateMemoryValue(string key, float? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    float gbValue = value.Value / 1024; // Convert MB to GB
                    float truncated = (float)(Math.Truncate(gbValue * 10) / 10);
                    hardwareControls[key].valueLabel.Content = $"{truncated:F1}GB";
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // 网速格式化（自动单位转换，截断到1位小数，与旧项目一致）
        private void UpdateSpeedValue(string key, double? value)
        {
            if (hardwareControls.ContainsKey(key))
            {
                if (value.HasValue)
                {
                    string displayValue;
                    if (value.Value >= 1024)
                    {
                        double mbValue = value.Value / 1024;
                        double truncated = Math.Truncate(mbValue * 10) / 10;
                        displayValue = $"{truncated:F1}MB/s";
                    }
                    else
                    {
                        double truncated = Math.Truncate(value.Value * 10) / 10;
                        displayValue = $"{truncated:F1}KB/s";
                    }
                    hardwareControls[key].valueLabel.Content = displayValue;
                }
                else
                {
                    hardwareControls[key].valueLabel.Content = "--";
                }
            }
        }

        // Event handlers
        private void ExitMenu_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.Application.Current.Shutdown();
        }

        private async void SwitchCN_Click(object sender, RoutedEventArgs e)
        {
            languageService.SetLanguage(N90.Shared.Language.ZH_CN);
            UpdateLanguageMenuState();

            // 实时通知Service语言变更
            var dataManager = HardwareDataManager.Instance;
            await dataManager.SendLanguageChanged();
        }

        private async void SwitchEN_Click(object sender, RoutedEventArgs e)
        {
            languageService.SetLanguage(N90.Shared.Language.EN_US);
            UpdateLanguageMenuState();

            // 实时通知Service语言变更
            var dataManager = HardwareDataManager.Instance;
            await dataManager.SendLanguageChanged();
        }

        private void StartupMenu_Click(object sender, RoutedEventArgs e)
        {
            if (StartupService.IsCompleteAutoStartSet())
            {
                var result = StartupService.RemoveCompleteAutoStart();
                if (!result)
                {
                    System.Windows.MessageBox.Show("移除开机自启失败，可能需要管理员权限。", "N90 Client",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                var result = StartupService.SetupCompleteAutoStart();
                if (!result)
                {
                    System.Windows.MessageBox.Show("设置开机自启失败，可能需要管理员权限。\n\n" +
                                  "请尝试以管理员身份运行程序，或手动运行 install-service.bat 脚本。",
                                  "N90 Client", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            UpdateStartupMenuState();
        }

        private async void CelsiusMenu_Click(object sender, RoutedEventArgs e)
        {
            temperatureUnitService.SwitchTemperatureUnit(TemperatureUnit.Celsius);
            UpdateTemperatureMenuState();

            // 实时通知Service温度单位变更
            var dataManager = HardwareDataManager.Instance;
            await dataManager.SendTemperatureUnitChanged();
        }

        private async void FahrenheitMenu_Click(object sender, RoutedEventArgs e)
        {
            temperatureUnitService.SwitchTemperatureUnit(TemperatureUnit.Fahrenheit);
            UpdateTemperatureMenuState();

            // 实时通知Service温度单位变更
            var dataManager = HardwareDataManager.Instance;
            await dataManager.SendTemperatureUnitChanged();
        }

        private async void CustomStringSave_Click(object sender, RoutedEventArgs e)
        {
            string customString = customStringTextBox.Text ?? "";

            if (pipeClient?.IsConnected == true)
            {
                var message = new IpcMessage
                {
                    Type = MessageType.CustomStringChanged,
                    Data = customString
                };
                await pipeClient.SendMessageAsync(message);
            }
        }

        // Helper methods
        private void ApplyLanguageSetting()
        {
            languageService.ApplyLanguageSetting();
            UpdateLanguageMenuState();
        }

        private void ApplyTemperatureSetting()
        {
            temperatureUnitService.ApplyTemperatureUnitSetting();
            UpdateTemperatureMenuState();
        }

        private void UpdateLanguageMenuState()
        {
            var currentLanguage = languageService.GetCurrentLanguage();
            cnMenuItem.IsChecked = currentLanguage == N90.Shared.Language.ZH_CN;
            enMenuItem.IsChecked = currentLanguage == N90.Shared.Language.EN_US;
        }

        private void UpdateTemperatureMenuState()
        {
            var currentUnit = temperatureUnitService.TemperatureUnit;
            celsiusMenuItem.IsChecked = currentUnit == TemperatureUnit.Celsius;
            fahrenheitMenuItem.IsChecked = currentUnit == TemperatureUnit.Fahrenheit;
        }

        private void UpdateStartupMenuState()
        {
            startupMenu.IsChecked = StartupService.IsCompleteAutoStartSet();
        }

        private void ShowWindow()
        {
            this.Show();
            this.WindowState = WindowState.Normal;
            this.ShowInTaskbar = true;
            this.Activate();
        }

        private void ExitApplication()
        {
            notifyIcon?.Dispose();

            // Always stop service when client exits
            StopServiceOnExit();

            System.Windows.Application.Current.Shutdown();
        }

        private bool IsWindowsServiceRunning()
        {
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "query \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null) return false;

                process.WaitForExit();

                if (process.ExitCode == 0)
                {
                    string output = process.StandardOutput.ReadToEnd();
                    return output.Contains("RUNNING");
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to check Windows service status: {ex.Message}");
                return false;
            }
        }

        private void StopServiceOnExit()
        {
            try
            {
                Console.WriteLine("Stopping all N90 services...");

                // 1. Stop Windows Service if it's running
                if (IsWindowsServiceRunning())
                {
                    Console.WriteLine("Stopping Windows Service...");
                    StopWindowsService();
                }

                // 2. Stop any console service processes
                var serviceProcesses = System.Diagnostics.Process.GetProcessesByName("N90.Service");

                foreach (var process in serviceProcesses)
                {
                    try
                    {
                        Console.WriteLine($"Stopping service process: {process.Id}");
                        process.CloseMainWindow();

                        // Wait a bit for graceful shutdown
                        if (!process.WaitForExit(3000))
                        {
                            Console.WriteLine($"Force killing service process: {process.Id}");
                            process.Kill();
                        }
                        else
                        {
                            Console.WriteLine($"Service process {process.Id} stopped gracefully");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to stop service process {process.Id}: {ex.Message}");
                    }
                }

                Console.WriteLine("All N90 services stopped.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop services: {ex.Message}");
            }
        }

        private void StopWindowsService()
        {
            try
            {
                // Try to stop Windows Service with current permissions
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "stop \"N90HardwareMonitorService\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        Console.WriteLine("Windows Service stopped successfully");
                    }
                    else
                    {
                        Console.WriteLine($"Failed to stop Windows Service (exit code: {process.ExitCode})");
                        string error = process.StandardError.ReadToEnd();
                        if (!string.IsNullOrEmpty(error))
                        {
                            Console.WriteLine($"Error: {error}");
                        }

                        // If permission denied, try to launch admin helper
                        if (error.Contains("拒绝访问") || error.Contains("Access is denied") || process.ExitCode == 5)
                        {
                            //Console.WriteLine("Attempting to stop service with elevated privileges...");
                            TryStopServiceWithElevation();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to stop Windows Service: {ex.Message}");
            }
        }

        private void TryStopServiceWithElevation()
        {
            try
            {
                // Create a batch script to stop the service
                var tempScript = Path.GetTempFileName() + ".bat";
                File.WriteAllText(tempScript, @"@echo off
sc stop ""N90HardwareMonitorService""
pause
del ""%~f0""");

                // Launch with elevated privileges
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempScript,
                    UseShellExecute = true,
                    Verb = "runas", // This triggers UAC
                    WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden
                };

                var process = System.Diagnostics.Process.Start(startInfo);
                Console.WriteLine("Launched elevated service stop script");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to launch elevated service stop: {ex.Message}");
            }
        }



        private async void SwitchLanguage(string languageCode)
        {
            languageService.SwitchLanguage(languageCode);
            UpdateContextMenuStates();
            UpdateLanguageDisplay();

            // 实时通知Service语言变更
            var dataManager = HardwareDataManager.Instance;
            await dataManager.SendLanguageChanged();
        }

        private void SwitchTemperatureUnit(bool useFahrenheit)
        {
            temperatureUnitService.UseFahrenheit = useFahrenheit;
            temperatureUnitService.SaveTemperatureUnitSetting();
            UpdateContextMenuStates();
        }

        private void ToggleStartup()
        {
            if (StartupService.IsCompleteAutoStartSet())
            {
                var result = StartupService.RemoveCompleteAutoStart();
                if (!result)
                {
                    System.Windows.MessageBox.Show("移除开机自启失败，可能需要管理员权限。", "N90 Client",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            else
            {
                var result = StartupService.SetupCompleteAutoStart();
                if (!result)
                {
                    System.Windows.MessageBox.Show("设置开机自启失败，可能需要管理员权限。\n\n" +
                                  "请尝试以管理员身份运行程序，或手动运行 install-service.bat 脚本。",
                                  "N90 Client", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            UpdateContextMenuStates();
        }

        private void UpdateContextMenuStates()
        {
            if (notifyIcon?.ContextMenuStrip == null) return;

            try
            {
                // Update language menu
                var currentLanguage = languageService.LoadLanguageSetting();
                languageMenuItems.chinese.Checked = currentLanguage == "zh-CN";
                languageMenuItems.english.Checked = currentLanguage == "en-US";

                // Update temperature menu
                temperatureMenuItems.celsius.Checked = !temperatureUnitService.UseFahrenheit;
                temperatureMenuItems.fahrenheit.Checked = temperatureUnitService.UseFahrenheit;

                // Update startup menu
                if (startupMenuItem != null)
                {
                    startupMenuItem.Checked = StartupService.IsCompleteAutoStartSet();
                }

                // Update menu text based on current language
                UpdateContextMenuText();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating context menu states: {ex.Message}");
            }
        }

        private void UpdateContextMenuText()
        {
            if (notifyIcon?.ContextMenuStrip == null) return;

            try
            {
                var items = notifyIcon.ContextMenuStrip.Items;

                // Update main menu items
                items[0].Text = LanguageService.GetString("Show");
                items[2].Text = LanguageService.GetString("Language");
                items[3].Text = LanguageService.GetString("TemperatureUnit");
                items[5].Text = LanguageService.GetString("StartWithWindows");
                items[7].Text = LanguageService.GetString("Exit");

                // Update language submenu
                languageMenuItems.chinese.Text = LanguageService.GetString("Chinese");
                languageMenuItems.english.Text = LanguageService.GetString("English");

                // Update temperature submenu
                temperatureMenuItems.celsius.Text = LanguageService.GetString("Celsius");
                temperatureMenuItems.fahrenheit.Text = LanguageService.GetString("Fahrenheit");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating context menu text: {ex.Message}");
            }
        }

        private void UpdateLanguageDisplay()
        {
            // Update all hardware control labels
            foreach (var kvp in hardwareControls)
            {
                var key = kvp.Key;
                var control = kvp.Value;

                // Find the parent panel and update the name label
                if (control.valueLabel.Parent is System.Windows.Controls.Panel panel)
                {
                    var nameLabel = panel.Children.OfType<System.Windows.Controls.Label>().FirstOrDefault();
                    if (nameLabel != null && nameLabel != control.valueLabel)
                    {
                        nameLabel.Content = LanguageService.GetString(key);
                    }
                }
            }

            // Update window title
            this.Title = LanguageService.GetString("WindowTitle");

            // Update context menu text
            UpdateContextMenuText();
        }

        private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // Hide to system tray instead of closing
                if (notifyIcon != null)
                {
                    e.Cancel = true;
                    this.Hide();
                    this.ShowInTaskbar = false;
                }
                else
                {
                    // If no NotifyIcon, allow normal close
                    System.Windows.Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during window closing: {ex.Message}");
            }
        }

        protected override void OnStateChanged(EventArgs e)
        {
            try
            {
                if (WindowState == WindowState.Minimized && notifyIcon != null)
                {
                    this.Hide();
                    this.ShowInTaskbar = false;
                }
                base.OnStateChanged(e);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during state change: {ex.Message}");
                base.OnStateChanged(e);
            }
        }

        #region Service Management (Integrated Launcher Functionality)

        private System.Diagnostics.Process? serviceProcess;

        private async Task EnsureServiceRunning()
        {
            try
            {
                // Check if Windows Service is already running
                if (!IsWindowsServiceRunning())
                {
                    // Start Service in console mode only if Windows Service is not running
                    await StartService();

                    // Wait a moment for service to initialize
                    await Task.Delay(2000);
                }
                else
                {
                    Console.WriteLine("Windows Service is already running, skipping console service startup.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to ensure service running: {ex.Message}");
                throw;
            }
        }

        private async Task StartService()
        {
            try
            {
                string serviceExePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "N90.Service.exe");
                if (!File.Exists(serviceExePath))
                {
                    throw new FileNotFoundException($"Service executable not found: {serviceExePath}");
                }

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = serviceExePath,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = false,
                    RedirectStandardError = false
                };

                serviceProcess = System.Diagnostics.Process.Start(startInfo);
                if (serviceProcess == null)
                {
                    throw new Exception("Failed to start service process");
                }

                // Wait a moment to ensure it started successfully
                await Task.Delay(1000);

                if (serviceProcess.HasExited)
                {
                    throw new Exception($"Service process exited immediately with code: {serviceProcess.ExitCode}");
                }

                Console.WriteLine("Service started successfully");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to start service: {ex.Message}", ex);
            }
        }

        #endregion
    }
}