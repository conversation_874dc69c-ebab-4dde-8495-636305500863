using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using N90.Client.Services;
using N90.Client.Views.Components;
using N90.Shared;
using N90.Shared.Services;
using N90.Client.Models;
using N90.Shared.Communication;
using System.Text.Json;
using System.IO;

namespace N90.Client.Views.Pages
{
    /// <summary>
    /// N90Page.xaml 的交互逻辑
    /// </summary>
    public partial class N90Page : Page, INotifyPropertyChanged
    {
        private HardwareData _hardwareData;
        private readonly TemperatureUnitService _temperatureUnitService;
        private readonly HardwareMonitorService _monitorService;
        private readonly MonitorSettings _settings;
        private Dictionary<string, string> _sensorValueMap;
        private NamedPipeClient _pipeClient;
        private bool _useServiceData = false; // 标志是否使用Service数据

        public event PropertyChangedEventHandler? PropertyChanged;

        public N90Page()
        {
            InitializeComponent();
            _temperatureUnitService = new TemperatureUnitService();
            _monitorService = HardwareMonitorService.Instance; // 使用单例
            _settings = MonitorSettings.Load();
            _sensorValueMap = new Dictionary<string, string>();

            InitializeHardwareData();
            DataContext = this;

            // 初始化IPC通信
            InitializeCommunication();

            // 初始化N90ControlPanel
            Loaded += (sender, e) =>
            {
                try
                {
                    if (N90ControlPanel != null)
                    {
                        N90ControlPanel.SetBrightness(50); // 默认50%亮度
                        N90ControlPanel.SetLightSwitch(true); // 默认开启
                    }

                    // 文件监视器已在InitializeCommunication中启动
                    // 如果没有连接到Service，同时使用本地传感器数据作为备用
                    if (!_useServiceData)
                    {
                        Console.WriteLine($"N90Page loaded: Using local sensor data as fallback. Hardware service initialized: {_monitorService.IsInitialized}, sensors discovered: {_monitorService.SensorsDiscovered}");
                        StartSensorUpdates();
                    }
                    else
                    {
                        Console.WriteLine("N90Page loaded: Using Service data via IPC + file watcher");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"初始化N90ControlPanel时出错: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// 初始化IPC通信
        /// </summary>
        private async void InitializeCommunication()
        {
            try
            {
                Console.WriteLine("N90Page: Initializing IPC communication...");
                _pipeClient = new NamedPipeClient("N90_Pipe");
                _pipeClient.MessageReceived += OnMessageReceived;

                //Console.WriteLine("N90Page: Attempting to connect to Service...");
                // 尝试连接到Service，增加超时时间
                bool connected = await _pipeClient.ConnectAsync(10000); // 10秒超时
                if (connected)
                {
                    _useServiceData = true;
                    Console.WriteLine("N90Page: ✅ Connected to Service, will use Service data");
                }
                else
                {
                    _useServiceData = false;
                    Console.WriteLine("N90Page: ❌ Failed to connect to Service, will use file watcher as primary mechanism");
                }

                // 无论IPC是否成功，都启动文件监视器
                Console.WriteLine("N90Page: Starting data file watcher...");
                StartDataFileWatcher();

                // 立即尝试加载一次数据
                Console.WriteLine("N90Page: Loading initial data from JSON...");
                _ = Task.Run(async () => await LoadDataFromJson());
            }
            catch (TimeoutException ex)
            {
                _useServiceData = false;
                Console.WriteLine($"N90Page: IPC connection timeout: {ex.Message}");
                Console.WriteLine("N90Page: Will rely on file watcher for data updates");

                // 确保文件监视器启动
                StartDataFileWatcher();
                _ = Task.Run(async () => await LoadDataFromJson());
            }
            catch (Exception ex)
            {
                _useServiceData = false;
                Console.WriteLine($"N90Page: IPC initialization failed: {ex.Message}");
                Console.WriteLine($"N90Page: Stack trace: {ex.StackTrace}");

                // 确保文件监视器启动
                StartDataFileWatcher();
                _ = Task.Run(async () => await LoadDataFromJson());
            }
        }

        /// <summary>
        /// 处理来自Service的消息
        /// </summary>
        private void OnMessageReceived(IpcMessage message)
        {
            try
            {
                Console.WriteLine($"N90Page: Received message type: {message.Type} at {DateTime.Now:HH:mm:ss.fff}");

                if (message.Type == MessageType.DataUpdated)
                {
                    Console.WriteLine($"N90Page: DataUpdated message received, loading data from JSON...");
                    // Service通知数据已更新，读取data.json文件
                    _ = Task.Run(async () => await LoadDataFromJson());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"N90Page: Error handling message: {ex.Message}");
            }
        }

        /// <summary>
        /// 从data.json文件加载数据
        /// </summary>
        private async Task LoadDataFromJson()
        {
            try
            {
                string dataFilePath = GetDataFilePath();
                Console.WriteLine($"N90Page: Looking for data file at: {dataFilePath}");

                if (File.Exists(dataFilePath))
                {
                    var fileInfo = new FileInfo(dataFilePath);
                    Console.WriteLine($"N90Page: File exists, size: {fileInfo.Length} bytes, last modified: {fileInfo.LastWriteTime:HH:mm:ss.fff}");

                    string json = await File.ReadAllTextAsync(dataFilePath);
                    Console.WriteLine($"N90Page: Read {json.Length} characters from JSON file");

                    var hardwareData = JsonSerializer.Deserialize<HardwareData>(json);

                    if (hardwareData != null)
                    {
                        Console.WriteLine($"N90Page: Successfully deserialized data - CPU Temp: {hardwareData.CPUTemperature?.value}, Time: {hardwareData.Time?.value}");

                        // 在UI线程上更新数据
                        await Dispatcher.InvokeAsync(() =>
                        {
                            UpdateHardwareData(hardwareData);
                            Console.WriteLine($"N90Page: Data updated in UI at {DateTime.Now:HH:mm:ss.fff}");
                        });
                    }
                    else
                    {
                        Console.WriteLine("N90Page: Deserialized data is null");
                    }
                }
                else
                {
                    Console.WriteLine($"N90Page: Data file does not exist at {dataFilePath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"N90Page: Error loading data from JSON: {ex.Message}");
                Console.WriteLine($"N90Page: Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 启动数据文件监视器（备用机制）
        /// </summary>
        private void StartDataFileWatcher()
        {
            // 启动一个定时器，每3秒检查一次data.json文件
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(3);
            timer.Tick += async (sender, e) =>
            {
                try
                {
                    string dataFilePath = GetDataFilePath();
                    if (File.Exists(dataFilePath))
                    {
                        var fileInfo = new FileInfo(dataFilePath);
                        // 检查文件是否在最近5秒内被修改过
                        if (DateTime.Now - fileInfo.LastWriteTime < TimeSpan.FromSeconds(5))
                        {
                            Console.WriteLine($"N90Page: Data file watcher detected file change at {DateTime.Now:HH:mm:ss.fff}");
                            await LoadDataFromJson();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"N90Page: Data file watcher error: {ex.Message}");
                }
            };
            timer.Start();
            Console.WriteLine("N90Page: Data file watcher started (backup mechanism)");
        }

        /// <summary>
        /// 获取data.json文件路径
        /// </summary>
        private string GetDataFilePath()
        {
#if DEBUG
            // Debug模式：使用项目根目录
            var projectRoot = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", ".."));
            return Path.Combine(projectRoot, "data.json");
#else
            // Release模式：使用可执行文件同目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "data.json");
#endif
        }

        private void InitializeHardwareData()
        {
            // 从注册表加载 CustomString 的值
            string savedCustomString = LoadCustomStringFromRegistry();

            _hardwareData = new HardwareData
            {
                Date = new Date { value = DateTime.Now.ToString("yyyy-MM-dd"), show = true },
                Time = new Time { value = DateTime.Now.ToString("HH:mm:ss"), show = true },
                Weekday = new Weekday { value = DateTime.Now.ToString("dddd"), show = true },

                CPUTemperature = new CPUTemperature { value = 0, show = true },
                CPUUsage = new CPUUsage { value = 0.0f, show = true },
                CPUPower = new CPUPower { value = 0, show = true },
                CPUFanSpeed = new CPUFanSpeed { value = 0, show = true },
                CPUModel = new CPUModel { value = null, show = true },

                GPUTemperature = new GPUTemperature { value = 0, show = true },
                GPUMemoryUsage = new GPUMemoryUsage { value = 0.0f, show = true },
                GPUPower = new GPUPower { value = 0, show = true },
                GPUModel = new GPUModel { value = null, show = true },

                RAMUsage = new RAMUsage { value = 0.0f, show = true },
                AvailableRAM = new AvailableRAM { value = 0, show = true }, // MB

                CaseFan1Speed = new CaseFan1Speed { value = 0, show = true },
                CaseFan2Speed = new CaseFan2Speed { value = 0, show = true },

                HDDTemperature = new HDDTemperature { value = 0, show = true },
                HDDUsage = new HDDUsage { value = 0.0f, show = true },

                UploadSpeed = new UploadSpeed { value = 0.0, show = true },
                DownloadSpeed = new DownloadSpeed { value = 0.0, show = true },

                CustomString = new CustomString { value = savedCustomString, show = true }
            };
        }

        private async void StartSensorUpdates()
        {
            while (true)
            {
                try
                {
                    await UpdateSelectedSensors();
                    await Task.Delay(2000); // 每2秒更新一次
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"传感器更新错误: {ex.Message}");
                    await Task.Delay(5000); // 出错时等待5秒后重试
                }
            }
        }

        private async Task UpdateSelectedSensors()
        {
            if (_settings.SelectedSensors == null || _settings.SelectedSensors.Count == 0)
                return;

            var values = _monitorService.GetSelectedValues(_settings.SelectedSensors);
            
            // 更新传感器值映射
            foreach (var kvp in values)
            {
                _sensorValueMap[kvp.Key] = kvp.Value.ToString();
            }

            // 更新显示
            await Dispatcher.InvokeAsync(() =>
            {
                UpdateDisplayValues(values);
                
                // 发送选中的数据到HID
                _ = SendSelectedDataToHID(values);
            });
        }

        private void UpdateDisplayValues(Dictionary<string, float> values)
        {
            // 根据传感器key更新对应显示值
            foreach (var kvp in values)
            {
                var key = kvp.Key.ToLower();
                var value = kvp.Value;

                if (key.Contains("temperature") && key.Contains("cpu"))
                {
                    _hardwareData.CPUTemperature.value = value;
                    OnPropertyChanged(nameof(CPUTemperatureValue));
                }
                else if (key.Contains("temperature") && key.Contains("gpu"))
                {
                    _hardwareData.GPUTemperature.value = value;
                    OnPropertyChanged(nameof(GPUTemperatureValue));
                }
                else if (key.Contains("usage") && key.Contains("cpu"))
                {
                    _hardwareData.CPUUsage.value = value;
                    OnPropertyChanged(nameof(CPUUsageValue));
                }
                else if (key.Contains("usage") && key.Contains("memory"))
                {
                    _hardwareData.RAMUsage.value = value;
                    OnPropertyChanged(nameof(RAMUsageValue));
                }
                else if (key.Contains("usage") && key.Contains("gpu"))
                {
                    _hardwareData.GPUMemoryUsage.value = value;
                    OnPropertyChanged(nameof(GPUMemoryUsageValue));
                }
                else if (key.Contains("power") && key.Contains("cpu"))
                {
                    _hardwareData.CPUPower.value = value;
                    OnPropertyChanged(nameof(CPUPowerValue));
                }
                else if (key.Contains("power") && key.Contains("gpu"))
                {
                    _hardwareData.GPUPower.value = value;
                    OnPropertyChanged(nameof(GPUPowerValue));
                }
                else if (key.Contains("fan") && key.Contains("cpu"))
                {
                    _hardwareData.CPUFanSpeed.value = value;
                    OnPropertyChanged(nameof(CPUFanSpeedValue));
                }
                else if (key.Contains("fan") && key.Contains("case"))
                {
                    if (key.Contains("1"))
                    {
                        _hardwareData.CaseFan1Speed.value = value;
                        OnPropertyChanged(nameof(CaseFan1SpeedValue));
                    }
                    else if (key.Contains("2"))
                    {
                        _hardwareData.CaseFan2Speed.value = value;
                        OnPropertyChanged(nameof(CaseFan2SpeedValue));
                    }
                }
            }
        }

        private async Task SendSelectedDataToHID(Dictionary<string, float> values)
        {
            try
            {
                // 只发送选中的数据到HID
                var dataToSend = new Dictionary<string, object>();
                foreach (var kvp in values)
                {
                    dataToSend[kvp.Key] = kvp.Value;
                }

                await HardwareDataManager.Instance.SendSelectedSensorData(dataToSend);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送HID数据错误: {ex.Message}");
            }
        }

        public void UpdateHardwareData(HardwareData newData)
        {
            if (newData == null) return;

            // 保存当前的 CustomString 值（来自注册表）
            string currentCustomString = _hardwareData?.CustomString?.value ?? "";

            _hardwareData = newData;

            // 恢复 CustomString 值，优先使用注册表中的值
            if (_hardwareData.CustomString != null)
            {
                // 如果当前有值（来自注册表），保持不变
                // 如果当前没有值，但新数据有值，则使用新数据的值
                if (!string.IsNullOrEmpty(currentCustomString))
                {
                    _hardwareData.CustomString.value = currentCustomString;
                }
                else if (string.IsNullOrEmpty(_hardwareData.CustomString.value))
                {
                    // 如果都没有值，从注册表重新加载
                    _hardwareData.CustomString.value = LoadCustomStringFromRegistry();
                }
            }

            // 通知所有数据值变化
            OnPropertyChanged(nameof(DateValue));
            OnPropertyChanged(nameof(TimeValue));
            OnPropertyChanged(nameof(WeekdayValue));
            OnPropertyChanged(nameof(CPUModelValue));
            OnPropertyChanged(nameof(GPUModelValue));
            OnPropertyChanged(nameof(CustomStringValue));
            OnPropertyChanged(nameof(CPUTemperatureValue));
            OnPropertyChanged(nameof(CPUUsageValue));
            OnPropertyChanged(nameof(CPUPowerValue));
            OnPropertyChanged(nameof(CPUFanSpeedValue));
            OnPropertyChanged(nameof(GPUTemperatureValue));
            OnPropertyChanged(nameof(GPUMemoryUsageValue));
            OnPropertyChanged(nameof(GPUPowerValue));
            OnPropertyChanged(nameof(RAMUsageValue));
            OnPropertyChanged(nameof(UsedRAMValue));
            OnPropertyChanged(nameof(DriveTemperatureValue));
            OnPropertyChanged(nameof(DriveUsageValue));
            OnPropertyChanged(nameof(CaseFan1SpeedValue));
            OnPropertyChanged(nameof(CaseFan2SpeedValue));
            OnPropertyChanged(nameof(NetworkUploadSpeedValue));
            OnPropertyChanged(nameof(NetworkDownloadSpeedValue));

            // 通知显示状态变化
            OnPropertyChanged(nameof(DateShowStatus));
            OnPropertyChanged(nameof(TimeShowStatus));
            OnPropertyChanged(nameof(WeekdayShowStatus));
            OnPropertyChanged(nameof(CPUModelShowStatus));
            OnPropertyChanged(nameof(GPUModelShowStatus));
            OnPropertyChanged(nameof(CustomStringShowStatus));
            OnPropertyChanged(nameof(CPUTemperatureShowStatus));
            OnPropertyChanged(nameof(CPUUsageShowStatus));
            OnPropertyChanged(nameof(CPUPowerShowStatus));
            OnPropertyChanged(nameof(CPUFanSpeedShowStatus));
            OnPropertyChanged(nameof(GPUTemperatureShowStatus));
            OnPropertyChanged(nameof(GPUMemoryUsageShowStatus));
            OnPropertyChanged(nameof(GPUPowerShowStatus));
            OnPropertyChanged(nameof(RAMUsageShowStatus));
            OnPropertyChanged(nameof(UsedRAMShowStatus));
            OnPropertyChanged(nameof(DriveTemperatureShowStatus));
            OnPropertyChanged(nameof(DriveUsageShowStatus));
            OnPropertyChanged(nameof(CaseFan1SpeedShowStatus));
            OnPropertyChanged(nameof(CaseFan2SpeedShowStatus));
            OnPropertyChanged(nameof(NetworkUploadSpeedShowStatus));
            OnPropertyChanged(nameof(NetworkDownloadSpeedShowStatus));
        }

        // 数据绑定属性
        public string DateValue => _hardwareData?.Date?.value ?? "--";
        public string TimeValue => _hardwareData?.Time?.value ?? "--";
        public string WeekdayValue => _hardwareData?.Weekday?.value ?? "--";
        public string CPUModelValue => _hardwareData?.CPUModel?.value ?? "--";
        public string GPUModelValue => _hardwareData?.GPUModel?.value ?? "--";

        public string CustomStringValue
        {
            get
            {
                // 优先从 _hardwareData 获取，如果没有则从注册表获取
                string value = _hardwareData?.CustomString?.value;
                if (string.IsNullOrEmpty(value))
                {
                    value = LoadCustomStringFromRegistry();
                    // 如果从注册表获取到值，同步到 _hardwareData
                    if (_hardwareData?.CustomString != null && !string.IsNullOrEmpty(value))
                    {
                        _hardwareData.CustomString.value = value;
                    }
                }
                return value ?? "";
            }
            set
            {
                if (_hardwareData?.CustomString != null && _hardwareData.CustomString.value != value)
                {
                    _hardwareData.CustomString.value = value;
                    OnPropertyChanged();

                    // 保存到注册表
                    SaveCustomStringToRegistry(value);

                    // 异步发送更新到服务
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await HardwareDataManager.Instance.SendCustomStringUpdate(value);
                        }
                        catch (Exception ex)
                        {
                            // 可以在这里记录日志，但不显示消息框
                            Console.WriteLine($"Failed to update custom string: {ex.Message}");
                        }
                    });
                }
            }
        }

        // 温度格式化
        public string CPUTemperatureValue => FormatTemperature(_hardwareData?.CPUTemperature?.value);
        public string GPUTemperatureValue => FormatTemperature(_hardwareData?.GPUTemperature?.value);
        public string DriveTemperatureValue => FormatTemperature(_hardwareData?.HDDTemperature?.value);

        // 百分比格式化
        public string CPUUsageValue => FormatPercentage(_hardwareData?.CPUUsage?.value);
        public string GPUMemoryUsageValue => FormatPercentage(_hardwareData?.GPUMemoryUsage?.value);
        public string RAMUsageValue => FormatPercentage(_hardwareData?.RAMUsage?.value);
        public string DriveUsageValue => FormatPercentage(_hardwareData?.HDDUsage?.value);

        // 功耗格式化
        public string CPUPowerValue => FormatPower(_hardwareData?.CPUPower?.value);
        public string GPUPowerValue => FormatPower(_hardwareData?.GPUPower?.value);

        // 风扇转速格式化
        public string CPUFanSpeedValue => FormatFanSpeed(_hardwareData?.CPUFanSpeed?.value);
        public string CaseFan1SpeedValue => FormatFanSpeed(_hardwareData?.CaseFan1Speed?.value);
        public string CaseFan2SpeedValue => FormatFanSpeed(_hardwareData?.CaseFan2Speed?.value);

        // 内存格式化
        public string UsedRAMValue => FormatMemory(_hardwareData?.AvailableRAM?.value);

        // 网速格式化
        public string NetworkUploadSpeedValue => FormatSpeed(_hardwareData?.UploadSpeed?.value);
        public string NetworkDownloadSpeedValue => FormatSpeed(_hardwareData?.DownloadSpeed?.value);

        // 显示状态属性（用于控制状态指示器的显示/隐藏）
        public Visibility DateShowStatus => (_hardwareData?.Date?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility TimeShowStatus => (_hardwareData?.Time?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility WeekdayShowStatus => (_hardwareData?.Weekday?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CPUModelShowStatus => (_hardwareData?.CPUModel?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility GPUModelShowStatus => (_hardwareData?.GPUModel?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CustomStringShowStatus => (_hardwareData?.CustomString?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        public Visibility CPUTemperatureShowStatus => (_hardwareData?.CPUTemperature?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CPUUsageShowStatus => (_hardwareData?.CPUUsage?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CPUPowerShowStatus => (_hardwareData?.CPUPower?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CPUFanSpeedShowStatus => (_hardwareData?.CPUFanSpeed?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        public Visibility GPUTemperatureShowStatus => (_hardwareData?.GPUTemperature?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility GPUMemoryUsageShowStatus => (_hardwareData?.GPUMemoryUsage?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility GPUPowerShowStatus => (_hardwareData?.GPUPower?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        public Visibility RAMUsageShowStatus => (_hardwareData?.RAMUsage?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility UsedRAMShowStatus => (_hardwareData?.AvailableRAM?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility DriveTemperatureShowStatus => (_hardwareData?.HDDTemperature?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility DriveUsageShowStatus => (_hardwareData?.HDDUsage?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        public Visibility CaseFan1SpeedShowStatus => (_hardwareData?.CaseFan1Speed?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility CaseFan2SpeedShowStatus => (_hardwareData?.CaseFan2Speed?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        public Visibility NetworkUploadSpeedShowStatus => (_hardwareData?.UploadSpeed?.show == true) ? Visibility.Visible : Visibility.Collapsed;
        public Visibility NetworkDownloadSpeedShowStatus => (_hardwareData?.DownloadSpeed?.show == true) ? Visibility.Visible : Visibility.Collapsed;

        // 格式化方法
        private string FormatTemperature(float? value)
        {
            if (!value.HasValue) return "0°C";

            // 每次格式化时重新读取温度单位设置，确保获取最新值
            _temperatureUnitService.ApplyTemperatureUnitSetting();
            string unit = _temperatureUnitService.UseFahrenheit ? "°F" : "°C";
            return $"{Math.Truncate(value.Value)}{unit}";
        }

        private string FormatPercentage(float? value)
        {
            if (!value.HasValue) return "0.0%";
            float truncated = (float)(Math.Truncate(value.Value * 10) / 10);
            return $"{truncated:F1}%";
        }

        private string FormatPower(float? value)
        {
            if (!value.HasValue) return "0.0W";
            float truncated = (float)(Math.Truncate(value.Value * 10) / 10);
            return $"{truncated:F1}W";
        }

        private string FormatFanSpeed(float? value)
        {
            if (!value.HasValue) return "0 RPM";
            return $"{value.Value:F0} RPM";
        }

        private string FormatMemory(float? value)
        {
            if (!value.HasValue) return "0.0 GB";
            float truncated = (float)(Math.Truncate(value.Value * 10) / 10);
            return $"{truncated:F1} GB";
        }

        private string FormatSpeed(double? value)
        {
            if (!value.HasValue) return "0.0 KB/s";

            if (value.Value >= 1024)
            {
                double mbValue = value.Value / 1024;
                double truncated = Math.Truncate(mbValue * 10) / 10;
                return $"{truncated:F1} MB/s";
            }
            else
            {
                double truncated = Math.Truncate(value.Value * 10) / 10;
                return $"{truncated:F1} KB/s";
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Custom String按钮点击事件处理
        /// </summary>
        private void CustomStringButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InputDialog.Show(
                    title: "Edit Custom String",
                    defaultText: CustomStringValue,
                    callback: (isConfirmed, inputText) =>
                    {
                        if (isConfirmed && !string.IsNullOrWhiteSpace(inputText))
                        {
                            CustomStringValue = inputText;
                        }
                    });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"Dialog loading failed: {ex.Message}\n\nError logged to N90_Error.log in Documents folder",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// N90ControlPanel灯光开关状态改变事件处理
        /// </summary>
        private void N90ControlPanel_LightSwitchChanged(object sender, Components.LightSwitchEventArgs e)
        {
            Console.WriteLine($"Running Light switched {(e.IsOn ? "ON" : "OFF")}");
            // 这里可以添加实际的灯光控制逻辑
        }

        /// <summary>
        /// N90ControlPanel亮度改变事件处理
        /// </summary>
        private void N90ControlPanel_BrightnessChanged(object sender, Components.BrightnessChangedEventArgs e)
        {
            Console.WriteLine($"亮度已更改为: {e.Brightness}%");

            // 异步发送亮度更新到服务（不阻塞UI线程）
            _ = Task.Run(async () =>
            {
                try
                {
                    await HardwareDataManager.Instance.SendBrightnessUpdate(e.Brightness);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发送亮度更新失败: {ex.Message}");
                }
            });
        }







        // 状态指示器点击事件处理方法（旧版本，保留兼容性）
        private void StatusIndicator_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is string dataType)
            {
                // 获取当前状态并切换
                bool currentState = HardwareDataManager.Instance.GetUserShowPreference(dataType);
                bool newState = !currentState;

                // 设置新的用户偏好
                HardwareDataManager.Instance.SetUserShowPreference(dataType, newState);

                Console.WriteLine($"Toggled {dataType} show state to: {newState}");
            }
        }

        // 新的状态指示器组件事件处理方法
        private void StatusIndicator_StatusChanged(object sender, StatusChangedEventArgs e)
        {
            // 将N90Page的数据类型名称映射到MainWindow的命名约定
            string mappedDataType = MapDataTypeToMainWindowConvention(e.DataType);

            // 设置用户偏好
            HardwareDataManager.Instance.SetUserShowPreference(mappedDataType, e.IsChecked);

            Console.WriteLine($"StatusIndicator: {e.DataType} -> {mappedDataType} changed to: {e.IsChecked}");
        }

        /// <summary>
        /// 将N90Page的数据类型名称映射到MainWindow的命名约定
        /// </summary>
        /// <param name="n90PageDataType">N90Page使用的数据类型名称</param>
        /// <returns>MainWindow使用的数据类型名称</returns>
        private string MapDataTypeToMainWindowConvention(string n90PageDataType)
        {
            return n90PageDataType switch
            {
                // 直接映射的数据类型
                "Date" => "Date",
                "Time" => "Time",
                "Weekday" => "Weekday",
                "CPUModel" => "CPUModel",
                "GPUModel" => "GPUModel",
                "CustomString" => "CustomString",
                "CPUTemperature" => "CPUTemperature",
                "CPUUsage" => "CPUUsage",
                "CPUPower" => "CPUPower",
                "CPUFanSpeed" => "CPUFanSpeed",
                "GPUTemperature" => "GPUTemperature",
                "GPUMemoryUsage" => "GPUMemoryUsage",
                "GPUPower" => "GPUPower",
                "RAMUsage" => "RAMUsage",
                "CaseFan1Speed" => "CaseFan1Speed",
                "CaseFan2Speed" => "CaseFan2Speed",

                // 需要映射的数据类型
                "UsedRAM" => "AvailableRAM",
                "DriveTemperature" => "HDDTemperature",
                "DriveUsage" => "HDDUsage",
                "NetworkUploadSpeed" => "UploadSpeed",
                "NetworkDownloadSpeed" => "DownloadSpeed",

                // 默认返回原名称
                _ => n90PageDataType
            };
        }

        /// <summary>
        /// 更新所有StatusIndicator组件的状态
        /// </summary>
        private void UpdateStatusIndicators()
        {
            // 这个方法会在数据更新时被调用，用于同步StatusIndicator的IsChecked状态
            // 由于我们现在使用用户偏好管理，StatusIndicator会通过数据绑定自动更新
        }

        private void TabButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button clickedButton)
            {
                // 隐藏所有标签页内容
                BasicParametersContent.Visibility = Visibility.Collapsed;
                ThemeContent.Visibility = Visibility.Collapsed;

                if (clickedButton == BasicParametersTab)
                {
                    // 设置样式
                    BasicParametersTab.Style = (Style)FindResource("LeftActiveTabButtonStyle");
                    ThemeTab.Style = (Style)FindResource("RightInactiveTabButtonStyle");

                    // 显示内容
                    BasicParametersContent.Visibility = Visibility.Visible;
                }
                else if (clickedButton == ThemeTab)
                {
                    // 设置样式
                    BasicParametersTab.Style = (Style)FindResource("LeftInactiveTabButtonStyle");
                    ThemeTab.Style = (Style)FindResource("RightActiveTabButtonStyle");

                    // 显示内容
                    ThemeContent.Visibility = Visibility.Visible;
                }
            }
        }

        /// <summary>
        /// 从注册表加载 CustomString 的值
        /// </summary>
        /// <returns>保存的 CustomString 值，如果没有则返回空字符串</returns>
        private string LoadCustomStringFromRegistry()
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.LocalMachine.CreateSubKey("SOFTWARE\\N90\\HardwareDisplay");
                return registryKey.GetValue("CustomStringValue", "") as string ?? "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading CustomString from registry: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 保存 CustomString 的值到注册表
        /// </summary>
        /// <param name="value">要保存的 CustomString 值</param>
        private void SaveCustomStringToRegistry(string value)
        {
            try
            {
                using var registryKey = Microsoft.Win32.Registry.LocalMachine.CreateSubKey("SOFTWARE\\N90\\HardwareDisplay");
                registryKey.SetValue("CustomStringValue", value ?? "");
                Console.WriteLine($"CustomString saved to registry: \"{value}\"");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving CustomString to registry: {ex.Message}");
            }
        }
    }


}
