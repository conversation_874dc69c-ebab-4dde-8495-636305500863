// MainWindow.xaml.cs 片段，实现语言切换和菜单刷新
using System.Windows;

namespace N90.Client
{
    public partial class MainWindow : Window
    {
        // Language switching methods moved to MainWindow.xaml.cs

        private void UpdateMenuItemText()
        {
            // Menu text updates moved to MainWindow.xaml.cs
        }

        public void UpdateLanguage()
        {
            // 假设 hardwareControls 为 Dictionary<string, (Label valueLabel, CheckBox showCheckBox)>
            foreach (var kvp in hardwareControls)
            {
                string resourceKey = kvp.Key;
                var (valueLabel, showCheckBox) = kvp.Value;
                if (valueLabel == null) continue;
                valueLabel.Content = FindResource(resourceKey) as string;
            }
            if (customStringNameLabel != null)
            {
                customStringNameLabel.Content = FindResource("CustomString") as string;
            }
        }
    }
}
