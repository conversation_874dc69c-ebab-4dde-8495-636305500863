using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using N90.Client.Services;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// N90ControlPanel.xaml 的交互逻辑
    /// N90控制面板组件，包含标题、图片和亮度控制功能
    /// </summary>
    public partial class N90ControlPanel : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 标题文本
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(
                nameof(Title),
                typeof(string),
                typeof(N90ControlPanel),
                new PropertyMetadata("N90"));

        /// <summary>
        /// 图片源路径
        /// </summary>
        public static readonly DependencyProperty ImageSourceProperty =
            DependencyProperty.Register(
                nameof(ImageSource),
                typeof(string),
                typeof(N90ControlPanel),
                new PropertyMetadata("/Resources/Images/img4.png"));

        /// <summary>
        /// 图标源路径
        /// </summary>
        public static readonly DependencyProperty IconSourceProperty =
            DependencyProperty.Register(
                nameof(IconSource),
                typeof(string),
                typeof(N90ControlPanel),
                new PropertyMetadata("/Resources/Images/img1.png"));

        /// <summary>
        /// 控制标题文本
        /// </summary>
        public static readonly DependencyProperty ControlTitleProperty =
            DependencyProperty.Register(
                nameof(ControlTitle),
                typeof(string),
                typeof(N90ControlPanel),
                new PropertyMetadata("Running Light Switch Brightness"));

        /// <summary>
        /// 亮度值
        /// </summary>
        public static readonly DependencyProperty BrightnessValueProperty =
            DependencyProperty.Register(
                nameof(BrightnessValue),
                typeof(double),
                typeof(N90ControlPanel),
                new PropertyMetadata(50.0, OnBrightnessValueChanged));

        #endregion

        #region 事件

        /// <summary>
        /// 灯光开关状态改变事件
        /// </summary>
        public event EventHandler<LightSwitchEventArgs>? LightSwitchChanged;

        /// <summary>
        /// 亮度值改变事件
        /// </summary>
        public event EventHandler<BrightnessChangedEventArgs>? BrightnessChanged;

        #endregion

        #region 属性

        /// <summary>
        /// 标题文本
        /// </summary>
        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        /// <summary>
        /// 图片源路径
        /// </summary>
        public string ImageSource
        {
            get => (string)GetValue(ImageSourceProperty);
            set => SetValue(ImageSourceProperty, value);
        }

        /// <summary>
        /// 图标源路径
        /// </summary>
        public string IconSource
        {
            get => (string)GetValue(IconSourceProperty);
            set => SetValue(IconSourceProperty, value);
        }

        /// <summary>
        /// 控制标题文本
        /// </summary>
        public string ControlTitle
        {
            get => (string)GetValue(ControlTitleProperty);
            set => SetValue(ControlTitleProperty, value);
        }

        /// <summary>
        /// 亮度值
        /// </summary>
        public double BrightnessValue
        {
            get => (double)GetValue(BrightnessValueProperty);
            set => SetValue(BrightnessValueProperty, value);
        }

        /// <summary>
        /// 当前灯光状态
        /// </summary>
        public bool IsLightOn { get; private set; } = true;

        #endregion

        public N90ControlPanel()
        {
            InitializeComponent();
            
            // 初始化亮度滑块
            Loaded += (sender, e) =>
            {
                try
                {
                    if (BrightnessSlider != null)
                    {
                        UpdateSliderProgress(BrightnessValue);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"初始化N90ControlPanel时出错: {ex.Message}");
                }
            };
        }

        #region 事件处理

        /// <summary>
        /// 亮度值依赖属性改变时的回调
        /// </summary>
        private static void OnBrightnessValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is N90ControlPanel panel)
            {
                panel.UpdateSliderProgress((double)e.NewValue);
            }
        }

        /// <summary>
        /// 灯光ON按钮点击事件处理
        /// </summary>
        private void LightOnButton_Click(object sender, RoutedEventArgs e)
        {
            SetLightState(true);
            LightSwitchChanged?.Invoke(this, new LightSwitchEventArgs(true));
            Console.WriteLine("Running Light switched ON");
        }

        /// <summary>
        /// 灯光OFF按钮点击事件处理
        /// </summary>
        private void LightOffButton_Click(object sender, RoutedEventArgs e)
        {
            SetLightState(false);
            LightSwitchChanged?.Invoke(this, new LightSwitchEventArgs(false));
            Console.WriteLine("Running Light switched OFF");
        }

        /// <summary>
        /// 亮度滑块值改变事件处理
        /// </summary>
        private void BrightnessSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (sender is Slider slider)
            {
                try
                {
                    // 确保在UI线程上执行
                    if (!Dispatcher.CheckAccess())
                    {
                        Dispatcher.Invoke(() => BrightnessSlider_ValueChanged(sender, e));
                        return;
                    }

                    // 更新进度条宽度
                    UpdateSliderProgress(slider.Value);
                    
                    // 更新依赖属性
                    BrightnessValue = slider.Value;

                    Console.WriteLine($"亮度已更改为: {slider.Value:F0}%");
                    
                    // 触发亮度改变事件
                    BrightnessChanged?.Invoke(this, new BrightnessChangedEventArgs((int)slider.Value));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"亮度滑块值改变时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 滑块预览鼠标按下事件 - 实现点击跳转功能
        /// </summary>
        private void BrightnessSlider_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Slider slider)
            {
                // 获取鼠标在滑块中的位置
                var position = e.GetPosition(slider);
                var percentage = Math.Max(0, Math.Min(1, position.X / slider.ActualWidth));
                var newValue = slider.Minimum + (slider.Maximum - slider.Minimum) * percentage;

                // 立即设置滑块值
                slider.Value = newValue;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置灯光状态
        /// </summary>
        private void SetLightState(bool isOn)
        {
            IsLightOn = isOn;
            
            if (isOn)
            {
                // 设置ON按钮为选中状态：灰色底绿色字
                LightOnButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x66, 0x66, 0x66));
                LightOnButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x97, 0xD7, 0x00));

                // 设置OFF按钮为未选中状态：黑色底白色字
                LightOffButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
                LightOffButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
            }
            else
            {
                // 设置OFF按钮为选中状态：灰色底绿色字
                LightOffButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x66, 0x66, 0x66));
                LightOffButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x97, 0xD7, 0x00));

                // 设置ON按钮为未选中状态：黑色底白色字
                LightOnButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
                LightOnButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
            }
        }

        /// <summary>
        /// 更新滑块进度显示
        /// </summary>
        private void UpdateSliderProgress(double value)
        {
            try
            {
                // 确保在UI线程上执行
                if (!Dispatcher.CheckAccess())
                {
                    Dispatcher.Invoke(() => UpdateSliderProgress(value));
                    return;
                }

                if (ProgressColumn != null && RemainingColumn != null)
                {
                    // 确保值在有效范围内
                    value = Math.Max(0, Math.Min(100, value));
                    
                    // 将百分比转换为Grid列宽比例
                    ProgressColumn.Width = new GridLength(value, GridUnitType.Star);
                    RemainingColumn.Width = new GridLength(100 - value, GridUnitType.Star);
                }
            }
            catch (System.InvalidOperationException ex)
            {
                // 捕获特定的InvalidOperationException，通常发生在控件尚未完全加载时
                Console.WriteLine($"更新滑块进度时发生线程错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新滑块进度时出错: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置灯光开关状态（外部调用）
        /// </summary>
        /// <param name="isOn">是否开启</param>
        public void SetLightSwitch(bool isOn)
        {
            SetLightState(isOn);
        }

        /// <summary>
        /// 设置亮度值（外部调用）
        /// </summary>
        /// <param name="brightness">亮度值 (0-100)</param>
        public void SetBrightness(double brightness)
        {
            BrightnessValue = Math.Max(0, Math.Min(100, brightness));
            if (BrightnessSlider != null)
            {
                BrightnessSlider.Value = BrightnessValue;
            }
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 灯光开关事件参数
    /// </summary>
    public class LightSwitchEventArgs : EventArgs
    {
        public bool IsOn { get; }

        public LightSwitchEventArgs(bool isOn)
        {
            IsOn = isOn;
        }
    }

    /// <summary>
    /// 亮度改变事件参数
    /// </summary>
    public class BrightnessChangedEventArgs : EventArgs
    {
        public int Brightness { get; }

        public BrightnessChangedEventArgs(int brightness)
        {
            Brightness = brightness;
        }
    }

    #endregion
}
