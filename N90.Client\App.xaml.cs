﻿using System.Configuration;
using System.Data;
using System.Windows;
using N90.Client.Views;

namespace N90.Client
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override async void OnStartup(StartupEventArgs e)
        {
            // Don't call base.OnStartup to prevent automatic main window creation

            // Show loading window and start application
            await LoadingWindow.ShowLoadingAndStartApplication();
        }
    }

}
