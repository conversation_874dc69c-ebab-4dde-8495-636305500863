<Page x:Class="N90.Client.Views.Pages.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:components="clr-namespace:N90.Client.Views.Components"
      Title="Settings"
      Background="Transparent"
      Width="930"
      Height="Auto"
      MaxWidth="930"
      MaxHeight="650">

    <Page.Resources>
        <!-- 设置标签样式 -->
        <Style x:Key="SettingLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <!-- 下拉框样式 -->
        <Style x:Key="SettingsComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="#FF1A1A1A"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF333333"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="MinWidth" Value="200"/>
        </Style>

        <!-- 复选框样式 -->
        <Style x:Key="SettingsCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,8,0,8"/>
        </Style>

        <!-- 数字输入框样式 -->
        <Style x:Key="NumberTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FF666666"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF333333"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="Width" Value="60"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="SettingsButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF666666"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <!-- 保存按钮样式 -->
        <Style x:Key="SaveButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4A9B4A"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="25,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Margin" Value="0,20,0,0"/>
        </Style>

        <!-- 头像区域样式 -->
        <Style x:Key="ProfileAreaStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,10,-10,0"/>
            <Setter Property="Height" Value="Auto"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
        </Style>

        <!-- StatusIndicator 配置项样式 -->
        <Style x:Key="SettingsIndicatorItemStyle" TargetType="Grid">
            <Setter Property="Margin" Value="0,8,0,8"/>
        </Style>

        <!-- StatusIndicator 标签样式 -->
        <Style x:Key="IndicatorLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="10,0,0,0"/>
        </Style>

        <!-- Upload New 按钮样式 -->
        <Style x:Key="UploadButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="120"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" CornerRadius="0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="#FF96D701" Offset="0"/>
                                    <GradientStop Color="#FF01E9FE" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border Margin="1" CornerRadius="0">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#FF3B5501" Offset="0"/>
                                        <GradientStop Color="#FF002F33" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Delete Picture 按钮样式 -->
        <Style x:Key="DeleteButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="120"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                BorderBrush="White"
                                BorderThickness="1"
                                CornerRadius="0"
                                Background="#FF888988">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Save Settings 按钮样式 (基于NavigationButton) -->
        <Style x:Key="SaveSettingsButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="180"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Roboto"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- 使用Path绘制特殊切角形状 -->
                            <Path x:Name="ButtonPath"
                                  StrokeThickness="2">
                                <Path.Stroke>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#FFC0E767" Offset="0"/>
                                        <GradientStop Color="#FF67F2FE" Offset="1"/>
                                    </LinearGradientBrush>
                                </Path.Stroke>
                                <Path.Fill>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#FF447212" Offset="0"/>
                                        <GradientStop Color="#FF015158" Offset="1"/>
                                    </LinearGradientBrush>
                                </Path.Fill>
                                <Path.Data>
                                    <!-- 切角形状路径：左上切角，右上切角，右下切角 -->
                                    <PathGeometry>
                                        <PathFigure StartPoint="20,0" IsClosed="True">
                                            <LineSegment Point="180,0"/>
                                            <LineSegment Point="180,0"/>
                                            <LineSegment Point="180,20"/>
                                            <LineSegment Point="160,40"/>
                                            <LineSegment Point="0,40"/>
                                            <LineSegment Point="0,20"/>
                                        </PathFigure>
                                    </PathGeometry>
                                </Path.Data>
                            </Path>

                            <!-- 文字内容 -->
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="10,5"/>

                            <!-- 阴影效果 -->
                            <Grid.Effect>
                                <DropShadowEffect Color="Black" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                            </Grid.Effect>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonPath" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧硬件配置区域 -->
            <StackPanel Grid.Column="0">

                <!-- Network Interface Card -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Select Network Interface Card" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="NetworkInterfaceComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- CPU Temperature Sensor -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="CPU Temperature Sensor" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CpuTempSensorComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- CPU Voltage -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="CPU Voltage" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CpuVoltageComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- CPU Usage -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="CPU Usage" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CpuUsageComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- GPU -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="GPU" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="GpuComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- CPU Fan -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="CPU Fan" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CpuFanComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- Pump Fan -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Pump Fan" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="PumpFanComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- Case Fan 1 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Case Fan 1" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CaseFan1ComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- Case Fan 2 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Case Fan 2" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="CaseFan2ComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- HDD -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="HDD" Style="{StaticResource SettingLabelStyle}"/>
                    <components:CustomComboBox Grid.Column="1" x:Name="HddComboBox" SelectionChanged="CustomComboBox_SelectionChanged"/>
                </Grid>

                <!-- Auto Detect 选项 -->
                <!-- <StackPanel Margin="0,30,0,0">
                    <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <components:StatusIndicator Grid.Column="0" x:Name="AutoDetectCpuIndicator" DataType="AutoDetectCpu" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                        <TextBlock Grid.Column="1" Text="Auto Detect" Style="{StaticResource IndicatorLabelStyle}"/>
                        <TextBlock Grid.Column="2" Text="Ryzen 7 4800 U" Foreground="#FFAAAAAA" FontSize="12" Margin="20,0,0,0"/>
                    </Grid>

                    <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <components:StatusIndicator Grid.Column="0" x:Name="AutoDetectGpuIndicator" DataType="AutoDetectGpu" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                        <TextBlock Grid.Column="1" Text="Auto Detect" Style="{StaticResource IndicatorLabelStyle}"/>
                        <TextBlock Grid.Column="2" Text="Radeon Vega" Foreground="#FFAAAAAA" FontSize="12" Margin="20,0,0,0"/>
                    </Grid>

                    <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <components:StatusIndicator Grid.Column="0" x:Name="AutoDetectMemoryIndicator" DataType="AutoDetectMemory" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                        <TextBlock Grid.Column="1" Text="Auto Detect" Style="{StaticResource IndicatorLabelStyle}"/>
                        <TextBlock Grid.Column="2" Text="16G" Foreground="#FFAAAAAA" FontSize="12" Margin="20,0,0,0"/>
                    </Grid>
                </StackPanel> -->

            </StackPanel>

            <!-- 中间分割图片 -->
            <Image Grid.Column="1"
                   Source="/Resources/Images/img10.png"
                   Stretch="Uniform"
                   Width="20"
                   MaxHeight="500"
                   Margin="10,0,10,0"
                   VerticalAlignment="Center"/>

            <!-- 右侧应用设置区域 -->
            <StackPanel Grid.Column="2">

                <!-- 应用设置选项 - 两列布局 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左列 - 3行 -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <components:StatusIndicator Grid.Column="0" x:Name="AutoLaunchIndicator" DataType="AutoLaunch" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                            <TextBlock Grid.Column="1" Text="Auto Launch at Startup" Style="{StaticResource IndicatorLabelStyle}"/>
                        </Grid>

                        <!-- <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <components:StatusIndicator Grid.Column="0" x:Name="DisableUpdatingIndicator" DataType="DisableUpdating" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                            <TextBlock Grid.Column="1" Text="Disable Updating" Style="{StaticResource IndicatorLabelStyle}"/>
                        </Grid> -->

                        <!-- <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <components:StatusIndicator Grid.Column="0" x:Name="ShowMessageBoxIndicator" DataType="ShowMessageBox" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                            <TextBlock Grid.Column="1" Text="Show Message Box on Startup" Style="{StaticResource IndicatorLabelStyle}"/>
                        </Grid> -->
                    </StackPanel>

                    <!-- 右列 - 2行 -->
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <components:StatusIndicator Grid.Column="0" x:Name="FahrenheitIndicator" DataType="Fahrenheit" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                            <TextBlock Grid.Column="1" Text="Fahrenheit" Style="{StaticResource IndicatorLabelStyle}"/>
                        </Grid>

                        <!-- <Grid Style="{StaticResource SettingsIndicatorItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <components:StatusIndicator Grid.Column="0" x:Name="CompatibilityModeIndicator" DataType="CompatibilityMode" DisableAutoLoad="True" StatusChanged="StatusIndicator_StatusChanged"/>
                            <TextBlock Grid.Column="1" Text="Launch in Compatibility Mode" Style="{StaticResource IndicatorLabelStyle}"/>
                        </Grid> -->
                    </StackPanel>
                </Grid>

                <!-- Delayed Start -->
                <Grid Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="Delayed Start (sec):" Style="{StaticResource SettingLabelStyle}"/>
                    <TextBox Grid.Column="1" x:Name="DelayedStartTextBox" Text="12" Style="{StaticResource NumberTextBoxStyle}" HorizontalAlignment="Left"/>
                </Grid>

                <!-- Profile Picture 区域 -->
                <Border Style="{StaticResource ProfileAreaStyle}">
                    <Grid>
                        <!-- Profile Picture 标签 -->
                        <TextBlock Text="Profile Picture:"
                                   Style="{StaticResource SettingLabelStyle}"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Top"
                                   Margin="0,0,0,0"
                                   Panel.ZIndex="2"/>

                        <!-- 装饰图片边框 -->
                        <Image Source="/Resources/Images/img13.png"
                               Stretch="Uniform"
                               HorizontalAlignment="Center"
                               Width="300"
                               MaxHeight="300"
                               Margin="105,10,0,-10"/>

                        <Grid Margin="0,30,0,0">
                            <!-- 头像显示区域 -->
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 头像 -->
                                <Border Grid.Column="0" Width="80" Height="80" Background="Transparent" CornerRadius="40">
                                    <Image Source="/Resources/Images/img12.png"
                                           Width="80"
                                           Height="80"
                                           Stretch="UniformToFill">
                                        <Image.Clip>
                                            <EllipseGeometry Center="40,40" RadiusX="40" RadiusY="40"/>
                                        </Image.Clip>
                                    </Image>
                                </Border>

                                <!-- 欢迎信息 -->
                                <StackPanel Grid.Column="1" Margin="15,0,0,0" VerticalAlignment="Center">
                                    <Border Background="#FF666666" Padding="15,8" HorizontalAlignment="Left">
                                        <TextBlock Text="Hello !" Foreground="White" FontSize="16" FontWeight="Bold"/>
                                    </Border>
                                    <TextBlock Text="Welcome" Foreground="White" FontSize="14" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Profile Picture 按钮区域 -->
                <StackPanel Orientation="Vertical" HorizontalAlignment="Left" Margin="0,0,0,0">
                    <Button Content="Upload New" Style="{StaticResource UploadButtonStyle}" Margin="0,-5,0,5"/>
                    <Button Content="Delete Picture" Style="{StaticResource DeleteButtonStyle}"/>
                </StackPanel>

                <!-- Save Settings 按钮 -->
                <Button Content="Save Settings" Style="{StaticResource SaveSettingsButtonStyle}" Click="SaveSettings_Click"/>

            </StackPanel>

        </Grid>
    </ScrollViewer>
</Page>
