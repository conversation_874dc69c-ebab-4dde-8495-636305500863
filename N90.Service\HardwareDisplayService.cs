// 硬件显示服务，负责保存/加载显示状态
using System;
using Microsoft.Win32;
using N90.Shared;

namespace N90.Service
{
    public class HardwareDisplayService
    {
        private readonly RegistryKey registryKey;

        public HardwareDisplayService()
        {
            registryKey = Registry.CurrentUser.CreateSubKey("Software\\N90\\HardwareDisplay");
        }

        public void SaveHardwareDisplayState(HardwareData hardwareData)
        {
            try
            {
                // Save Date/Time
                registryKey.SetValue("Date", hardwareData.Date.show ? 1 : 0);
                registryKey.SetValue("Time", hardwareData.Time.show ? 1 : 0);
                registryKey.SetValue("Weekday", hardwareData.Weekday.show ? 1 : 0);

                // Save CPU
                registryKey.SetValue("CPUTemperature", hardwareData.CPUTemperature.show ? 1 : 0);
                registryKey.SetValue("CPUUsage", hardwareData.CPUUsage.show ? 1 : 0);
                registryKey.SetValue("CPUPower", hardwareData.CPUPower.show ? 1 : 0);
                registryKey.SetValue("CPUFanSpeed", hardwareData.CPUFanSpeed.show ? 1 : 0);
                registryKey.SetValue("CPUModel", hardwareData.CPUModel.show ? 1 : 0);

                // Save GPU
                registryKey.SetValue("GPUTemperature", hardwareData.GPUTemperature.show ? 1 : 0);
                registryKey.SetValue("GPUMemoryUsage", hardwareData.GPUMemoryUsage.show ? 1 : 0);
                registryKey.SetValue("GPUPower", hardwareData.GPUPower.show ? 1 : 0);
                registryKey.SetValue("GPUModel", hardwareData.GPUModel.show ? 1 : 0);

                // Save RAM
                registryKey.SetValue("RAMUsage", hardwareData.RAMUsage.show ? 1 : 0);
                registryKey.SetValue("AvailableRAM", hardwareData.AvailableRAM.show ? 1 : 0);

                // Save Fans
                registryKey.SetValue("CaseFan1Speed", hardwareData.CaseFan1Speed.show ? 1 : 0);
                registryKey.SetValue("CaseFan2Speed", hardwareData.CaseFan2Speed.show ? 1 : 0);

                // Save HDD
                registryKey.SetValue("HDDTemperature", hardwareData.HDDTemperature.show ? 1 : 0);
                registryKey.SetValue("HDDUsage", hardwareData.HDDUsage.show ? 1 : 0);

                // Save Network
                registryKey.SetValue("UploadSpeed", hardwareData.UploadSpeed.show ? 1 : 0);
                registryKey.SetValue("DownloadSpeed", hardwareData.DownloadSpeed.show ? 1 : 0);

                // Save CustomString
                registryKey.SetValue("CustomString", hardwareData.CustomString.show ? 1 : 0);
                registryKey.SetValue("CustomStringValue", hardwareData.CustomString.value ?? "");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving hardware display state: {ex.Message}");
            }
        }

        public void LoadHardwareDisplayState(HardwareData hardwareData)
        {
            try
            {
                // Load Date/Time
                hardwareData.Date.show = Convert.ToBoolean(registryKey.GetValue("Date", 1));
                hardwareData.Time.show = Convert.ToBoolean(registryKey.GetValue("Time", 1));
                hardwareData.Weekday.show = Convert.ToBoolean(registryKey.GetValue("Weekday", 1));

                // Load CPU
                hardwareData.CPUTemperature.show = Convert.ToBoolean(registryKey.GetValue("CPUTemperature", 1));
                hardwareData.CPUUsage.show = Convert.ToBoolean(registryKey.GetValue("CPUUsage", 1));
                hardwareData.CPUPower.show = Convert.ToBoolean(registryKey.GetValue("CPUPower", 1));
                hardwareData.CPUFanSpeed.show = Convert.ToBoolean(registryKey.GetValue("CPUFanSpeed", 1));
                hardwareData.CPUModel.show = Convert.ToBoolean(registryKey.GetValue("CPUModel", 1));

                // Load GPU
                hardwareData.GPUTemperature.show = Convert.ToBoolean(registryKey.GetValue("GPUTemperature", 1));
                hardwareData.GPUMemoryUsage.show = Convert.ToBoolean(registryKey.GetValue("GPUMemoryUsage", 1));
                hardwareData.GPUPower.show = Convert.ToBoolean(registryKey.GetValue("GPUPower", 1));
                hardwareData.GPUModel.show = Convert.ToBoolean(registryKey.GetValue("GPUModel", 1));

                // Load RAM
                hardwareData.RAMUsage.show = Convert.ToBoolean(registryKey.GetValue("RAMUsage", 1));
                hardwareData.AvailableRAM.show = Convert.ToBoolean(registryKey.GetValue("AvailableRAM", 1));

                // Load Fans
                hardwareData.CaseFan1Speed.show = Convert.ToBoolean(registryKey.GetValue("CaseFan1Speed", 1));
                hardwareData.CaseFan2Speed.show = Convert.ToBoolean(registryKey.GetValue("CaseFan2Speed", 1));

                // Load HDD
                hardwareData.HDDTemperature.show = Convert.ToBoolean(registryKey.GetValue("HDDTemperature", 1));
                hardwareData.HDDUsage.show = Convert.ToBoolean(registryKey.GetValue("HDDUsage", 1));

                // Load Network
                hardwareData.UploadSpeed.show = Convert.ToBoolean(registryKey.GetValue("UploadSpeed", 1));
                hardwareData.DownloadSpeed.show = Convert.ToBoolean(registryKey.GetValue("DownloadSpeed", 1));

                // Load CustomString
                hardwareData.CustomString.show = Convert.ToBoolean(registryKey.GetValue("CustomString", 1));
                hardwareData.CustomString.value = registryKey.GetValue("CustomStringValue", "") as string;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading hardware display state: {ex.Message}");
            }
        }
    }
}
