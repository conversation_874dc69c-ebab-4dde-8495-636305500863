<Page x:Class="N90.Client.Views.Pages.N90Page"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:components="clr-namespace:N90.Client.Views.Components"
      Title="N90"
      Background="Transparent"
      Width="Auto"
      Height="Auto">

    <Page.Resources>

        <!-- 分组标题样式 -->
        <Style x:Key="GroupTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#97D200"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 带图标的分组标题样式 -->
        <Style x:Key="GroupTitleWithIconStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 标题图标样式 -->
        <Style x:Key="TitleIconStyle" TargetType="Image">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 标题文本样式 -->
        <Style x:Key="TitleTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 信息标签样式 -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,2"/>
        </Style>

        <!-- 信息值样式 -->
        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>



        <!-- 主标题样式 -->
        <Style x:Key="MainTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="MainImgStyle" TargetType="Image">
            <Setter Property="Width" Value="225"/>
            <Setter Property="Height" Value="280"/>
            <Setter Property="Margin" Value="0,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 副标题样式 -->
        <Style x:Key="SubTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,18"/>
        </Style>

        <!-- 标签页按钮样式 -->
        <Style x:Key="TabButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#FF999999"/>
            <Setter Property="BorderThickness" Value="0,0,0,2"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <!-- 动效 -->
                        <!-- <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="#FFCCCCCC"/>
                                <Setter Property="BorderBrush" Value="#FF666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers> -->
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 激活的标签页按钮样式 -->
        <Style x:Key="ActiveTabButtonStyle" TargetType="Button" BasedOn="{StaticResource TabButtonStyle}">
            <Setter Property="Foreground" Value="#97D200"/>
            <Setter Property="BorderBrush" Value="#97D200"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <!-- 动效 -->
                        <!-- <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="#97D200"/>
                                <Setter Property="BorderBrush" Value="#97D200"/>
                            </Trigger>
                        </ControlTemplate.Triggers> -->
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 通用：菱形按钮基础样式 -->
        <Style x:Key="ParallelogramTabButtonBaseStyle" TargetType="Button">
            <Setter Property="Width" Value="160"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="#97D200"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True"
                                RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <SkewTransform AngleX="45"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              RenderTransformOrigin="0.5,0.5">
                                <ContentPresenter.RenderTransform>
                                    <SkewTransform AngleX="-45"/>
                                </ContentPresenter.RenderTransform>
                            </ContentPresenter>
                        </Border>
                        <!-- <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="#FFCCCCCC"/>
                                <Setter Property="BorderBrush" Value="#FF666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers> -->
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 左侧（Basic Parameters） -->
        <Style x:Key="LeftActiveTabButtonStyle" TargetType="Button" BasedOn="{StaticResource ParallelogramTabButtonBaseStyle}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,2,2,0"/>
        </Style>

        <Style x:Key="LeftInactiveTabButtonStyle" TargetType="Button" BasedOn="{StaticResource ParallelogramTabButtonBaseStyle}">
            <Setter Property="Background" Value="#FF666666"/>
            <Setter Property="BorderThickness" Value="0,2,0,0"/>
        </Style>

        <!-- 右侧（Theme） -->
        <Style x:Key="RightActiveTabButtonStyle" TargetType="Button" BasedOn="{StaticResource ParallelogramTabButtonBaseStyle}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,2,2,0"/>
        </Style>

        <Style x:Key="RightInactiveTabButtonStyle" TargetType="Button" BasedOn="{StaticResource ParallelogramTabButtonBaseStyle}">
            <Setter Property="Background" Value="#FF666666"/>
            <Setter Property="BorderThickness" Value="0,0,0,2"/>
        </Style>

        <!-- 标签页容器样式 -->
        <Style x:Key="TabContainerStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>
    </Page.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部标签页菜单 -->
        <Grid Grid.Row="0" Margin="15,0,0,12" Height="40">
            <!-- 背景 -->
            <Rectangle Fill="Transparent"/>

            <!-- 底部横向绿色线条 -->
            <Rectangle Fill="#97D200" Height="2" VerticalAlignment="Bottom" HorizontalAlignment="Stretch" Margin="338,0,0,0"/>
            <!-- 标签页按钮 -->
            <StackPanel Style="{StaticResource TabContainerStyle}"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left">
                <Button x:Name="BasicParametersTab"
                        Content="Basic Parameters"
                        Style="{StaticResource LeftActiveTabButtonStyle}"
                        Click="TabButton_Click"/>
                <Button x:Name="ThemeTab"
                        Content="Theme"
                        Style="{StaticResource RightInactiveTabButtonStyle}"
                        Click="TabButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <!-- Basic Parameters 标签页内容 -->
            <Grid x:Name="BasicParametersContent" Visibility="Visible">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="270"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧区域 - 使用N90ControlPanel组件 -->
                <components:N90ControlPanel Grid.Column="0"
                                            x:Name="N90ControlPanel"
                                            Title="N90"
                                            ImageSource="/Resources/Images/img4.png"
                                            IconSource="/Resources/Images/img1.png"
                                            ControlTitle="Running Light Switch Brightness"
                                            BrightnessValue="50"
                                            LightSwitchChanged="N90ControlPanel_LightSwitchChanged"
                                            BrightnessChanged="N90ControlPanel_BrightnessChanged"/>

        <!-- 右侧信息显示区域 -->
        <StackPanel Grid.Column="1" Margin="35,0,0,0">
            <!-- 副标题 -->
            <TextBlock Text="Slideshow Information Display" Style="{StaticResource SubTitleStyle}" FontSize="20"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="6*"/>
                    <ColumnDefinition Width="4*"/>
                </Grid.ColumnDefinitions>

                <!-- 左列信息 -->
                <StackPanel Grid.Column="0" Margin="0,0,40,0">

                    <!-- System Info -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                            <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                            <TextBlock Text="System Info" Style="{StaticResource TitleTextStyle}"/>
                        </StackPanel>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Date" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding DateValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="Date"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Time" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding TimeValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="Time"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Day of Week" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding WeekdayValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="Weekday"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="CPU Model" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding CPUModelValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CPUModel"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="GPU Model" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding GPUModelValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="GPUModel"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Custom String" Style="{StaticResource InfoLabelStyle}"/>
                            <Button Grid.Column="1"
                                    x:Name="CustomStringButton"
                                    Width="180"
                                    Height="13"
                                    HorizontalAlignment="Left"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    Padding="0"
                                    Click="CustomStringButton_Click"
                                    Cursor="Hand">
                                <Button.Resources>
                                    <!-- 边框渐变：从左到右 RGB(151,251,0) -> RGB(1,233,254) -->
                                    <LinearGradientBrush x:Key="BorderGradientBrush" StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#FF97FB00" Offset="0"/>
                                        <GradientStop Color="#FF01E9FE" Offset="1"/>
                                    </LinearGradientBrush>

                                    <!-- 填充渐变：从上到下 RGB(60,86,0) -> RGB(11,58,47) -->
                                    <LinearGradientBrush x:Key="FillGradientBrush" StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#FF3C5600" Offset="0"/>
                                        <GradientStop Color="#FF0B3A2F" Offset="1"/>
                                    </LinearGradientBrush>

                                    <!-- 悬停时的填充渐变（稍微亮一些） -->
                                    <LinearGradientBrush x:Key="HoverFillGradientBrush" StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#FF4C6610" Offset="0"/>
                                        <GradientStop Color="#FF1B4A3F" Offset="1"/>
                                    </LinearGradientBrush>

                                    <!-- 按下时的填充渐变（稍微暗一些） -->
                                    <LinearGradientBrush x:Key="PressedFillGradientBrush" StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#FF2C4600" Offset="0"/>
                                        <GradientStop Color="#FF012A1F" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Resources>

                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border x:Name="ButtonBorder"
                                                Background="{StaticResource FillGradientBrush}"
                                                BorderBrush="{StaticResource BorderGradientBrush}"
                                                BorderThickness="2">
                                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                              VerticalAlignment="Center"
                                                              Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource HoverFillGradientBrush}"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PressedFillGradientBrush}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Button.Template>
                                <TextBlock Text="Edit"
                                           Foreground="White"
                                           FontSize="11"
                                           FontFamily="Roboto"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Button>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CustomString"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>
                    </StackPanel>

                    <!-- Cooling & Fans - 50% 宽度 -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="7*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                                <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                                <TextBlock Text="Cooling &amp; Fans" Style="{StaticResource TitleTextStyle}"/>
                            </StackPanel>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="CPU Fan Speed" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Column="1" Text="{Binding CPUFanSpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                                <components:StatusIndicator Grid.Column="2"
                                                            Margin="5,0"
                                                            DataType="CPUFanSpeed"
                                                            StatusChanged="StatusIndicator_StatusChanged"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Chassis Fan 1 Speed" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Column="1" Text="{Binding CaseFan1SpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                                <components:StatusIndicator Grid.Column="2"
                                                            Margin="5,0"
                                                            DataType="CaseFan1Speed"
                                                            StatusChanged="StatusIndicator_StatusChanged"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Chassis Fan 2 Speed" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Column="1" Text="{Binding CaseFan2SpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                                <components:StatusIndicator Grid.Column="2"
                                                            Margin="5,0"
                                                            DataType="CaseFan2Speed"
                                                            StatusChanged="StatusIndicator_StatusChanged"/>
                            </Grid>
                        </StackPanel>
                    </Grid>

                    <!-- Network Info - 50% 宽度 -->
                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="7*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                                <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                                <TextBlock Text="Network Info" Style="{StaticResource TitleTextStyle}"/>
                            </StackPanel>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Network Upload Speed" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Column="1" Text="{Binding NetworkUploadSpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                                <components:StatusIndicator Grid.Column="2"
                                                            Margin="5,0"
                                                            DataType="NetworkUploadSpeed"
                                                            StatusChanged="StatusIndicator_StatusChanged"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Network Download Speed" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Column="1" Text="{Binding NetworkDownloadSpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                                <components:StatusIndicator Grid.Column="2"
                                                            Margin="5,0"
                                                            DataType="NetworkDownloadSpeed"
                                                            StatusChanged="StatusIndicator_StatusChanged"/>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </StackPanel>

                <!-- 右列信息 -->
                <StackPanel Grid.Column="1">

                    <!-- CPU Info -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                            <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                            <TextBlock Text="CPU Info" Style="{StaticResource TitleTextStyle}"/>
                        </StackPanel>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="CPU Temperature" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding CPUTemperatureValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CPUTemperature"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="CPU Usage" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding CPUUsageValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CPUUsage"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="CPU Power" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding CPUPowerValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CPUPower"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="CPU Fan Speed" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding CPUFanSpeedValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="CPUFanSpeed"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>
                    </StackPanel>

                    <!-- GPU Info 第二部分 -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                            <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                            <TextBlock Text="GPU Info" Style="{StaticResource TitleTextStyle}"/>
                        </StackPanel>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="GPU Temperature" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding GPUTemperatureValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="GPUTemperature"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="GPU Memory Usage" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding GPUMemoryUsageValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="GPUMemoryUsage"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="GPU Power" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding GPUPowerValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="GPUPower"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>
                    </StackPanel>

                    <!-- Memory & Storage -->
                    <StackPanel Margin="0,0,0,20">
                        <StackPanel Style="{StaticResource GroupTitleWithIconStyle}">
                            <Image Source="/Resources/Images/img1.png" Style="{StaticResource TitleIconStyle}"/>
                            <TextBlock Text="Memory &amp; Storage" Style="{StaticResource TitleTextStyle}"/>
                        </StackPanel>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="RAM Usage" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding RAMUsageValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="RAMUsage"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Available RAM" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding UsedRAMValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="UsedRAM"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Drive Temperature" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding DriveTemperatureValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="DriveTemperature"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Drive Usage" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Grid.Column="1" Text="{Binding DriveUsageValue}" Style="{StaticResource InfoValueStyle}"/>
                            <components:StatusIndicator Grid.Column="2"
                                                        Margin="5,0"
                                                        DataType="DriveUsage"
                                                        StatusChanged="StatusIndicator_StatusChanged"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </StackPanel>
            </Grid>

            <!-- Theme 标签页内容 -->
            <Grid x:Name="ThemeContent" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="270"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧区域 - 使用N90ControlPanel组件 -->
                <components:N90ControlPanel Grid.Column="0"
                                            x:Name="N90ControlPanelTheme"
                                            Title="N90"
                                            ImageSource="/Resources/Images/img4.png"
                                            IconSource="/Resources/Images/img1.png"
                                            ControlTitle="Running Light Switch Brightness"
                                            BrightnessValue="50"
                                            LightSwitchChanged="N90ControlPanel_LightSwitchChanged"
                                            BrightnessChanged="N90ControlPanel_BrightnessChanged"/>

                <!-- 右侧信息显示区域 -->
                <StackPanel Grid.Column="1" Margin="35,0,0,0">
                    <!-- 副标题 -->
                    <TextBlock Text="Theme Display" Style="{StaticResource SubTitleStyle}" FontSize="20"/>

                    <!-- Theme图片 -->
                    <Image Source="/Resources/Images/theme.png"
                           Width="900"
                           Height="420"
                           Margin="0,20,0,0"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Top"
                           Stretch="Uniform"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Grid>
</Page>
