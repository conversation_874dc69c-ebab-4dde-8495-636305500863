using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using N90.Shared;
using N90.Shared.Services;

namespace N90.Service
{
    class Program
    {
        private static HardwareReader hardwareReader;
        private static HidSender hidSender;
        private static HardwareData hardwareData;
        private static HardwareDisplayService hardwareDisplayService;
        private static TemperatureUnitService temperatureUnitService;
        private static LanguageService languageService;
        private const int UPDATE_INTERVAL = 2000; // 2 seconds

        static async Task Main(string[] args)
        {
            // Initialize logging system
            Logger.LogInfo("N90 Service starting...");

            // Check if running as Windows Service or Console
            var isService = args.Length > 0 && args[0] == "--service";

            if (isService)
            {
                Logger.LogInfo("Running as Windows Service");
                // Run as Windows Service
                await CreateHostBuilder(args).Build().RunAsync();
            }
            else
            {
                Logger.LogInfo("Running as Console Application (development mode)");
                // Run as Console Application (for development)
                await RunAsConsole();
            }

            Logger.LogInfo("N90 Service shutdown complete");
        }

        static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService(options =>
                {
                    options.ServiceName = "N90 Hardware Monitor Service";
                })
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddHostedService<N90ServiceWorker>();
                    services.AddSingleton<HardwareReader>();
                    services.AddSingleton<HidSender>(provider => new HidSender(0x5131, 0x2007));
                    services.AddSingleton<HardwareDisplayService>();
                    services.AddSingleton<TemperatureUnitService>();
                    services.AddSingleton<LanguageService>();
                    services.AddSingleton<HardwareData>();
                    services.AddSingleton<DisplayPreferencesService>();
                });

        static async Task RunAsConsole()
        {
            Console.WriteLine("N90 Service starting in console mode...");
            Console.WriteLine("Press Ctrl+C to stop the service.");
            Logger.LogInfo("Console mode initialization started");

            // Initialize hardware data
            InitHardwareData();

            var worker = new N90ServiceWorker(
                new HardwareReader(),
                new HidSender(0x5131, 0x2007),
                new HardwareDisplayService(),
                new TemperatureUnitService(),
                new LanguageService(),
                hardwareData
            );

            var cancellationTokenSource = new CancellationTokenSource();

            // Handle Ctrl+C
            Console.CancelKeyPress += (sender, e) =>
            {
                e.Cancel = true;
                cancellationTokenSource.Cancel();
            };

            try
            {
                await worker.StartAsync(cancellationTokenSource.Token);

                // Keep running until cancelled
                while (!cancellationTokenSource.Token.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationTokenSource.Token);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            finally
            {
                await worker.StopAsync(CancellationToken.None);
                Console.WriteLine("N90 Service stopped.");
            }
        }

        private static void InitHardwareData()
        {
            hardwareData = new HardwareData();
            hardwareData.Date = new Date();
            hardwareData.Time = new Time();
            hardwareData.Weekday = new Weekday();

            hardwareData.CPUTemperature = new CPUTemperature();
            hardwareData.CPUUsage = new CPUUsage();
            hardwareData.CPUPower = new CPUPower();
            hardwareData.CPUFanSpeed = new CPUFanSpeed();
            hardwareData.CPUModel = new CPUModel();

            hardwareData.GPUTemperature = new GPUTemperature();
            hardwareData.GPUMemoryUsage = new GPUMemoryUsage();
            hardwareData.GPUPower = new GPUPower();
            hardwareData.GPUModel = new GPUModel();

            hardwareData.RAMUsage = new RAMUsage();
            hardwareData.AvailableRAM = new AvailableRAM();

            hardwareData.CaseFan1Speed = new CaseFan1Speed();
            hardwareData.CaseFan2Speed = new CaseFan2Speed();

            hardwareData.HDDTemperature = new HDDTemperature();
            hardwareData.HDDUsage = new HDDUsage();

            hardwareData.UploadSpeed = new UploadSpeed();
            hardwareData.DownloadSpeed = new DownloadSpeed();

            hardwareData.CustomString = new CustomString();

            // Set default show states
            hardwareData.Date.show = true;
            hardwareData.Time.show = true;
            hardwareData.Weekday.show = true;
            hardwareData.CPUTemperature.show = true;
            hardwareData.CPUUsage.show = true;
            hardwareData.CPUPower.show = true;
            hardwareData.CPUFanSpeed.show = true;
            hardwareData.CPUModel.show = true;
            hardwareData.GPUTemperature.show = true;
            hardwareData.GPUMemoryUsage.show = true;
            hardwareData.GPUPower.show = true;
            hardwareData.GPUModel.show = true;
            hardwareData.RAMUsage.show = true;
            hardwareData.AvailableRAM.show = true;
            hardwareData.CaseFan1Speed.show = true;
            hardwareData.CaseFan2Speed.show = true;
            hardwareData.HDDTemperature.show = true;
            hardwareData.HDDUsage.show = true;
            hardwareData.UploadSpeed.show = true;
            hardwareData.DownloadSpeed.show = true;
            hardwareData.CustomString.show = true;
        }

        private static string GetWeekdayString(DayOfWeek dayOfWeek)
        {
            var currentLanguage = languageService.LoadLanguageSetting();

            if (currentLanguage == "en-US")
            {
                return dayOfWeek switch
                {
                    DayOfWeek.Monday => "Monday",
                    DayOfWeek.Tuesday => "Tuesday",
                    DayOfWeek.Wednesday => "Wednesday",
                    DayOfWeek.Thursday => "Thursday",
                    DayOfWeek.Friday => "Friday",
                    DayOfWeek.Saturday => "Saturday",
                    DayOfWeek.Sunday => "Sunday",
                    _ => "Monday"
                };
            }
            else // Default to Chinese
            {
                return dayOfWeek switch
                {
                    DayOfWeek.Monday => "星期一",
                    DayOfWeek.Tuesday => "星期二",
                    DayOfWeek.Wednesday => "星期三",
                    DayOfWeek.Thursday => "星期四",
                    DayOfWeek.Friday => "星期五",
                    DayOfWeek.Saturday => "星期六",
                    DayOfWeek.Sunday => "星期日",
                    _ => "星期一"
                };
            }
        }
    }
}
