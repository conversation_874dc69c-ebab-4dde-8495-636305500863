namespace N90.Shared
{
    /// <summary>
    /// HID 协议中 Type 字节的取值，来源于旧项目文档。
    /// </summary>
    public static class PacketType
    {
        // 时间相关 0x01~0x0F
        public const byte Date = 0x00;
        public const byte Time = 0x01;
        public const byte Weekday = 0x02;

        // CPU 0x10 ~ 0x1F
        public const byte CpuTemperature = 0x10;
        public const byte CpuUsage = 0x11;
        public const byte CpuPower = 0x12;
        public const byte CpuFanSpeed = 0x13;
        public const byte CpuModel = 0x14;

        // GPU 0x20 ~ 0x2F
        public const byte GpuTemperature = 0x20;
        public const byte GpuMemoryUsage = 0x21;
        public const byte GpuPower = 0x22;
        public const byte GpuModel = 0x23;

        // RAM 0x30 ~ 0x3F
        public const byte RamUsage = 0x30;
        public const byte AvailableRam = 0x31;

        // Fan 0x40 ~ 0x4F
        public const byte CaseFan1Speed = 0x40;
        public const byte CaseFan2Speed = 0x41;

        // HDD 0x50 ~ 0x5F
        public const byte HddTemperature = 0x50;
        public const byte HddUsage = 0x51;

        // Network 0x60 ~ 0x6F
        public const byte UploadSpeed = 0x60;
        public const byte DownloadSpeed = 0x61;

        // Custom
        public const byte CustomString = 0x70;
    }
}
