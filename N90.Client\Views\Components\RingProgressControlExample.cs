using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// RingProgressControl组件使用示例
    /// 展示如何在其他页面或窗口中使用RingProgressControl组件
    /// </summary>
    public class RingProgressControlExample
    {
        /// <summary>
        /// 在代码中创建和配置RingProgressControl组件的示例
        /// </summary>
        public static RingProgressControl CreateRingProgress()
        {
            var ringProgress = new RingProgressControl
            {
                // 设置组件属性
                Value = 59,                    // 设置进度值为59%
                Label = "CPU Usage",           // 设置标签
                ShowPercentSign = true,        // 显示百分号
                ValueFormat = "F0",           // 整数格式
                Width = 200,                  // 设置宽度
                Height = 200,                 // 设置高度
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            return ringProgress;
        }

        /// <summary>
        /// 创建不同配置的环形进度控件示例
        /// </summary>
        public static StackPanel CreateMultipleRingProgressExample()
        {
            var container = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // CPU使用率
            var cpuProgress = new RingProgressControl
            {
                Value = 75,
                Label = "CPU",
                ShowPercentSign = true,
                Width = 150,
                Height = 150,
                Margin = new Thickness(10)
            };

            // 内存使用率
            var memoryProgress = new RingProgressControl
            {
                Value = 45,
                Label = "Memory",
                ShowPercentSign = true,
                Width = 150,
                Height = 150,
                Margin = new Thickness(10)
            };

            // 磁盘使用率
            var diskProgress = new RingProgressControl
            {
                Value = 89,
                Label = "Disk",
                ShowPercentSign = true,
                Width = 150,
                Height = 150,
                Margin = new Thickness(10)
            };

            // 温度显示（不显示百分号）
            var temperatureProgress = new RingProgressControl
            {
                Value = 65,
                Label = "Temperature",
                ShowPercentSign = false,
                ValueFormat = "F0",
                Width = 150,
                Height = 150,
                Margin = new Thickness(10)
            };

            container.Children.Add(cpuProgress);
            container.Children.Add(memoryProgress);
            container.Children.Add(diskProgress);
            container.Children.Add(temperatureProgress);

            return container;
        }

        /// <summary>
        /// 演示如何动态更新进度值
        /// </summary>
        public static void DemonstrateProgressAnimation(RingProgressControl ringProgress)
        {
            if (ringProgress == null) return;

            // 创建定时器来模拟数据更新
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };

            var currentValue = 0.0;
            var targetValue = 85.0;
            var increment = 2.0;

            timer.Tick += (sender, e) =>
            {
                currentValue += increment;
                ringProgress.SetValue(currentValue);

                if (currentValue >= targetValue)
                {
                    timer.Stop();
                }
            };

            timer.Start();
        }

        /// <summary>
        /// 演示如何通过代码控制组件
        /// </summary>
        public static void DemonstrateControlMethods(RingProgressControl ringProgress)
        {
            if (ringProgress == null) return;

            // 设置特定值
            ringProgress.SetValue(75);

            // 修改标签
            ringProgress.Label = "GPU Usage";

            // 切换百分号显示
            ringProgress.ShowPercentSign = false;

            // 修改数值格式
            ringProgress.ValueFormat = "F1"; // 显示一位小数

            // 重置进度
            // ringProgress.Reset();

            // 设置满进度
            // ringProgress.SetFull();
        }
    }
}

/*
XAML中使用RingProgressControl组件的示例：

<Window x:Class="YourNamespace.YourWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:components="clr-namespace:N90.Client.Views.Components">
    
    <Grid>
        <!-- 基本使用 -->
        <components:RingProgressControl x:Name="CpuProgress"
                                        Value="59"
                                        Label="CPU"
                                        ShowPercentSign="True"
                                        Width="200"
                                        Height="200"/>
        
        <!-- 自定义配置 -->
        <components:RingProgressControl x:Name="MemoryProgress"
                                        Grid.Column="1"
                                        Value="75"
                                        Label="Memory Usage"
                                        ShowPercentSign="True"
                                        ValueFormat="F1"
                                        Width="180"
                                        Height="180"/>
        
        <!-- 温度显示（不显示百分号） -->
        <components:RingProgressControl x:Name="TemperatureProgress"
                                        Grid.Column="2"
                                        Value="65"
                                        Label="Temperature"
                                        ShowPercentSign="False"
                                        ValueFormat="F0"
                                        Width="160"
                                        Height="160"/>
    </Grid>
</Window>

对应的代码文件中的使用：

// 动态更新进度值
CpuProgress.SetValue(85);

// 重置进度
MemoryProgress.Reset();

// 设置满进度
TemperatureProgress.SetFull();

// 修改标签和格式
CpuProgress.Label = "Processor";
CpuProgress.ValueFormat = "F1";
*/
