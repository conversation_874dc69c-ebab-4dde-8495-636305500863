using System;
using System.Collections;
using System.Windows;
using System.Windows.Controls;

namespace N90.Client.Views.Components
{
    /// <summary>
    /// CustomComboBox.xaml 的交互逻辑
    /// 使用img11.png作为边框的自定义下拉框组件
    /// </summary>
    public partial class CustomComboBox : UserControl
    {
        /// <summary>
        /// 选中项依赖属性
        /// </summary>
        public static readonly DependencyProperty SelectedItemProperty =
            DependencyProperty.Register(
                nameof(SelectedItem),
                typeof(object),
                typeof(CustomComboBox),
                new PropertyMetadata(null, OnSelectedItemChanged));

        /// <summary>
        /// 选中索引依赖属性
        /// </summary>
        public static readonly DependencyProperty SelectedIndexProperty =
            DependencyProperty.Register(
                nameof(SelectedIndex),
                typeof(int),
                typeof(CustomComboBox),
                new PropertyMetadata(-1, OnSelectedIndexChanged));

        /// <summary>
        /// 数据源依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register(
                nameof(ItemsSource),
                typeof(IEnumerable),
                typeof(CustomComboBox),
                new PropertyMetadata(null, OnItemsSourceChanged));

        /// <summary>
        /// 显示成员路径依赖属性
        /// </summary>
        public static readonly DependencyProperty DisplayMemberPathProperty =
            DependencyProperty.Register(
                nameof(DisplayMemberPath),
                typeof(string),
                typeof(CustomComboBox),
                new PropertyMetadata(string.Empty, OnDisplayMemberPathChanged));

        /// <summary>
        /// 项目字符串数组依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemsProperty =
            DependencyProperty.Register(
                nameof(Items),
                typeof(string[]),
                typeof(CustomComboBox),
                new PropertyMetadata(null, OnItemsChanged));

        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event SelectionChangedEventHandler? SelectionChanged;

        public CustomComboBox()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 选中项
        /// </summary>
        public object SelectedItem
        {
            get => GetValue(SelectedItemProperty);
            set => SetValue(SelectedItemProperty, value);
        }

        /// <summary>
        /// 选中索引
        /// </summary>
        public int SelectedIndex
        {
            get => (int)GetValue(SelectedIndexProperty);
            set => SetValue(SelectedIndexProperty, value);
        }

        /// <summary>
        /// 数据源
        /// </summary>
        public IEnumerable ItemsSource
        {
            get => (IEnumerable)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }

        /// <summary>
        /// 显示成员路径
        /// </summary>
        public string DisplayMemberPath
        {
            get => (string)GetValue(DisplayMemberPathProperty);
            set => SetValue(DisplayMemberPathProperty, value);
        }

        /// <summary>
        /// 项目字符串数组
        /// </summary>
        public string[] Items
        {
            get => (string[])GetValue(ItemsProperty);
            set => SetValue(ItemsProperty, value);
        }

        /// <summary>
        /// 获取内部ComboBox的Items集合
        /// </summary>
        public ItemCollection InternalItems => InternalComboBox.Items;

        /// <summary>
        /// 选中项改变时的回调
        /// </summary>
        private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CustomComboBox customComboBox)
            {
                customComboBox.InternalComboBox.SelectedItem = e.NewValue;
            }
        }

        /// <summary>
        /// 选中索引改变时的回调
        /// </summary>
        private static void OnSelectedIndexChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CustomComboBox customComboBox)
            {
                customComboBox.InternalComboBox.SelectedIndex = (int)e.NewValue;
            }
        }

        /// <summary>
        /// 数据源改变时的回调
        /// </summary>
        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CustomComboBox customComboBox)
            {
                customComboBox.InternalComboBox.ItemsSource = e.NewValue as IEnumerable;
            }
        }

        /// <summary>
        /// 显示成员路径改变时的回调
        /// </summary>
        private static void OnDisplayMemberPathChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CustomComboBox customComboBox)
            {
                customComboBox.InternalComboBox.DisplayMemberPath = e.NewValue as string ?? string.Empty;
            }
        }

        /// <summary>
        /// 项目数组改变时的回调
        /// </summary>
        private static void OnItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CustomComboBox customComboBox)
            {
                customComboBox.UpdateItems(e.NewValue as string[]);
            }
        }

        /// <summary>
        /// 更新项目列表
        /// </summary>
        private void UpdateItems(string[]? items)
        {
            InternalComboBox.Items.Clear();

            if (items != null)
            {
                for (int i = 0; i < items.Length; i++)
                {
                    var item = new ComboBoxItem
                    {
                        Content = items[i],
                        IsSelected = i == 0 // 默认选中第一项
                    };
                    InternalComboBox.Items.Add(item);
                }
            }
        }

        /// <summary>
        /// 内部ComboBox选择改变事件处理
        /// </summary>
        private void InternalComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 同步选中项和选中索引
            SelectedItem = InternalComboBox.SelectedItem;
            SelectedIndex = InternalComboBox.SelectedIndex;
            
            // 触发外部事件
            SelectionChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 添加ComboBoxItem的便捷方法
        /// </summary>
        /// <param name="content">项内容</param>
        /// <param name="isSelected">是否选中</param>
        public void AddItem(object content, bool isSelected = false)
        {
            var item = new ComboBoxItem
            {
                Content = content,
                IsSelected = isSelected
            };
            InternalComboBox.Items.Add(item);
        }

        /// <summary>
        /// 添加ComboBoxItem的便捷方法，支持显示内容和实际值分离
        /// </summary>
        /// <param name="displayContent">显示内容</param>
        /// <param name="actualValue">实际值</param>
        /// <param name="isSelected">是否选中</param>
        public void AddItem(object displayContent, object actualValue, bool isSelected = false)
        {
            var item = new ComboBoxItem
            {
                Content = displayContent,
                Tag = actualValue, // 使用Tag存储实际值
                IsSelected = isSelected
            };
            InternalComboBox.Items.Add(item);
        }

        /// <summary>
        /// 清空所有项
        /// </summary>
        public void ClearItems()
        {
            InternalComboBox.Items.Clear();
        }

        /// <summary>
        /// 设置选中项（通过内容）
        /// </summary>
        /// <param name="content">要选中的内容</param>
        public void SetSelectedByContent(object content)
        {
            foreach (ComboBoxItem item in InternalComboBox.Items)
            {
                if (item.Content?.Equals(content) == true)
                {
                    InternalComboBox.SelectedItem = item;
                    break;
                }
            }
        }
    }
}
