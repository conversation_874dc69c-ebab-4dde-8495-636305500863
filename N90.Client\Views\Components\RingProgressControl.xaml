<UserControl x:Class="N90.Client.Views.Components.RingProgressControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 环形进度样式 -->
        <Style x:Key="RingImageStyle" TargetType="Image">
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 数值文本样式 -->
        <Style x:Key="ValueTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="36"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <!-- 标签文本样式 -->
        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5,0,0"/>
        </Style>
    </UserControl.Resources>

    <Grid x:Name="RootGrid">
        <!-- 基础环形背景 -->
        <Image x:Name="BaseRing" 
               Source="/Resources/Images/ring_base.png" 
               Style="{StaticResource RingImageStyle}"/>

        <!-- 动态分段层 - 从segment_1到segment_10 -->
        <Image x:Name="Segment1" 
               Source="/Resources/Images/segment/segment_1.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment2" 
               Source="/Resources/Images/segment/segment_2.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment3" 
               Source="/Resources/Images/segment/segment_3.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment4" 
               Source="/Resources/Images/segment/segment_4.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment5" 
               Source="/Resources/Images/segment/segment_5.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment6" 
               Source="/Resources/Images/segment/segment_6.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment7" 
               Source="/Resources/Images/segment/segment_7.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment8" 
               Source="/Resources/Images/segment/segment_8.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment9" 
               Source="/Resources/Images/segment/segment_9.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>
        
        <Image x:Name="Segment10" 
               Source="/Resources/Images/segment/segment_10.png" 
               Style="{StaticResource RingImageStyle}"
               Visibility="Collapsed"/>

        <!-- 中心文本显示区域 -->
        <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="-50,20,0,0">
            <!-- 数值显示 -->
            <TextBlock x:Name="ValueText"
                       Text="{Binding Value, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       Style="{StaticResource ValueTextStyle}"/>

            <!-- 标签显示 -->
            <TextBlock x:Name="LabelText"
                       Text="{Binding Label, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       Style="{StaticResource LabelTextStyle}"/>
        </StackPanel>
    </Grid>
</UserControl>
