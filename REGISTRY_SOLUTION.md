# Windows服务注册表访问问题解决方案

## 问题描述

您的分析完全正确！这是一个典型的Windows服务权限问题：

### 问题根源
1. **Windows服务运行环境**：N90服务以系统账户（LocalSystem/NetworkService）运行
2. **注册表访问限制**：`Registry.CurrentUser` 指向当前登录用户的配置单元（HKEY_CURRENT_USER）
3. **权限冲突**：
   - 服务未登录时：无法访问用户的注册表配置单元
   - 服务登录时：访问的是系统账户的配置单元，不是实际用户的

## 解决方案

### 采用配置文件替代注册表

我们实现了一个新的 `DisplayPreferencesService`，使用JSON配置文件替代注册表：

#### 优势
- ✅ **无权限问题**：文件存储在 `%PROGRAMDATA%\N90\` 目录
- ✅ **服务和客户端共享**：两者都能读写同一个配置文件
- ✅ **自动迁移**：首次运行时自动从注册表迁移现有设置
- ✅ **更好的可维护性**：JSON格式易于调试和备份

#### 配置文件位置
```
%PROGRAMDATA%\N90\display_preferences.json
```

### 实现细节

#### 1. 新增服务类
- `DisplayPreferencesService.cs` - 配置文件管理
- `RegistryMigrationService.cs` - 注册表迁移工具

#### 2. 修改的文件
- `N90.Service\N90ServiceWorker.cs` - 使用配置服务
- `N90.Service\Program.cs` - 注册新服务
- `N90.Client\Services\HardwareDataManager.cs` - 使用配置服务

#### 3. 自动迁移流程
1. 首次启动时检查是否存在注册表设置
2. 如果存在且配置文件不存在，自动迁移
3. 迁移完成后，新设置保存到配置文件

### 配置文件示例

```json
{
  "Date": true,
  "Time": true,
  "Weekday": true,
  "CPUTemperature": true,
  "CPUUsage": false,
  "GPUTemperature": true,
  ...
}
```

## 测试验证

运行 `test_display_preferences.bat` 来验证：
- 配置文件位置是否正确
- 服务和客户端是否都能访问
- 迁移是否成功

## 部署说明

1. **重新编译**：包含新的配置服务
2. **重新安装服务**：使用现有的安装脚本
3. **自动迁移**：首次运行时自动完成
4. **向后兼容**：如果没有注册表设置，使用默认值

## 技术细节

### 权限模型
- **HKEY_CURRENT_USER**：用户特定，服务无法访问
- **%PROGRAMDATA%**：系统级目录，服务和用户都能访问
- **文件权限**：默认允许所有用户读写

### 同步机制
- 客户端修改设置时通过NamedPipe通知服务
- 服务收到通知后重新加载配置文件
- 确保两端设置同步

这个解决方案彻底解决了Windows服务无法访问用户注册表的问题，同时保持了现有功能的完整性。
