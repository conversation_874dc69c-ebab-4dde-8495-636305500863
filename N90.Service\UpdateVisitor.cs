using LibreHardwareMonitor.Hardware;

namespace N90.Service.Utils
{
    /// <summary>
    /// 简单实现的 LibreHardwareMonitor 访问器，用于递归更新硬件树。
    /// </summary>
    public class UpdateVisitor : IVisitor
    {
        public void VisitComputer(IComputer computer)
        {
            computer.Traverse(this);
        }

        public void VisitHardware(IHardware hardware)
        {
            hardware.Update();
            foreach (IHardware subHardware in hardware.SubHardware)
            {
                subHardware.Accept(this);
            }
        }

        public void VisitSensor(ISensor sensor) { /* 不处理传感器 */ }

        public void VisitParameter(IParameter parameter) { /* 不处理参数 */ }
    }
}
